﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.classes
{
    public class MonitoringDataProcessorContext
    {
        public bool GenerateReport { get; set; } = false;
        public bool Persist { get; set; } = false;
        public string? UploadUri { get; set; }
        public bool ProcessFiles { get; set; } = false;

    }
}
