﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.classes.Notifications
{
    public class NotificationsRequest
    {
        public List<HTNotification>? Notifications { get; set; }
        public List<NotificationTemplate> Templates { get; set; }
    }


    public class HTNotification
    {
        public int NotificationId { get; set; }
        public string ExNotificationId { get; set; }
        public DateTime DateCreatedUtc { get; set; }
        public string Tag { get; set; }
        public bool IsActive { get; set; }
        public string Name { get; set; }
        public string NoficationType { get; set; }
        public string Status { get; set; }
        public string ExEntityId { get; set; }
        public string EntityType { get; set; }
        public DateTime EntityDateUtc { get; set; }
        public DateTime EntityDateLocal { get; set; }
        public string ExRecipientId { get; set; }
        public string RecipientName { get; set; }
        public string Message { get; set; }
        public string DeliveryMethod { get; set; }
        public string DeliveryDestination { get; set; }
        public int? RetryCount { get; set; }
        public DateTime? NotificationSentUtc { get; set; }
        public DateTime? NotificationReceivedUtc { get; set; }
        public string NotificationResult { get; set; }

        public string NotificationMinutes { get; set; }

    }

}
