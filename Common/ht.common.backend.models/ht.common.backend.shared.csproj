﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="models\int\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Communication.CallingServer" Version="1.0.0-beta.3" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.2.2" />
    <PackageReference Include="Azure.Identity" Version="1.10.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
    <PackageReference Include="GemBox.Document" Version="35.0.1488-hotfix" />

    <PackageReference Include="ht.data.common" Version="1.0.2-250610141501" />

    <PackageReference Include="libphonenumber-csharp" Version="8.13.23" />

    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.2.0" />

    <PackageReference Include="Microsoft.Extensions.ApiDescription.Client" Version="7.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.24" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.0" />
    <PackageReference Include="PeterPiper" Version="1.0.5" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.32.3" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
  </ItemGroup>

</Project>
