﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared
{
    public class ApiException
    {
        public string Message { get; set; }
        public string Status { get; set; }
        public string ErrorCode { get; set; }
        public string StackTrace { get; set; }

        public ApiException()
        {

        }

        public ApiException(Exception ex)
        {
            this.Message = ex.Message;
            this.Status = "Error";
            this.StackTrace = ex.StackTrace;

        }
    }
}
