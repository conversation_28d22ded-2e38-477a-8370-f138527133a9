﻿using Microsoft.AspNetCore.StaticFiles;
using System;
using System.Collections.Generic;
using System.Text;

namespace ht.common.backend.shared
{
    public class Globals
    {
        public static Dictionary<string,string> Properties { get; set; } = new Dictionary<string, string>();
        public static string HT_KV_URI { get;set;  }

        public static string GetMimeType(string filename)
        {
            var prov = new FileExtensionContentTypeProvider();
            string ct = string.Empty;
            if (!prov.TryGetContentType(filename, out ct))
                ct = "application/octet-stream";
            return (ct);
        }


    }
}
