﻿using Microsoft.Extensions.Logging;
using Microsoft.WindowsAzure.Storage.Blob;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace ht.common.backend.shared.helpers;

public class BlobHelper
{
    private string _key;
    private string _acctName;
    private ILogger _log;
    private CloudBlobClient _client;
    private Microsoft.WindowsAzure.Storage.Auth.StorageCredentials _acctStorageCreds;

    public static string AcsAccessKey { get; set; }
    public static string AcsUrl { get; set; }

    public static string acctName;
    public static string acctKey;


    public BlobHelper()
    {
        this._key = acctKey;
        this._acctName = acctName;
        _acctStorageCreds = new Microsoft.WindowsAzure.Storage.Auth.StorageCredentials(_acctName, _key);
    }
    public BlobHelper(string acctName, string key, ILogger log = null)
    {
        try
        {
            this._key = key;
            this._acctName = acctName;
            this._log = log;
            _acctStorageCreds = new Microsoft.WindowsAzure.Storage.Auth.StorageCredentials(_acctName, _key);
        }
        catch (Exception ex)
        {
            log?.LogError(ex, "ERROR BlobHelper");
        }
    }

    public bool CheckIfBlobExists(string fullBlobUrl)
    {
        var uri = new Uri(fullBlobUrl);
        var bExists= (new CloudBlobClient(uri, _acctStorageCreds))?.GetBlobReferenceFromServerAsync(uri).GetAwaiter().GetResult().ExistsAsync().GetAwaiter().GetResult();
        return bExists.GetValueOrDefault(false);
        
    }

    public bool CheckIfContainerExists(string rootUrl, string parentContainer, string cName)
    {
        var client = new CloudBlobClient(new Uri(rootUrl), _acctStorageCreds);
        var root = client.GetRootContainerReference();

        _log.LogInformation(client.GetRootContainerReference().ToString());
        return client.GetContainerReference(cName).CreateIfNotExistsAsync().GetAwaiter().GetResult();
    }

    public async Task<bool> SaveBlob(string url, string sasToken, byte[] bytes, Dictionary<string, string> metadata)
    {
        using (var dstClient = new HttpClient())
        {
            var uri = new Uri(url);
            var dstBlobUrl = url + (!string.IsNullOrEmpty(sasToken) ? $"?{sasToken}" : "");
            var srcContentType = Globals.GetMimeType(System.IO.Path.GetFileName(uri.AbsolutePath));
            using (var dstReq = new HttpRequestMessage(HttpMethod.Put, dstBlobUrl))
            {
                dstReq.Headers.Add("x-ms-blob-container-type", srcContentType);
                dstReq.Headers.Add("x-ms-blob-type", "BlockBlob");
                dstReq.Headers.Add("x-ms-version", "2020-02-10");

                //Add parameters as meta data
                if (metadata?.Count > 0)
                {
                    foreach (var p in metadata)
                        dstReq.Headers.Add($"x-ms-meta-{p.Key}", p.Value);
                }
                var sc = new ByteArrayContent(bytes, 0, bytes.Length);

                sc.Headers.Remove("Content-Type");
                sc.Headers.Add("Content-Type", "application/octet-stream");
                dstReq.Content = sc;

                var dstResp = await dstClient.SendAsync(dstReq);
                dstResp.EnsureSuccessStatusCode();
            }
        }
        return true;
    }

    public async Task<byte[]> GetBlob(string url)
    {
        var originalUrl = url;
        var uri = new Uri(url);
        var blobClient = new CloudBlobClient(uri, _acctStorageCreds);
        var bRef = await blobClient.GetBlobReferenceFromServerAsync(uri);
        await bRef.FetchAttributesAsync();
        var ms = new MemoryStream();

        await bRef.DownloadToStreamAsync(ms);
        return (ms.ToArray());
    }

    public async Task<IDictionary<string, string>> GetBlobMetadata(string url)
    {
        var props = new Dictionary<string, string>();
        var originalUrl = url;
        var uri = new Uri(url);
        var blobClient = new CloudBlobClient(uri, _acctStorageCreds);
        
        var bRef = await blobClient.GetBlobReferenceFromServerAsync(uri);
        await bRef.FetchAttributesAsync();

        if (bRef.Metadata?.Count>0)
        {
            foreach (var p in bRef.Metadata)
            {
                props[p.Key.ToLower()] = p.Value;
            }
        }
        return (props);
    }

    public async Task SetBlobMetadata(string url, Dictionary<string, string> props, bool bOverwrite = false)
    {
        if (!string.IsNullOrEmpty(url))
        {
            _log?.LogInformation($"SaveBlobMetadata Url={url} Props={props?.Count}");
            props ??= new Dictionary<string, string>();
            var originalUrl = url;
            var uri = new Uri(url);
            var blobClient = new CloudBlobClient(uri, _acctStorageCreds);
            var bRef = await blobClient.GetBlobReferenceFromServerAsync(uri);
            await bRef.FetchAttributesAsync();
            if (bOverwrite)
                bRef.Metadata?.Clear();

            foreach (var kv in props)
            {
                if (!string.IsNullOrEmpty(kv.Key))
                    bRef.Metadata[kv.Key] = kv.Value;
            }
            await bRef.SetMetadataAsync();
        }
        else
        {
            _log?.LogError("ERROR: SetBlobMetadata was passed a null URL");
        }

    }
    public async Task<string> GetSasToken(string url, int minsValidFor)
    {
        var uri = new Uri(url);
        var client = new CloudBlobClient(uri, _acctStorageCreds);
        var bRef = await client.GetBlobReferenceFromServerAsync(uri);
        var res = bRef.GetSharedAccessSignature(new SharedAccessBlobPolicy
        {
            SharedAccessExpiryTime = DateTime.UtcNow.AddMinutes(minsValidFor),
            Permissions = SharedAccessBlobPermissions.Read
        });

        return (res?.Replace("?", ""));
    }


    public async Task<string> GetSasTokenfor2Years(string url, int yearsValidFor)
    {
        var uri = new Uri(url);
        var client = new CloudBlobClient(uri, _acctStorageCreds);
        var bRef = await client.GetBlobReferenceFromServerAsync(uri);
        var res = bRef.GetSharedAccessSignature(new SharedAccessBlobPolicy
        {
            SharedAccessExpiryTime = DateTime.UtcNow.AddYears(yearsValidFor),
            Permissions = SharedAccessBlobPermissions.Read
        });

        return (res?.Replace("?", ""));  
    }


}

