﻿
using GemBox.Document;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.helpers
{
    public class RTFHelper
    {
        public static string Lic = "DN-2022Apr25-vBukBWLH2kUR0fYZpMmHgyRgz/bOjbSxacxUUxcg/yigwwASdxVI8QWZsxysiTL4MCmi9ww9yroVHEn3awQX6Xivs2w==A";

        private ILogger log;

        static RTFHelper()
        {
            ComponentInfo.SetLicense(Lic);
        }
        public RTFHelper(ILogger _log)
        {
            log = _log;
        }

        public static RTFHelper GetInstance(ILogger _log)
        {
            return new RTFHelper(_log);
        }

        /// <summary>
        /// Convert a HTML string to RTF
        /// </summary>
        /// <param name="htmlString"></param>
        /// <returns></returns>
        public string ConvertHTMLtoRTF(string htmlString)
        {
            string rtfString = "";

            using (MemoryStream stream = new MemoryStream(Encoding.UTF8.GetBytes(htmlString)))
            {
                // Load input HTML stream
                DocumentModel document = DocumentModel.Load(stream, LoadOptions.HtmlDefault);

                // When reading any HTML content a single Section element is created.
                // We can use that Section element to specify various page options.
                //Section section = document.Sections[0];
                //PageSetup pageSetup = section.PageSetup;
                //PageMargins pageMargins = pageSetup.PageMargins;
                //pageMargins.Top = pageMargins.Bottom = pageMargins.Left = pageMargins.Right = 0;

                // output the document as RTF
                using (MemoryStream rtfStream = new MemoryStream())
                {
                    document.Save(rtfStream, SaveOptions.RtfDefault);
                    rtfStream.Position = 0;
                    using (StreamReader reader = new StreamReader(rtfStream))
                    {
                        rtfString = reader.ReadToEnd();
                    }
                }
            }
            return (rtfString);
        }

        /// <summary>
        /// Convert a HTML string to RTF
        /// </summary>
        /// <param name="htmlString"></param>
        /// <returns></returns>
        public string ConvertRTFtoHTML(string rtfString)
        {
            string htmlString = "";

            using (MemoryStream stream = new MemoryStream(Encoding.UTF8.GetBytes(rtfString)))
            {
                // Load input RTF stream
                DocumentModel document = DocumentModel.Load(stream, LoadOptions.RtfDefault);

                // output the document as RTF
                using (MemoryStream htmlStream = new MemoryStream())
                {
                    document.Save(htmlStream, SaveOptions.HtmlDefault);
                    htmlStream.Position = 0;
                    using (StreamReader reader = new StreamReader(htmlStream))
                    {
                        htmlString = reader.ReadToEnd();
                    }
                }
            }
            return (htmlString);
        }
    }
}
