﻿using Azure.Core;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Cryptography.X509Certificates;

namespace ht.common.backend.shared.helpers
{
    public class AzureKeyvaultHelper
    {

        public static string KeyVaultUrl;
        public static string KeyPrefix;
        public static string ClientId;
        public static string ClientSecret;
        public static string TenantId;
        public static string ImportFlagFiledName;
        public static string ImportFlagFieldValue;
        public static string Backend_URL;
        /*
        public AzureKeyvaultHelper(string tenantId, string clientId, string clientSecret)
        {
            _clientId = clientId;
            _clientSecret = clientSecret;
            _tenantId = tenantId;
        }
        */

        public static X509Certificate2 GetCertificate(string certName)
        {
            var creds = GetCreds();
            var client = new SecretClient(new Uri(KeyVaultUrl), creds);

            KeyVaultSecret secret = client.GetSecret(certName);
            byte[] certificate = Convert.FromBase64String(secret.Value);

            X509Certificate2 x509 = new X509Certificate2(certificate);

            return x509;
        }

        public static Dictionary<string, string> GetSecrets()
        {
            var prefix = KeyPrefix?.ToLower();
            return GetSecrets(prefix);
        }

        public static Dictionary<string, string> GetSecrets(string prefix)
        {
            var creds = GetCreds();
            var r = new Dictionary<string, string>();
            try
            {
                var clnt = new SecretClient(new Uri(KeyVaultUrl), creds);
                var pageList = clnt.GetPropertiesOfSecrets().AsPages().ToList();
                foreach (var p in pageList)
                {
                    foreach (var v in p.Values)
                    {
                        if (v.Name.ToLower().StartsWith(prefix))
                        {
                            var n = v.Name.Replace($"{prefix}-", "").Replace(prefix, "").Replace("--", "");
                            r[n] = clnt.GetSecret(v.Name)?.Value?.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.Print(ex.ToString());
            }
            return r;
        }

        public static TokenCredential GetCreds()
        {
            if (!AzureKeyvaultHelper.IsInAzure)
                return new ClientSecretCredential(TenantId, ClientId, ClientSecret);

            else
                return new DefaultAzureCredential();
        }

        public static bool IsInAzure
        {
            get
            {
                var s = System.Environment.GetEnvironmentVariable("WEBSITE_HOSTNAME");
                return (!string.IsNullOrEmpty(s) && !s.Contains("localhost"));
            }
        }

        public static bool SetSecret(string secretName, string secretValue)
        {
            var creds = GetCreds();
            var client = new SecretClient(new Uri(KeyVaultUrl), creds);
            try
            {
                var result = client.SetSecret(secretName, secretValue);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
