﻿using ht.common.backend.shared.models.security;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using ht.data.common;
namespace ht.common.backend.shared.helpers;
public class TokenService
{
    public string _signingKey { get; set; }
    public SymmetricSecurityKey _secretKey { get; set; }

    public long _secsToLive = 1800;

    public static string _rsaPublicKey { get; set; }

    private static byte[] _rsaPrivKey { get; set; }
    private static byte[] _rsaPubKey { get; set; }

    private static RSA _rsaPublic { get; set; }
    private static RSA _rsaPrivate { get; set; }

    public static string _issuer { get; set; }

    public static void Init(string issuer, string pubKey64enc, string privKey64enc)
    {
        _issuer = issuer;

        _rsaPublicKey = System.Text.UTF8Encoding.UTF8.GetString(Convert.FromBase64String(pubKey64enc)).Replace("-----BEGIN PUBLIC KEY-----\n", "").Replace("-----END PUBLIC KEY-----", "");
        var privKey64 = System.Text.UTF8Encoding.UTF8.GetString(Convert.FromBase64String(privKey64enc)).Replace("-----BEGIN RSA PRIVATE KEY-----\n", "").Replace("-----END RSA PRIVATE KEY-----", "");

        _rsaPrivKey = Convert.FromBase64String(privKey64);
        _rsaPubKey = Convert.FromBase64String(_rsaPublicKey);

        _rsaPublic = RSA.Create();
        _rsaPublic.ImportSubjectPublicKeyInfo(_rsaPubKey, out _);

        _rsaPrivate = RSA.Create();
        _rsaPrivate.ImportRSAPrivateKey(_rsaPrivKey, out _);


    }

    public TokenService(string key = "")
    {
        if (key != null)
        {
            _signingKey = key;
            _secretKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(key));
        }
    }
    public TokenService(string key, long secsToLive = 1800)
    {
        _signingKey = key;
        _secretKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(key));
        _secsToLive = secsToLive;

    }

    private SigningCredentials getRSASigningCreds(bool bJustPublic = false)
    {
        var _rsa = (bJustPublic) ? _rsaPublic : _rsaPrivate;
        var _signCreds = new SigningCredentials(new RsaSecurityKey(_rsa), SecurityAlgorithms.RsaSha256)
        {
            CryptoProviderFactory = new CryptoProviderFactory { CacheSignatureProviders = false }
        };
        return (_signCreds);
    }



    public ClaimsPrincipal ValidateAppToken(string tok, string audience = "")
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokParams = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = false,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = _issuer,
            ValidAudience = audience,
            IssuerSigningKey = new RsaSecurityKey(_rsaPublic),

            CryptoProviderFactory = new CryptoProviderFactory()
            {
                CacheSignatureProviders = false
            }
        };

        try
        {
            var handler = new JwtSecurityTokenHandler();
            return handler.ValidateToken(tok, tokParams, out var valTok);

        }
        catch (Exception ex)
        {
            return null;
        }
    }

    public ClaimsPrincipal ValidateAADToken(string tok, string issuer, string audience = "")
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokParams = new TokenValidationParameters
        {
            ValidateIssuer = false,
            ValidateAudience = true,
            ValidateLifetime = false,
            ValidateIssuerSigningKey = false,
            ValidIssuer = issuer,
            ValidAudience = audience
            //IssuerSigningKey = new RsaSecurityKey(_rsaPublic),

            //CryptoProviderFactory = new CryptoProviderFactory()
            //{
            //	CacheSignatureProviders = false
            //}
        };

        try
        {
            var handler = new JwtSecurityTokenHandler();
            return handler.ValidateToken(tok, tokParams, out var valTok);

        }
        catch (Exception ex)
        {
            return null;
        }
    }

    public JwtSecurityToken DeserialiseToken(string tok)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var jwt = tokenHandler.ReadJwtToken(tok);
        return (jwt);
    }



    public async Task<customJWTToken> ReturnNewSecurityToken(string idName, string audience, string tokenType, List<string> roles, List<Claim> customClaims = null)
    {

        customJWTToken res;

        try
        {
            var dt = DateTime.Now;
            var expDt = DateTime.UtcNow.AddSeconds(_secsToLive);
            var unixTimeSeconds = new DateTimeOffset(dt).ToUnixTimeSeconds();


            res = new customJWTToken
            {
                tokenType = tokenType,
                issuedUtc_at = dt,
                expiresUtc_at = expDt,
                expires_in = (long)expDt.Subtract(dt).TotalSeconds
            };

            List<Claim> claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Iat,unixTimeSeconds.ToString(),ClaimValueTypes.Integer64),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.NameIdentifier, idName)
            };
            if (customClaims != null)
            {
                claims.AddRange(customClaims);
            }

            if (roles != null)
            {
                foreach (var r in roles)
                    claims.Add(new Claim(ClaimTypes.Role, r));
            };

            var token = new JwtSecurityToken(
                    issuer: _issuer,
                    audience: audience,
                    claims: claims.ToArray(),
                    expires: expDt,
                    notBefore: dt,
                    signingCredentials: getRSASigningCreds()
                );

            res.access_token = new JwtSecurityTokenHandler().WriteToken(token);
            return (res);
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<customJWTToken> ReturnGuestUserSecurityToken(string idName, string audience, string tokenType, List<string> roles, string userRoles, DateTime taskDateTimeUtc, List<Claim> customClaims = null)
    {

        customJWTToken res;
        //  AuthUserResponse tokenresponse = new();
        try
        {
            var dt = taskDateTimeUtc;
            var expDt = taskDateTimeUtc;
            if (taskDateTimeUtc > DateTime.UtcNow)
            {
                if ((int)taskDateTimeUtc.Subtract(DateTime.UtcNow).TotalMinutes > 10)
                {
                    dt = taskDateTimeUtc.AddMinutes(-10);
                }
                else
                {
                    dt = taskDateTimeUtc;
                }

                expDt = taskDateTimeUtc.AddHours(12);
            }
            else
            {
                dt = DateTime.UtcNow;
                expDt = DateTime.UtcNow.AddHours(12);
            }

            var unixTimeSeconds = new DateTimeOffset(dt).ToUnixTimeSeconds();

            res = new customJWTToken
            {
                tokenType = tokenType,
                issuedUtc_at = dt,
                expiresUtc_at = expDt,
                expires_in = (long)expDt.Subtract(dt).TotalSeconds
            };

            List<Claim> claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Iat,unixTimeSeconds.ToString(),ClaimValueTypes.Integer64),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.NameIdentifier, idName)
            };
            if (customClaims != null)
            {
                claims.AddRange(customClaims);
            }

            if (roles != null)
            {
                foreach (var r in roles)
                    claims.Add(new Claim(ClaimTypes.Role, r));
            };
            //if(!string.IsNullOrEmpty(userRoles))
            //{
            //             claims.Add(new Claim("ht_roles", Convert.ToBase64String(Encoding.UTF8.GetBytes(Convert.ToString(userRoles)))));
            //         }

            var subject = new ClaimsIdentity(new[]
                   {
                        new Claim("ht_roles", Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(Convert.ToString(userRoles)))),

                    });

            //var token = new JwtSecurityToken(
            //        issuer: _issuer,
            //        audience: audience,
            //        claims: claims.ToArray(),
            //        expires: expDt,
            //        notBefore: dt,
            //        signingCredentials: getRSASigningCreds()
            //    );

            //res.access_token = new JwtSecurityTokenHandler().WriteToken(token);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = subject,
                Expires = expDt,
                Issuer = _issuer,
                Audience = audience,
                SigningCredentials = getRSASigningCreds()
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            var jwtToken = tokenHandler.WriteToken(token);
            res.access_token = jwtToken;


            return (res);

        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public string ReturnNewSecurityToken(string tok)
    {
        var tokenHandler = new JwtSecurityTokenHandler();

        try
        {
            List<Claim> claims = new List<Claim>();
            //claims.AddRange(jwt.Claims);
            claims.Add(new Claim(ClaimTypes.Role, "Doctor"));


            var token = new JwtSecurityToken(
                    issuer: "https://localhost:5001", //jwt.Issuer,
                    audience: "https://apisbffuat.healthteams.com.au",//_audience,
                    claims: claims,
                    expires: DateTime.Now.AddHours(1)
                );


            return (tokenHandler.WriteToken(token));
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public async Task<customJWTToken> ReturnResidentDirectLinkGuestUserSecurityToken(string idName, string audience, string tokenType, List<string> roles, string userRoles, int hours, List<Claim> customClaims = null)
    {

        customJWTToken res;
        //  AuthUserResponse tokenresponse = new();
        try
        {
            var dt = DateTime.UtcNow;
            var expDt = DateTime.UtcNow.AddHours(hours);


            var unixTimeSeconds = new DateTimeOffset(dt).ToUnixTimeSeconds();

            res = new customJWTToken
            {
                tokenType = tokenType,
                issuedUtc_at = dt,
                expiresUtc_at = expDt,
                expires_in = (long)expDt.Subtract(dt).TotalSeconds
            };

            List<Claim> claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Iat,unixTimeSeconds.ToString(),ClaimValueTypes.Integer64),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(ClaimTypes.NameIdentifier, idName)
        };
            if (customClaims != null)
            {
                claims.AddRange(customClaims);
            }

            if (roles != null)
            {
                foreach (var r in roles)
                    claims.Add(new Claim(ClaimTypes.Role, r));
            };
            //if(!string.IsNullOrEmpty(userRoles))
            //{
            //             claims.Add(new Claim("ht_roles", Convert.ToBase64String(Encoding.UTF8.GetBytes(Convert.ToString(userRoles)))));
            //         }

            var subject = new ClaimsIdentity(new[]
                   {
                    new Claim("ht_roles", Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(Convert.ToString(userRoles)))),

                });

            //var token = new JwtSecurityToken(
            //        issuer: _issuer,
            //        audience: audience,
            //        claims: claims.ToArray(),
            //        expires: expDt,
            //        notBefore: dt,
            //        signingCredentials: getRSASigningCreds()
            //    );

            //res.access_token = new JwtSecurityTokenHandler().WriteToken(token);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = subject,
                Expires = expDt,
                Issuer = _issuer,
                Audience = audience,
                SigningCredentials = getRSASigningCreds()
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            var jwtToken = tokenHandler.WriteToken(token);
            res.access_token = jwtToken;


            return (res);

        }
        catch (Exception ex)
        {
            throw;
        }
    }
}

