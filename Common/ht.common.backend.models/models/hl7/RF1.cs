﻿using ht.data.common.Billing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    public class RF1
    {
        public string SegmentType { get; set; } = "RF1"; // Always "RF1"
        public string ReferralPriority { get; set; }
        public ReferralType ReferralType { get; set; }
        public string ReferralDisposition { get; set; }
        public string ReferralReason { get; set; }
        public (string identifier, string sendingSytem) OriginatingReferralIdentifier { get; set; }
        public string ExternalReferralIdentifier { get; set; }
        public string ReferralStatus { get; set; }
        public string ReferralActionCode { get; set; }
        public string EffectiveDate  { get; set; }
        public string ReferralExpirationDateTime { get; set; }
        // Add more fields as needed for your specific implementation

        public override string ToString()
        {
            return $"RF1|{ReferralPriority}|{ReferralType}|{ReferralReason}|{ExternalReferralIdentifier}|{ReferralStatus}|{ReferralActionCode}|{EffectiveDate}|{ReferralExpirationDateTime}|";
        }
    }
    public class ReferralType
    {
        public string ReferralTypeCode { get; set; }
        public string ReferralTypeDescription { get; set; }
        public string ReferralTypeCodingSystem { get; set; }
    }
        public enum ReferralStatus
    {
        P, // Pending
        A, // Accepted
        R, // Rejected
        E  // Expired
    }

    public enum ReferralPriority
    {
        R, // Routine
        S, // STAT – Critical/Urgent
        A  // As soon as possible
    }

}
