﻿using ht.data.common.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    public class REFI12_2_3_1 : HL7Message
    {
        /*
        
        MSH|^~\&|Sample test hospital|TEST|||20191105154020||REF^I12|178038310|P|2.3.1
        PID|1||179790^^^test||infection3^Test||19881128|F|||29 Mousehole Cresent^^Yanchep^WA^6035|||
        PV1|1|O|29677|||||873250^Cailes^Jeremy^^^^^^test|muddu^Aung^Htun^TEST^^^^^
        OBR|1|0175671960^test|0175671960^test|test123 Letter^Letter^testletter||20180725|20180719||20180725|||||||^Cail<PERSON>^Jeremy^^^^^^testletter||||||||||||sunil^<PERSON><PERSON><PERSON>^Suma^(testpartner)^^^^^test~X0012622^Hess^Sally^(OPA)^^^^^test~I96766753^Doctor 1^Mail^^^^^^test~X20180713013100^Doctor 2^Mail^Business Name^^^^^test~FAX356^Doctor 3^Fax^(FAX)^^^^^test~||||||||20180719
        OBX|1|FT|OLETTER^^test||Sample test hospital||||||F
  
        */
        public REFI12_2_3_1() : base("2.3.1", "REF", "I12", "MSH,RF1,PRD,PID,OBR,OBX")
        {

            var rf1 = GetHL7Segment("RF1");
            var prd = GetHL7Segment("PRD");
            var pid = GetHL7Segment("PID");
            var obr = GetHL7Segment("OBR");
            var obx = GetHL7Segment("OBX");
            var msh = GetHL7Segment("MSH");

            //set MSH
            GenerateMSH(new MSH()
            {
                SendingApplication = "Health Teams v1",
                SendingFacility = "healthte",
                ReceivingApplication = "HealthLink",
                ReceivingFacility = "pms3medd",

            });

            //RF1

            GenerateRF1(new RF1()
            {
                ReferralStatus = ReferralStatus.P.ToString(),
                ReferralPriority = ReferralPriority.R.ToString(),
                ReferralType = new()
                {
                    ReferralTypeCode = "GRF",
                    ReferralTypeDescription = "General referral",
                    ReferralTypeCodingSystem = "HL70281"
                },
                ReferralDisposition = "UHR",
                OriginatingReferralIdentifier = (identifier: GetFieldValue("MSH", 10), sendingSytem: GetFieldValue("MSH", 3)),
                EffectiveDate = GetFieldValue("MSH", 7)
            });

            //PRD
            GeneratePRD(new PRD()
            {
                //@"Doctor^Demo^^^Dr",
                ProviderRole = ProviderRole.RP,
                ProviderName = new()
                {
                    FamilyNameLastNamePrefix = "Doctor",
                    GivenName = "Demo",
                    Prefix = "Dr"
                },
                ProviderIdentifiers = (ID: "0000000Y", IdType: "PROV#")
            });

            //PID
            GeneratePID(new PID()
            {
                SetID = "1",
                //DateTimeOfBirth = new DateTime(1981, 1, 30),
                Sex = "M",
                PatientIdentifierList = new()
                {
                   new PatientIdentifier()
                   {
                       ID="**********",
                       Authority="AUSHIC",
                       IdentifierTypeCode="MC"

                   }
                },
                PatientNameList = new()
                {
                    new PatientName()
                    {
                        LastName="Mouse",
                        FirstName="Mickey",
                        MiddleName="Middle",
                        Prefix="Sir"

                    }
                },
                PatientAddressList = new()
                {
                    new()
                    {
                        StreetAddress="1 Testing Street",
                        City="Woonona",
                        State="NSW",
                        PostalCode="2517"
                    }
                },


                PhoneNumberList = new()
                {
                    new()
                    {
                        TelecommunicationUseCode="PRN",
                        TelecommunicationEquipmentType="CP",
                        AreaCityCode="04",
                        TelephoneNumber="88888888"
                    }
                }




            });

            //obr
            GenerateOBR(new OBR()
            {
                SetID = "1",
                FillerOrderNumber = new()
                {
                    EntityIdentifier = "1234",
                    NameSpaceId = "HealthTeams",
                    UniversalId = "Healthteams"
                },
                UniversalServiceIDInfo = new()
                {
                    Text = "Letter - Referral Report"
                },
            //    ObservationDateTime = DateTime.Now,
                OrderingProviders = new()
                {
                    new OBR.Provider()
                    {
                        ID="0000000Y",
                        LastName="Doctor",
                        FirstName="Demo",
                        Prefix="Dr",
                        Authority="AUSHICPR"
                    }
                },
                DiagnosticServiceSectorID = "PHY",
                ResultStatusID = "F"

            });

            //obx
            GenerateOBX(new OBX()
            {
                SetID = "1",
                ValueType = "ED",
                ObservationIdentifier = new()
                {
                    Identifier = "PDF",
                    Text = "Display format in PDF",
                    NameOfCodingSystem = "AUSPDI"
                },
                ObservationValue = ""
            });
        }



        public void GenerateMSH(MSH mSH)
        {
            SetField("MSH", 3, mSH.SendingApplication);
            SetField("MSH", 4, mSH.SendingFacility);
            SetField("MSH", 5, mSH.ReceivingApplication);
            SetField("MSH", 6, mSH.ReceivingFacility);

            AddComponent("MSH", 12, 1, "2.3.1");
            AddComponent("MSH", 12, 2, "AUS");
            SetField("MSH", 15, "NE");
            SetField("MSH", 16, "AL");

        }

        public void GenerateRF1(RF1 rf1)
        {
            SetField("RF1", 1, rf1.ReferralStatus);
            SetField("RF1", 2, rf1.ReferralPriority);

            AddComponent("RF1", 3, 1, rf1.ReferralType.ReferralTypeCode);
            AddComponent("RF1", 3, 2, rf1.ReferralType.ReferralTypeDescription);
            AddComponent("RF1", 3, 3, rf1.ReferralType.ReferralTypeCodingSystem);

            SetField("RF1", 4, rf1.ReferralDisposition);

            AddComponent("RF1", 6, 1, rf1.OriginatingReferralIdentifier.identifier);
            AddComponent("RF1", 6, 2, rf1.OriginatingReferralIdentifier.sendingSytem);

            SetField("RF1", 9, rf1.EffectiveDate);


        }

        public void GeneratePRD(PRD prd)
        {
            SetField("PRD", 1, prd.ProviderRole.ToString());

            AddComponent("PRD", 2, 1, prd?.ProviderName.FamilyNameLastNamePrefix);
            AddComponent("PRD", 2, 2, prd?.ProviderName.GivenName);
            AddComponent("PRD", 2, 5, prd?.ProviderName.Prefix);

            SetField("PRD", 3, prd?.ProviderAddress ?? "");
            SetField("PRD", 4, prd?.ProviderLocation ?? "");
            SetField("PRD", 5, prd?.ProviderCommunicationInformation ?? "");
            SetField("PRD", 6, prd?.PreferredMethodofContact ?? "");

            AddComponent("PRD", 7, 1, prd?.ProviderIdentifiers.ID);
            AddComponent("PRD", 7, 2, prd?.ProviderIdentifiers.IdType);

            SetField("PRD", 8, prd?.EffectiveStartDate ?? "");
            SetField("PRD", 9, prd?.EffectiveEndDate ?? "");
        }

        public void GeneratePID(PID pid)
        {
            SetField("PID", 1, pid.SetID);

            pid.PatientIdentifierList.ForEach(identifier =>
            {
                AddComponent("PID", field: 3, component: 1, identifier.ID);
                AddComponent("PID", field: 3, component: 4, identifier.Authority);
                AddComponent("PID", field: 3, component: 5, identifier.IdentifierTypeCode);
            });
            pid.PatientNameList.ForEach(name =>
            {
                AddComponent("PID", field: 5, component: 1, name.LastName);
                AddComponent("PID", field: 5, component: 2, name.FirstName);
                AddComponent("PID", field: 5, component: 3, name.MiddleName ?? "");
                AddComponent("PID", field: 5, component: 5, name.Prefix ?? "");
            });
            pid.PatientAddressList.ForEach(address =>
            {
                AddComponent("PID", field: 11, component: 1, address.StreetAddress);
                AddComponent("PID", field: 11, component: 3, address.City);
                AddComponent("PID", field: 11, component: 4, address.State);
                AddComponent("PID", field: 11, component: 5, address.PostalCode);
            });
            pid.PhoneNumberList.ForEach(number =>
            {
                AddComponent("PID", field: 13, component: 2, number.TelecommunicationUseCode);
                AddComponent("PID", field: 13, component: 3, number.TelecommunicationEquipmentType);
                AddComponent("PID", field: 13, component: 6, number.AreaCityCode);
                AddComponent("PID", field: 13, component: 7, number.TelephoneNumber);
            });

            SetField("PID", 7, pid.DateTimeOfBirth.Value.ToString("yyyyMMdd"));
            SetField("PID", 8, pid.Sex);

        }

        public void GenerateOBR(OBR obr)
        {
            SetField("OBR", 1, obr.SetID);
            AddComponent("OBR", field: 3, component: 1, obr.FillerOrderNumber?.EntityIdentifier);
            AddComponent("OBR", field: 3, component: 2, obr.FillerOrderNumber?.NameSpaceId);
            AddComponent("OBR", field: 3, component: 3, obr.FillerOrderNumber?.UniversalId);
            AddComponent("OBR", field: 4, component: 2, obr.UniversalServiceIDInfo.Text);
          //  SetField("OBR", 7, $"{obr.ObservationDateTime.Value.ToString("yyyyMMddHHmmss")}+1000");
            SetField("OBR", 6, $"{DateTime.Now.ToString("yyyyMMddHHmmss")}+1000");


            obr.OrderingProviders.ForEach(provider =>
            {
                AddComponent("OBR", field: 16, component: 1, provider.ID);
                AddComponent("OBR", field: 16, component: 2, provider.LastName);
                AddComponent("OBR", field: 16, component: 3, provider.FirstName);
                AddComponent("OBR", field: 16, component: 6, provider.Prefix);
                AddComponent("OBR", field: 16, component: 9, provider.Authority);
            });

            SetField("OBR", 24, obr.DiagnosticServiceSectorID);
            SetField("OBR", 25, obr.ResultStatusID);

            AddComponent("OBR", 27, 4, $"{GetFieldValue("OBR", 6)}");
        }

        public void GenerateOBX(OBX obx)
        {
            SetField("OBX", 1, obx.SetID);
            SetField("OBX", 2, obx.ValueType);
            AddComponent("OBX", 3, 1, obx.ObservationIdentifier.Identifier);
            AddComponent("OBX", 3, 2, obx.ObservationIdentifier.Text);
            AddComponent("OBX", 3, 3, obx.ObservationIdentifier.NameOfCodingSystem);

            AddComponent("OBX", 5, 2, "TX");
            AddComponent("OBX", 5, 3, "PDF");
            AddComponent("OBX", 5, 4, "Base64");
            AddComponent("OBX", 5, 5, obx.ObservationValue);
            SetField("OBX", 14, $"{GetFieldValue("OBR", 6)}");

        }


    }


}
