﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Intrinsics.X86;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    public class PID
    {
        public string SetID { get; set; } // PID-1
        public string PatientID { get; set; } // PID-2
        public List<PatientIdentifier> PatientIdentifierList { get; set; } // PID-3
        public List<PatientName> PatientNameList { get; set; } // PID-5
        public DateTime? DateTimeOfBirth { get; set; } // PID-7
        public string Sex { get; set; } // PID-8
        public string Race { get; set; } // PID-10
        public List<PhoneNumber> PhoneNumberList { get; set; } // PID-13
        public List<PatientAddress> PatientAddressList { get; set; } // PID-11

      

    }
    public class PatientIdentifier
    {
        public string ID { get; set; }
        public string Authority { get; set; }
        public string IdentifierTypeCode { get; set; }
    }

    public class PatientName
    {
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string Prefix { get;  set; }
    }

    public class PatientAddress
    {
        public string StreetAddress { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
    }

    public class PhoneNumber
    {
        public string UseCode { get; set; }
        public string TelephoneNumber { get; set; }
        public string TelecommunicationUseCode { get; set; }
        public string TelecommunicationEquipmentType { get; set; }
        public string EmailAddress { get; set; }
        public string CountryCode { get; set; }
        public string AreaCityCode { get; set; }
        public string LocalNumber { get; set; }
    }
}
