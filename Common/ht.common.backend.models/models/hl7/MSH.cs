﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    public class MSH
    {
        public string FieldSeparator { get; set; }
        public string EncodingCharacters { get; set; }
        public string SendingApplication { get; set; }
        public string SendingFacility { get; set; }
        public string ReceivingApplication { get; set; }
        public string ReceivingFacility { get; set; }
        public DateTime DateTimeOfMessage { get; set; }
        public string MessageType { get; set; }
        public string MessageControlID { get; set; }
        public string ProcessingID { get; set; }
        public string VersionID { get; set; }

        public string Serialize()
        {
            return string.Format(
                "MSH{0}{1}{0}{2}{0}{3}{0}{4}{0}{5}{0}{6}{0}{7:yyyyMMddHHmmss}{0}{8}{0}{9}{0}{10}{0}{11}{0}{12}",
                FieldSeparator,
                EncodingCharacters,
                SendingApplication,
                SendingFacility,
                ReceivingApplication,
                ReceivingFacility,
                DateTimeOfMessage,
                MessageType,
                MessageControlID,
                ProcessingID,
                VersionID
            );
        }

        public void SetMSHFields(MSH mSH, HL7Message HL7)
        {
            HL7.SetField("MSH", 3, mSH.SendingApplication);
            HL7.SetField("MSH", 4, mSH.SendingFacility);
            HL7.SetField("MSH", 5, mSH.ReceivingApplication);
            HL7.SetField("MSH", 6, mSH.ReceivingFacility);
            HL7.SetField("MSH", 10, GenerateMessageControlId());
            HL7.SetField("MSH", 15, "NE");
            HL7.SetField("MSH", 16, "AL");
        }

        public void AddMSHComponents(HL7Message HL7)
        {
            HL7.AddComponent("MSH", 12, 1, "2.3.1");
            HL7.AddComponent("MSH", 12, 2, "AUS");
        }

        string GenerateMessageControlId()
        {
            // Combine the organization name with a truncated current date and time to create a unique ID.
            //string dateTimePart = DateTime.Now.ToString("yyMMddHHmmss");


            //// Combine the truncated org name with the dateTimePart.
            //return $"HT{dateTimePart}";

            string dateTimePart = DateTime.Now.ToString("yyMMddHHmmss");
            string uniqueId = Guid.NewGuid().ToString("N");

            string combinedId = $"HT{dateTimePart}{uniqueId}";
            return combinedId.Substring(0, Math.Min(15, combinedId.Length));
             
        }
    }


   
}
