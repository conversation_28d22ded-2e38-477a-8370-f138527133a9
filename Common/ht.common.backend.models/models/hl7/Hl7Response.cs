﻿using ht.data.common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{

    public class Hl7Response : ht.data.common.baseResponse
    {
        public List<Hl7Data> Hl7Data { get; set; }

        public HL7Acknowledgment HL7Acknowledgment { get; set; }
    }
    public class Hl7Data
    {
        public int ID { get; set; }
        public string MessageID { get; set; }
        public string SendingApplication { get; set; }
        public string SendingEDI { get; set; }
        public string ReceivingApplication { get; set; }
        public string ReceivingEDI { get; set; }
        public string MessageType { get; set; }
        public DateTime? MessageTimestamp { get; set; }
        public string MessageContent { get; set; }
        public string Status { get; set; }
        public string Acknowledgment { get; set; }
        public string ExResidentId { get; set; }
        public string ExFacilityId { get; set; }
    }
    public class HL7Acknowledgment
    {
        public int ID { get; set; }
        public string MessageID { get; set; }
        public string ErrorMessage { get; set; }
        public string AcknowledgmentCode { get; set; }
        public string AcknowledgmentMessage { get; set; }
        public DateTime? AcknowledgmentTimestamp { get; set; }
        public string SendingApplication { get; set; }
        public string SendingEDI { get; set; }
        public string ReceivingApplication { get; set; }
        public string ReceivingEDI { get; set; }
        public string Status { get; set; }
    }
    public class Hl7Request : baseRequest
    {
        public List<string> ExNoteIds { get; set; }
        public bool IsAllProgressNotes { get; set; }
        public bool IsAmended { get; set; } = false;
        public string? ExResidentId { get; set; }

        public Hl7Provider ReferToProvider { get; set; }
        public Hl7Provider CopyToProvider { get; set; }

        [JsonIgnore]
        public string PdfEncode { get; set; }

    }

    public class Hl7Provider
    {
        public string FamilyNameLastNamePrefix { get; set; }
        public string GivenName { get; set; }
        public string MiddleInitialOrName { get; set; }
        public string Prefix { get; set; }
    }
}
