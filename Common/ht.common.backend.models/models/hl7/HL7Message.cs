﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    public class HL7Message : HL7BaseMessage
    {
        private HL7MsgMap map = new HL7MsgMap();

        public HL7Message() : base()
        {
           
        }
        public HL7Message(string version, string msgType, string msgTrigger) : base(version, msgType, msgTrigger)
        { }

        public HL7Message(string version,string msgType,string msgTrigger,string segments) :base(version,msgType,msgTrigger)
        {
            foreach (var s in segments.Split(','))
                map.Segments.Add(s, null);
            
        }
        public HL7Message(HL7MsgMap msgMap) : base()
        {
            map = msgMap;
        }

        private void CreateMessageSegments()
        {
            foreach (var s in map.Segments)
                AddHL7Segment(s.Key);
        }
    }
}
