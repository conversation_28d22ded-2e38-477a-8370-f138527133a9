﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    public class PRD
    {
        public ProviderRole ProviderRole { get; set; }
        public Providername ProviderName { get; set; }
        public string ProviderAddress { get; set; }
        public (string ID, string IdType) ProviderIdentifiers { get; set; }
        public string EffectiveStartDate { get; set; }
        public string EffectiveEndDate { get; set; }
        public string ProviderLocation { get; set; }
        public string ProviderCommunicationInformation { get; set; }
        public string PreferredMethodofContact { get; set; }
    }

    public class Providername
    {
        public string FamilyNameLastNamePrefix { get; set; }
        public string GivenName { get; set; }
        public string MiddleInitialOrName { get; set; }
        public string Suffix { get; set; }
        public string Prefix { get; set; }
        public string Degree { get; set; }
        public string NameTypeCode { get; set; }
        public string NameRepresentationCode { get; set; }
    }
    public enum ProviderRole
    {
        CP, // Consulting Provider
        PP, // Primary Care Provider
        RP, // Referring Provider
        RT  // Referred to Provider
    }
}
