﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    public class OBX
    {
        public string SetID { get; set; }
        public string ValueType { get; set; }
        public ObservationIdentifier ObservationIdentifier { get; set; }
        public string ObservationSubId { get; set; }
        public string ObservationValue { get; set; }
        public string Units { get; set; }
        public string ReferenceRanges { get; set; }
        public string AbnormalFlags { get; set; }
        public string Probability { get; set; }
        public string ObservationResultStatus { get; set; }
        public string DateTimeOfObservation { get; set; }
        public string ProducersID { get; set; }
    }

    public class ObservationIdentifier
    {
        public string Identifier { get; set; }
        public string Text { get; set; }
        public string NameOfCodingSystem { get; set; }
        public string AlternateComponents { get; set; }
        public string AlternateText { get; set; }
        public string NameOfAlternateCodingSystem { get; set; }
    }
}
