﻿using PeterPiper.Hl7.V2.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    public class HL7BaseMessage
    {
        internal PeterPiper.Hl7.V2.Model.IMessage hl7Msg { get; set; }

        public HL7BaseMessage()
        {
            
        }

        public HL7BaseMessage(string version,string msgType,string msgTrigger)
        {
            hl7Msg = Creator.Message(version, msgType, msgTrigger); 
        }

        public string GetFieldValue(string segName,int idx)
        {
            return hl7Msg.Segment(segName).Field(idx).AsStringRaw;
        }
        public string GetFieldComponentValue(string segName, int idx,int component)
        {
            return hl7Msg.Segment(segName).Field(idx).Component(component).AsStringRaw;
        }

        public PeterPiper.Hl7.V2.Model.IField GetField(string segName, int idx)
        {
            return hl7Msg.Segment(segName).Field(idx);
        }

        public void SetField(string segName,int idx, string fValue)
        {
            hl7Msg.Segment(segName).Field(idx).AsString = fValue;
        }
        public ISegment CloneSegment(string segName)
        {
          return  hl7Msg.Segment(segName).Clone();
        }

        public void ParseHL7Message(string msg)
        {
            try
            {
                hl7Msg = PeterPiper.Hl7.V2.Model.Creator.Message(msg);
            }
            catch (PeterPiper.Hl7.V2.CustomException.PeterPiperException Exec)
            {
                string Message = Exec.Message;
                throw new Exception(Message);   
            }
        }

        public void CreateHL7Message(string msgVer,string msgType, string msgTrigger)
        {
            //string MessageVersion = "2.4";
            //string MessageType = "ADT";
            //string MessageTrigger = "A01";

            hl7Msg = PeterPiper.Hl7.V2.Model.Creator.Message(msgVer, msgType, msgTrigger);
        }

        public PeterPiper.Hl7.V2.Model.ISegment GetHL7Segment(string segName)
        {
          
            foreach(var seg in hl7Msg.SegmentList())
            {
                if (seg.Code == segName)
                    return (seg);
            }


            PeterPiper.Hl7.V2.Model.ISegment res = AddHL7Segment(segName);
            return (res);
        }


        public PeterPiper.Hl7.V2.Model.ISegment AddHL7Segment(string segName)
        {
            var obj = PeterPiper.Hl7.V2.Model.Creator.Segment(segName);
            hl7Msg.Add(obj);
            return (obj);
            
            //NOTE: we can also insert here as well to an index value.   
        }

        public void AddComponent(string segName, int field, int component,string value)
        {
           hl7Msg.Segment(segName).Field(field).Component(component).AsString=value;

        }

        public override string ToString()
        {
            return hl7Msg?.AsStringRaw;
        }
    }

   
}
