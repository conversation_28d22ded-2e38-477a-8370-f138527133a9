﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.hl7
{
    using System;
    using System.Collections.Generic;

    public class OBR
    {
        public string SetID { get; set; } // OBR-1
        public string PlacerOrderNumber { get; set; } // OBR-2
        public FileOrderNumber FillerOrderNumber { get; set; } // OBR-3
        public UniversalServiceID UniversalServiceIDInfo { get; set; } // OBR-4
        public string ObservationDateTime { get; set; } // OBR-7
        public Quantity CollectionVolume { get; set; } // OBR-9
        public string RelevantClinicalInfo { get; set; } // OBR-13
        public DateTime? SpecimenReceivedDateTime { get; set; } // OBR-14
        public List<Provider> OrderingProviders { get; set; } // OBR-16
        public List<string> FillerField1 { get; set; } // OBR-20
        public DateTime? ResultsReportDateTime { get; set; } // OBR-22
        public string DiagnosticServiceSectorID { get; set; } // OBR-24
        public string ResultStatusID { get; set; } // OBR-25
        public List<QuantityTiming> QuantityTimings { get; set; } // OBR-27
        public List<Provider> ResultCopiesTo { get; set; } // OBR-28


        public class FileOrderNumber
        {
            public string EntityIdentifier { get; set; }
            public string NameSpaceId { get; set; }
            public string UniversalId { get; set; }
            public string IdType { get; set; }
        }
        public class UniversalServiceID
        {
            public string Identifier { get; set; }
            public string Text { get; set; }
            public string NameOfCodingSystem { get; set; }
            public string AlternateIdentifier { get; set; }
            public string AlternateText { get; set; }
            public string NameOfAlternateCodingSystem { get; set; }
        }

        public class Quantity
        {
            public string Qty { get; set; }
            public string Units { get; set; }
        }

        public class Provider
        {
            public string ID { get; set; }
            public string Prefix { get; set; }
            public string Authority { get; set; }
            public string LastName { get; set; }
            public string FirstName { get; set; }
            public string MiddleInitial { get; set; }
        }

        public class QuantityTiming
        {
            public string Quantity { get; set; }
            public string TimingQualifier { get; set; }
        }
    }

}
