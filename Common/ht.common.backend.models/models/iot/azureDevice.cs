﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.iot
{
    public class azureDevice
    {
        public int? IoTDeviceId { get; set; }
        public string? ExIoTDeviceId { get; set; }
        public string? DeviceConnectionString { get; set; }
        public DateTime? DateCreatedUtc { get; set; }
        public DateTime? DateModifiedUtc { get; set; }
        public DateTime? DateLastAllocatedUtc { get; set; }
        public string? DeviceName { get; set; }
        public string? ExFacilityId { get; set; }

    }
}
