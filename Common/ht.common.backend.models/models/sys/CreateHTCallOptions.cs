﻿using Azure.Communication;
using Azure.Communication.CallingServer;
using ht.data.common.Telehealth;
using System;
using System.Collections.Generic;
using System.Text;

namespace ht.common.backend.shared.models.sys
{
    public class CreateHTCallOptions
    {
        public string acsCallId { get; set; }
        public string Subject { get; set; }
        public string NotifcationCallbackUri { get; set; }
        public MeetingUser SourceParty { get; set; }
        public List<MeetingUser> Parties { get; set; }

        public CreateHTCallOptions()
        {
            
        }
        

    }
}
