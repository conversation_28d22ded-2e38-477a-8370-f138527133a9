﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.common.backend.shared.models.security
{
    public class authClientConfig
    {
            public string Instance { get; set; } = "https://login.microsoftonline.com/{0}";
            public string TenantId { get; set; }
            public string ClientId { get; set; }
            public string Authority
            {
                get
                {
                    return String.Format(CultureInfo.InvariantCulture,
                                         Instance, TenantId);
                }
            }
            public string ClientSecret { get; set; }
            public string BaseAddress { get; set; }
            public string ResourceID { get; set; }

            public static authClientConfig ReadFromJsonFile(string path)
            {
                IConfiguration Configuration;

                var builder = new ConfigurationBuilder()
                  .SetBasePath(Directory.GetCurrentDirectory())
                  .AddJsonFile(path);

                Configuration = builder.Build();

                return Configuration.Get<authClientConfig>();
            }
        }


    }

