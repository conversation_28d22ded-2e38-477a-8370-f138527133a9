#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app

# Install dependencies for downloading fonts
RUN apt-get update && apt-get install -y \
    curl \
    fontconfig \
    --no-install-recommends && \
    mkdir -p /usr/share/fonts/truetype/roboto && \
    curl -L -o /usr/share/fonts/truetype/roboto/Roboto-Regular.ttf https://github.com/google/fonts/raw/main/apache/roboto/Roboto-Regular.ttf && \
    curl -L -o /usr/share/fonts/truetype/roboto/Roboto-Bold.ttf https://github.com/google/fonts/raw/main/apache/roboto/Roboto-Bold.ttf && \
    fc-cache -f -v && \
    apt-get clean && rm -rf /var/lib/apt/lists/*
    
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
#release or debug 
ARG CONFIGURATION

COPY ["Integrations/ht.be.apis/ht.be.apis.csproj", "Integrations/ht.be.apis/"]
COPY ["Common/ht.common.backend.models/ht.common.backend.shared.csproj", "Common/ht.common.backend.models/"]
RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh  | sh
ARG FEED_ACCESSTOKEN
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS \
    "{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/healthteams/_packaging/healthteams/nuget/v3/index.json\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"

ENV DOCKER_BUILDKIT=0
COPY ["Nuget.config", "."]

RUN dotnet nuget locals all --clear

RUN dotnet restore  --no-cache --force --verbosity detailed "Integrations/ht.be.apis/ht.be.apis.csproj" --configfile "Nuget.config"
COPY . .
WORKDIR "/src/Integrations/ht.be.apis"
ENV CONFIGURATION=$CONFIGURATION

RUN echo "Build Configuration: ${CONFIGURATION}"

RUN dotnet build "ht.be.apis.csproj" -c ${CONFIGURATION} -o /app/build

FROM build AS publish
RUN dotnet publish "ht.be.apis.csproj" -c ${CONFIGURATION} -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ht.be.apis.dll"]
