﻿using Azure.Messaging.EventGrid;
using ht.common.backend.shared.helpers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class IotHubFileProcessorTest
    {
        [Fact]
        public void TestIotHubFileProcessor()
        {
            // Arrange
            var uploadUrl = "https://myiotdevice.blob.core.windows.net/uploads/myfile.jpg";
            var data = new { url = uploadUrl }.ToString();
            var eventGridEvent = new EventGridEvent( "subject", "eventType", "dataVersion", data);

            var loggerMock = new Mock<ILogger>();
            var blobHelperMock = new Mock<BlobHelper>();

            // Act
           IoTHubFileProcessor.Run(eventGridEvent, loggerMock.Object);
            Assert.NotNull(blobHelperMock.Object);

        }
    }
}
