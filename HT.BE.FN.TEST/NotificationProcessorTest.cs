﻿using Microsoft.Azure.WebJobs.Extensions.Timers;
using Microsoft.Azure.WebJobs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class NotificationProcessorTests
    {
        [Fact]
        public void TestNotificationProcessor()
        {
            // Arrange
            var timerInfo = new TimerInfo(new ScheduleStub(), new ScheduleStatus());
            var mockLogger =new  Mock<ILogger>();

            // Act
            var function = new NotificationProcessor();
            function.Run(timerInfo, mockLogger.Object);

            // Assert
            mockLogger.Verify(
                x => x.LogInformation("NotificationProcessor - Starting Timer Job"), Times.Once);
            mockLogger.Verify(
                x => x.LogInformation("NotificationProcessor - Completed Timer Job"), Times.Once);

        }

        // A simple implementation of Schedule for testing
        private class ScheduleStub : TimerSchedule
        {
            public override bool AdjustForDST => false;

            public override DateTime GetNextOccurrence(DateTime now)
            {
                return now.AddMinutes(30);
            }
        }

        
        
    }
}
