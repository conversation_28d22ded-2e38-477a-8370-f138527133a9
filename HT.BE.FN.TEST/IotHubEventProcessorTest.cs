﻿using Azure.Messaging.EventGrid;
using Azure.Messaging.EventHubs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class IotHubEventProcessorTests
    {
        [Fact]
        public void IotHubEventProcessor_ProcessesMessage()
        {
            // Arrange
            var mockLog = new Mock<ILogger<IotHubEventProcessor>>();
            var function = new IotHubEventProcessor(mockLog.Object);

            // Set up a mock event data object
            var eventGridEvent = new EventGridEvent("subject", "Telemetry", "dataVersion",  new { test = "" }.ToString() );
        
            // Act
            function.RunMessageProcessor(eventGridEvent, mockLog.Object);
            Assert.NotNull(eventGridEvent);
        }
    }
}
