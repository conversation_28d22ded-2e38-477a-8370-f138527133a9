﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class PbsProcessorTest
    {

        [Fact]
        public async Task TestPbsProcessor()
        {
            var mockRequest = new Mock<HttpRequest>();
            var mockLogger =  Mock.Of<ILogger>();

            var query= new Dictionary<string, StringValues>()
            {
                { "runId","123456" }
            };

            mockRequest.Setup(p => p.Query).Returns(new QueryCollection(query));

            var response = await PbsProcessor.Run(mockRequest.Object, mockLogger);

            var expected = new { };

             var result= Assert.IsType<OkObjectResult>(response);
            Assert.Equal(expected, result.Value);

        }
    }
}
