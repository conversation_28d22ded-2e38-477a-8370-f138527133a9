﻿using ht.common.backend.shared.helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using OpenCvSharp.Aruco;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Ht.Be.Fn.Test;
public class TestFactory
{

    public HttpClient _client;


    public TestFactory()
    {
        DBHelper.dbConnStr = "Server=tcp:htnp01.database.windows.net,1433;Initial Catalog=ht_db;Persist Security Info=False;User ID=adminuser;Password=!HealthTeams.123!;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30";
        _client = new HttpClient();
        _client.BaseAddress = new Uri("http://localhost:5001");
    }

    static Dictionary<string, StringValues> GetQueryString(string key, string values)
    {
        return new Dictionary<string, StringValues>()
        {
            { key, values}

        };
    }

    public static HttpRequest GetHttpRequest(string key, string value)
    {
        var context = new DefaultHttpContext();
        HttpRequest request = context.Request;
        request.Query = new QueryCollection(GetQueryString(key, value));
        return request;
    }



}



