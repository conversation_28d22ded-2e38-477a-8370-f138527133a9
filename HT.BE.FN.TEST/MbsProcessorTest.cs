﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class MbsProcessorTest
    {

        [Fact]
        public async Task TestMbsProcessor()
        {

            // Arrange
            var mockLogger = new Mock<ILogger>();
            var mockRequest = new Mock<HttpRequest>();

            var query = new Dictionary<string, StringValues>()
            {
                { "runId","123456" }
            };

            mockRequest.Setup(p => p.Query).Returns(new QueryCollection(query));
            var result = await MbsProcessor.Run(mockRequest.Object, mockLogger.Object);

            var expected = Assert.IsType<string>(result);
            Assert.Equal(expected.ToString(), result.ToString());
        }
    }
}
