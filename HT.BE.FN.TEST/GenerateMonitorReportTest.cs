﻿

namespace Ht.Be.Fn.Test
{
    public class GenerateMonitorReportTest
    {
        [Fact]
        public async Task GenerateMonitorReport_Should_Return_Url()
        {
            // Arrange
            var mockLogger = new Mock<ILogger>();
            var mockRequest = new Mock<HttpRequest>();
            var query = new Dictionary<string, StringValues>()
        {
            { "userExId", "DC81F83D-A9CD-4CC5-A4BC-3D6EED4B9092" },
            { "exTaskId", "00000000-0000-0000-0000-000000000000" },
            { "exTransactionId", "" }
        };
            mockRequest.Setup(x => x.Query).Returns(new QueryCollection(query));

            var dt = DateTime.UtcNow.ToString("yyyyMMdd-hhmm");
            var userExId = query["userExId"].FirstOrDefault();

            var expectedResult = new JsonResult(new { url = $"{userExId?.ToUpper()}/reports/HealthTeamsReport-{userExId}-{dt}.pdf"});

            var result = await GenerateMonitorReport.Run(mockRequest.Object, mockLogger.Object);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(expectedResult?.Value?.ToString(), jsonResult?.Value?.ToString());

        }
    }
}
