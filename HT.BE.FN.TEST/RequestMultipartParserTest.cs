﻿using Microsoft.Azure.WebJobs.Extensions.Http;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class RequestMultipartParserTest
    {

        [Fact]
        public async Task Should_Return_Null_When_No_Content_Type()
        {
            // Arrange
           
            var mockRequest = new  Mock<HttpRequest>();
            mockRequest.Setup(x=>x.Method).Returns(HttpMethod.Post.ToString());
            mockRequest.Setup(x=>x.Body).Returns(new MemoryStream(Encoding.UTF8.GetBytes("test")));
            mockRequest.Setup(x => x.Headers["Content-Type"]).Returns(new StringValues("multipart/form-data"));
            var logger = new LoggerFactory().CreateLogger("RequestMultipartParser");

            // Act
            var result = (OkObjectResult)await RequestMultipartParser.Run(mockRequest.Object, logger, null);

            // Assert
         
            var response = result?.Value;
            Assert.Null(response);
        }

    }
}
