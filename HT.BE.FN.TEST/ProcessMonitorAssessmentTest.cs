﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class ProcessMonitorAssessmentTest
    {
        [Fact]
        public async Task TestMonitorAssessment()
        {
            // Arrange
            var logger = new Mock<ILogger>();
            var request = new Mock<HttpRequest>();

            var requestBody = "{\"ExFacilityId\":\"66A6A72A-3312-4C7B-9DE0-840988314880\",\"UserExId\":\"335D7BD0-4CEE-4E21-81B6-00621582ADCD\",\"ExTaskId\":\"11111\"}";
            var stream = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));

            request.Setup(p => p.Body).Returns(stream);
            // Act
            var response = await ProcessMonitoringAssessment.Run(request.Object, logger.Object);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
            Assert.Equal("application/json", response.Content.Headers.ContentType.MediaType);
            var content = await response.Content.ReadAsStringAsync();
            Assert.NotEmpty(content);
        }
    }
}
