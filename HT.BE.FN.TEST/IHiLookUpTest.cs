﻿
using ht.common.backend.shared.models;

namespace Ht.Be.Fn.Test
{
    public class IHiLookUpTest
    {
        /* MICK - Lookup is performed in another project - not needed here.

        [Fact]
        public async Task TestIHiLookUp()
        {
            // Arrange
            var logger = new Mock<ILogger>();
            var request = new Mock<HttpRequest>();

            var requestBody = "{\"ExFacilityId\":\"66A6A72A-3312-4C7B-9DE0-840988314880\",\"UserExId\":\"335D7BD0-4CEE-4E21-81B6-00621582ADCD\",\"ExTaskId\":\"11111\"}";
            var stream = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));

            request.Setup(p => p.Body).Returns(stream);

            var result = await IHILookup.Run(request.Object,logger.Object) as OkObjectResult;
            Assert.IsType<ht.data.common.baseResponse>(result?.Value);
        }
        */
    }
}
