﻿using Azure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class GenerateNotereportTest
    {

        [Theory]
        [InlineData("209986dc-1616-4d52-b8fb-cefd4d39415f")]
        public async Task GenerateNoteReport_ReturnsPdfFile(string residentId)
        {
            // Arrange
            var mockLogger = new Mock<ILogger>();
            var mockRequest = new Mock<HttpRequest>();
            var query = new Dictionary<string, StringValues>()
        {
            { "exResidentId", residentId }
        };
            mockRequest.Setup(x => x.Query).Returns(new QueryCollection(query));

            var result = await GenerateNoteReport.Run(mockRequest.Object, mockLogger.Object);

            // Assert
            // Assert
            Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/pdf", ((FileContentResult)result).ContentType);
            Assert.NotNull(((FileContentResult)result).FileContents);
        }
    }
}
