﻿using ht.data.common.Wounds;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class DiagnoseImageTest
    {
        [Fact]
        public void DiagnoseImage_ReturnsOkObjectResult()
        {
            // Arrange
            
            var httpRequestMessage = new HttpRequestMessage();
            var logger = new Mock<ILogger>();
            // Arrange
            var request = new WoundDiagRequest
            {
                Images = new List<ImageDiag>
                {
                   
                },
                Detailed = false
            };
            var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

            httpRequestMessage.Content= content;
            // Act
            var response = DiagnoseImage.Run(httpRequestMessage, logger.Object).Result as OkObjectResult;

            // Assert
            Assert.NotNull(response);
            Assert.IsType<OkObjectResult>(response);
            Assert.Equal(200, response.StatusCode);
        }
    }
}
