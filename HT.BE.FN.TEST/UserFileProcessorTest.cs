﻿
namespace Ht.Be.Fn.Test
{
    public class UserFileProcessorTest :TestFactory
    {

        [Fact]
        public async Task TestFileProcessor()
        {
            var mockLogger = new Mock<ILogger>();
            var mockRequest = new Mock<HttpRequest>();
            var query = new Dictionary<string, StringValues>()
        {
            { "userid", "209986dc-1616-4d52-b8fb-cefd4d39415f" },
            { "fname", "john" },
            { "ftype", "logo" }
        };

            mockRequest.Setup(x => x.Query).Returns(new QueryCollection(query));
            mockRequest.Setup(x => x.Body).Returns(new MemoryStream(Encoding.UTF8.GetBytes("test content")));
            var userStorageUrl = "https://htapp.blob.core.windows.net/users";
            string? userId = query["userid"].FirstOrDefault();
            string? fname = query["fname"].FirstOrDefault();

            var result =  await UserFileProcessor.Run(mockRequest.Object,mockLogger.Object);
            string expected = $"{userStorageUrl}/{userId?.ToUpper()}/profile/{fname}.png";

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var userUrl = Assert.IsType<string>(okResult.Value);
            Assert.Equal(expected, userUrl);
        }
    }
}
