﻿using ht.data.common.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Ht.Be.Fn.Test
{
    public class UserFileGetSasTokenTest
    {
        [Fact]
        public async Task TestUserFileSasToken() { 

            var mockLogger= Mock.Of<ILogger>();
            var req = TestFactory.GetHttpRequest("itime", "60");

           req.Body= new MemoryStream(Encoding.UTF8.GetBytes("{\"Files\": [{\"FileUrl\": \"https://htappuat.blob.core.windows.net/users/0193119E-10BD-4137-84CB-D417DF5C91DD/reports/HealthTeamsReport-00000000-0000-0000-0000-000000000000-20220928-0956.pdf\"}]}"));
            req.ContentType="application/json";
            
            var result = await UserFileGetSasToken.Run(req, mockLogger);

            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }
    }
}
