﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.2" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.2" />
		<PackageReference Include="coverlet.collector" Version="6.0.0">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="xunit" Version="2.5.0" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.5.0">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Moq" Version="4.20.69" />

	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Functions\ht.be.fe\ht.be.fn.csproj" />
	</ItemGroup>

</Project>
