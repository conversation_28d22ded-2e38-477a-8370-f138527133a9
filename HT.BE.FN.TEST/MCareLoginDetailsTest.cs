﻿using Azure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ht.Be.Fn.Test
{
    public class MCareLoginDetailsTest
    {
        [Fact]
        public async Task TestMCareLoginDetails()
        {
            // Arrange
            var mockLogger = new Mock<ILogger>();
            var mockRequest = new Mock<HttpRequest>();

            var result = await mCareGetLoginDetails.Run(mockRequest.Object, mockLogger.Object) as OkObjectResult;
            Assert.NotNull(result);

            Assert.NotNull(result.Value as string);

        }
    }
}
