﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31423.177
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Samples", "Samples", "{2FBC2F26-1004-4940-9253-F013508CE94D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ACS_ConsoleApp", "Samples\ACS_ConsoleApp\ACS_ConsoleApp.csproj", "{F771E180-AFCA-4485-9880-EDA0AC6BBDD2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Integrations", "Integrations", "{D4765537-3939-470A-9AA0-022F71F3BE5C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{DF5C069F-B11B-4BCE-B6B0-CE260F5B1DAF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ht.common.backend.shared", "Common\ht.common.backend.models\ht.common.backend.shared.csproj", "{FBCB0FB7-2AE2-444E-8983-5FBD279C15AD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ht.be.apis", "Integrations\ht.be.apis\ht.be.apis.csproj", "{59F5BEAB-BE03-4934-8E79-385385CB0766}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{FDB14EC1-411E-460C-A0E8-1657470FC7B8}"
	ProjectSection(SolutionItems) = preProject
		ht-be-apis-uat-client.txt = ht-be-apis-uat-client.txt
		Nuget.config = Nuget.config
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ht.be.apis.uat.client", "Samples\ht.be.apis.uat.client\ht.be.apis.uat.client.csproj", "{6EF9307C-3970-4303-9270-3CFE32A68AB3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "HL7Sample", "Samples\HL7Sample\HL7Sample.csproj", "{79DC7826-2FF4-46E2-8629-9473B3E38654}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Functons", "Functons", "{AAD7150C-DC02-4AEE-A47F-59E310315939}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ht.be.fn", "Functions\ht.be.fe\ht.be.fn.csproj", "{65725685-6C5C-416F-9C61-1A298767077A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ht.be.apis.tests", "Integrations\ht.be.apis.tests\ht.be.apis.tests.csproj", "{F947EB6B-BB0B-4CB2-B9AB-F6718D92F56B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MediaToImage", "Samples\MediaToImage\MediaToImage.csproj", "{740F8B04-AC91-46DB-A143-1F232019EEFC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Utils", "Utils", "{2F3CCC6F-8757-4539-A73B-991A1AAD3277}"
	ProjectSection(SolutionItems) = preProject
		Integrations\ht.be.apis\Controllers\TeleHealthController.cs = Integrations\ht.be.apis\Controllers\TeleHealthController.cs
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ht.b2c.api.extension", "Functions\ht.b2c.api.extension\ht.b2c.api.extension.csproj", "{5BE31D19-711C-4ED3-90D6-2BA0BA6B7DD7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{9A2FEAEA-CCA5-4358-9E3F-02068E3E306A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Ht.Be.Fn.Test", "HT.BE.FN.TEST\Ht.Be.Fn.Test.csproj", "{BFD3E3A7-DEB2-4F2E-8BB1-28B69C48662B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AAD_SampleCode", "Samples\AAD_SampleCode\AAD_SampleCode.csproj", "{7BF33FC9-8E30-4A2E-9578-2DBF8D4DBACA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F771E180-AFCA-4485-9880-EDA0AC6BBDD2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F771E180-AFCA-4485-9880-EDA0AC6BBDD2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F771E180-AFCA-4485-9880-EDA0AC6BBDD2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F771E180-AFCA-4485-9880-EDA0AC6BBDD2}.Release|Any CPU.Build.0 = Release|Any CPU
		{FBCB0FB7-2AE2-444E-8983-5FBD279C15AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FBCB0FB7-2AE2-444E-8983-5FBD279C15AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FBCB0FB7-2AE2-444E-8983-5FBD279C15AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FBCB0FB7-2AE2-444E-8983-5FBD279C15AD}.Release|Any CPU.Build.0 = Release|Any CPU
		{59F5BEAB-BE03-4934-8E79-385385CB0766}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59F5BEAB-BE03-4934-8E79-385385CB0766}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59F5BEAB-BE03-4934-8E79-385385CB0766}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59F5BEAB-BE03-4934-8E79-385385CB0766}.Release|Any CPU.Build.0 = Release|Any CPU
		{6EF9307C-3970-4303-9270-3CFE32A68AB3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6EF9307C-3970-4303-9270-3CFE32A68AB3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6EF9307C-3970-4303-9270-3CFE32A68AB3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6EF9307C-3970-4303-9270-3CFE32A68AB3}.Release|Any CPU.Build.0 = Release|Any CPU
		{79DC7826-2FF4-46E2-8629-9473B3E38654}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{79DC7826-2FF4-46E2-8629-9473B3E38654}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{79DC7826-2FF4-46E2-8629-9473B3E38654}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{79DC7826-2FF4-46E2-8629-9473B3E38654}.Release|Any CPU.Build.0 = Release|Any CPU
		{65725685-6C5C-416F-9C61-1A298767077A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{65725685-6C5C-416F-9C61-1A298767077A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{65725685-6C5C-416F-9C61-1A298767077A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{65725685-6C5C-416F-9C61-1A298767077A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F947EB6B-BB0B-4CB2-B9AB-F6718D92F56B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F947EB6B-BB0B-4CB2-B9AB-F6718D92F56B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F947EB6B-BB0B-4CB2-B9AB-F6718D92F56B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F947EB6B-BB0B-4CB2-B9AB-F6718D92F56B}.Release|Any CPU.Build.0 = Release|Any CPU
		{740F8B04-AC91-46DB-A143-1F232019EEFC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{740F8B04-AC91-46DB-A143-1F232019EEFC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{740F8B04-AC91-46DB-A143-1F232019EEFC}.Release|Any CPU.Build.0 = Release|Any CPU
		{5BE31D19-711C-4ED3-90D6-2BA0BA6B7DD7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5BE31D19-711C-4ED3-90D6-2BA0BA6B7DD7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5BE31D19-711C-4ED3-90D6-2BA0BA6B7DD7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5BE31D19-711C-4ED3-90D6-2BA0BA6B7DD7}.Release|Any CPU.Build.0 = Release|Any CPU
		{BFD3E3A7-DEB2-4F2E-8BB1-28B69C48662B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BFD3E3A7-DEB2-4F2E-8BB1-28B69C48662B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BFD3E3A7-DEB2-4F2E-8BB1-28B69C48662B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BFD3E3A7-DEB2-4F2E-8BB1-28B69C48662B}.Release|Any CPU.Build.0 = Release|Any CPU
		{7BF33FC9-8E30-4A2E-9578-2DBF8D4DBACA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7BF33FC9-8E30-4A2E-9578-2DBF8D4DBACA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7BF33FC9-8E30-4A2E-9578-2DBF8D4DBACA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7BF33FC9-8E30-4A2E-9578-2DBF8D4DBACA}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F771E180-AFCA-4485-9880-EDA0AC6BBDD2} = {2FBC2F26-1004-4940-9253-F013508CE94D}
		{DF5C069F-B11B-4BCE-B6B0-CE260F5B1DAF} = {D4765537-3939-470A-9AA0-022F71F3BE5C}
		{FBCB0FB7-2AE2-444E-8983-5FBD279C15AD} = {DF5C069F-B11B-4BCE-B6B0-CE260F5B1DAF}
		{59F5BEAB-BE03-4934-8E79-385385CB0766} = {D4765537-3939-470A-9AA0-022F71F3BE5C}
		{6EF9307C-3970-4303-9270-3CFE32A68AB3} = {2FBC2F26-1004-4940-9253-F013508CE94D}
		{79DC7826-2FF4-46E2-8629-9473B3E38654} = {2FBC2F26-1004-4940-9253-F013508CE94D}
		{65725685-6C5C-416F-9C61-1A298767077A} = {AAD7150C-DC02-4AEE-A47F-59E310315939}
		{F947EB6B-BB0B-4CB2-B9AB-F6718D92F56B} = {9A2FEAEA-CCA5-4358-9E3F-02068E3E306A}
		{740F8B04-AC91-46DB-A143-1F232019EEFC} = {2FBC2F26-1004-4940-9253-F013508CE94D}
		{5BE31D19-711C-4ED3-90D6-2BA0BA6B7DD7} = {AAD7150C-DC02-4AEE-A47F-59E310315939}
		{BFD3E3A7-DEB2-4F2E-8BB1-28B69C48662B} = {9A2FEAEA-CCA5-4358-9E3F-02068E3E306A}
		{7BF33FC9-8E30-4A2E-9578-2DBF8D4DBACA} = {2FBC2F26-1004-4940-9253-F013508CE94D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {00C182C2-5FAA-42B7-8CD1-406B1F90E045}
	EndGlobalSection
EndGlobal
