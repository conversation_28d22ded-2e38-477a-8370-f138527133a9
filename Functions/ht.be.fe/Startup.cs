﻿using ht.be.fn.Helpers;
using ht.be.fn.Utils;
using ht.common.backend.shared.helpers;

using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text;


[assembly: FunctionsStartup(typeof(ht.be.fn.Startup))]
namespace ht.be.fn
{

    public class Startup : FunctionsStartup
    {
        public override void Configure(IFunctionsHostBuilder builder)
        {
            var context = builder.GetContext();

            Utilities.os_env = ((System.Environment.GetEnvironmentVariable("ASPNETCORE_CONTENTROOT")?.Contains("/azure-functions") == true) ? "linux" : "x86");

            MediaHelper.ffMpegZipPath = "https://htappuat.blob.core.windows.net/pub/utils/ffmpeg.zip";
            MediaHelper.AppPath = context.ApplicationRootPath;

            AzureKeyvaultHelper.KeyVaultUrl = Environment.GetEnvironmentVariable("KeyVaultUrl");
            AzureKeyvaultHelper.KeyPrefix = "htbefn";
            AzureKeyvaultHelper.ClientId = "1c120c0c-85b0-44b8-9828-fa48901674ab"; //NB: these are just for Non-Prod\r\n
            AzureKeyvaultHelper.ClientSecret = "**********************************";
            AzureKeyvaultHelper.TenantId = "e6a1adfb-abaf-4fb0-b0b1-a5dcd7c60baf"; //azure AD tenant

            Utils.Utilities.svc_acct_UserExId = "10000001-1001-1001-1001-100000000001";

            var config = AzureKeyvaultHelper.GetSecrets();
            if (config?.Count > 0)
            {
                NotificationHelper.EmailUrl = config["emailurl"]; //these 2 are the logic apps
                NotificationHelper.SMSUrl = config["smsurl"];
                NotificationHelper.apimKey = config["apimKey"];
                DBHelper.dbConnStr = config["dbconnstr"];
                AzureKeyvaultHelper.Backend_URL = config["backend-url"];
                AzureKeyvaultHelper.ImportFlagFiledName = config["import-flag-field-name"];
                AzureKeyvaultHelper.ImportFlagFieldValue = config["import-flag-field-value"];
                IotHubEventProcessor.UserBlobUri = config["blobusersurl"];   //https://htappuat.blob.core.windows.net/users
                IotHubEventProcessor.UploadBlobUri = config["blobuploadsurl"]; // Environment.GetEnvironmentVariable("UploadBlobUrl");
                ProcessMonitoringAssessment.UserBlobUri = config["blobusersurl"];   //https://htappuat.blob.core.windows.net/users
                ProcessMonitoringAssessment.UploadBlobUri = config["blobuploadsurl"]; // Environment.GetEnvironmentVariable("UploadBlobUrl");
                AssessReadings.ReadIconBaseUrl = config["readiconbaseurl"]; //Environment.GetEnvironmentVariable("ReadIconBaseUrl");

                BlobHelper.AcsAccessKey = config["acsaccesskey"];
                BlobHelper.AcsUrl = config["acsurl"]; //Environment.GetEnvironmentVariable("AcsUrl");

                //MediaHelper.DownloadFFMpeg();

                NotificationHelper.LinkUrls = new()
                {
                    {"ApptReminder",config["apptreminder"] }   //https://appuat.healthteams.com.au/reminder?ExTaskId={exTaskId}
                };
            }
            if (Utilities.os_env == "linux")
            {


                #region bash
                //string sourceFilePath = "/home/<USER>/wwwroot/ffmpeg/linux/ffmpeg.exe";
                //string targetDirectory = "/tmp/";

                /* MICK: Pramesh this code will slow the entire function project down - usually the functions are 'on-demand'
                 * spun up in response to a HTTP request.
                 * We can't have this here.
                 
                ProcessStartInfo startInfo = new ProcessStartInfo();
                startInfo.FileName = "/bin/bash";
                startInfo.Arguments = $"sudo yum install -y libgdiplus-2.10-9.el7.x86_64";
                startInfo.UseShellExecute = false;
                startInfo.RedirectStandardOutput = true;
                startInfo.RedirectStandardError = true;

                Process process = new Process();
                process.StartInfo = startInfo;
                process.Start();

                string output = process.StandardOutput.ReadToEnd();
                string error = process.StandardError.ReadToEnd();

                process.WaitForExit();
                */

                //if (process.ExitCode != 0)
                //{
                //    Console.WriteLine($"Error: {error}");
                //}
                //else
                //{
                //    Console.WriteLine($"File copied successfully to '{targetDirectory}'");
                //}
                #endregion

                #region system.io
                ProcessStartInfo startInfo = new ProcessStartInfo();
                Process process = new Process();

                string sourceFilePath = "/home/<USER>/wwwroot/ffmpeg/linux/ffmpeg.exe";
                string targetDirectory = "/tmp/";

                string targetFilePath = Path.Combine(targetDirectory, "ffmpeg.exe");

                File.Copy(sourceFilePath, targetFilePath, true);
                File.SetAttributes(targetFilePath, FileAttributes.Normal);
                File.SetAttributes(targetFilePath, FileAttributes.ReadOnly);
                File.SetAttributes(targetFilePath, FileAttributes.Archive | FileAttributes.Hidden | FileAttributes.ReadOnly | FileAttributes.System);


                startInfo.FileName = "chmod";
                startInfo.Arguments = $"777 {targetFilePath}";

                process = new Process();
                process.StartInfo = startInfo;
                process.Start();
                process.WaitForExit();

                if (process.ExitCode != 0)
                {
                    Console.WriteLine("Failed to set file permission");
                }
                else
                {
                    Console.WriteLine($"File copied successfully to '{targetDirectory}' and permission set to 777");
                }
                Console.WriteLine($"File copied successfully to '{targetDirectory}'");

                #endregion

            }







        }


    }
}
