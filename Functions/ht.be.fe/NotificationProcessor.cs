using System;
using ht.be.fn.Helpers;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Host;
using Microsoft.Extensions.Logging;

namespace ht.be.fn
{
    public class NotificationProcessor
    {
        [FunctionName("NotificationProcessor")]
        public void Run([TimerTrigger("0 */30 * * * *")]TimerInfo myTimer, ILogger log)
        {
            log.LogInformation($"NotificationProcessor - Starting Timer Job");
            var helper = new NotificationHelper(log);
           // helper.ProcessNotifications().GetAwaiter().GetResult();
            log.LogInformation($"NotificationProcessor - Completed Timer Job");
        }
    }
}
