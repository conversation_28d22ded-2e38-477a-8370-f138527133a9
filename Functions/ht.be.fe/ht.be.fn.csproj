﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="bin\**" />
    <EmbeddedResource Remove="bin\**" />
    <None Remove="bin\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="AVProcessor.cs" />
    <Compile Remove="IotEventTrigger.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
    <PackageReference Include="Curiosity.runtime.osx.10.10-arm64.CoreCompat.System.Drawing" Version="6.0.5.4" />
    <PackageReference Include="ereno.linux-x64.eugeneereno.System.Drawing" Version="*******" />
    <PackageReference Include="GemBox.Document" Version="35.0.1488-hotfix" />
    <PackageReference Include="HarfBuzzSharp" Version="*******" />
    <PackageReference Include="Hl7.Fhir.R4" Version="5.5.1" />
	  <PackageReference Include="ht.data.common" Version="1.0.2-250610141501" />
	  <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" Version="*******" />

    <PackageReference Include="HtmlAgilityPack" Version="1.12.0" />

    <PackageReference Include="HttpMultipartParser" Version="9.0.0" />
    <PackageReference Include="libphonenumber-csharp" Version="8.13.23" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.9" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.3.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.EventGrid" Version="3.4.4" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.EventHubs" Version="6.3.5" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.ServiceBus" Version="5.16.5" />
   <!--<PackageReference Include="Microsoft.Identity.Client" Version="4.63.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.0.1" />-->
    <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.2.0" />
    <PackageReference Include="NAudio" Version="2.2.1" />
    <PackageReference Include="NAudio.WaveFormRenderer" Version="2.0.0" />
    <PackageReference Include="OpenCvSharp4" Version="4.8.0.20230708" />
    <PackageReference Include="OpenCvSharp4.Extensions" Version="4.8.0.20230708" />
    <PackageReference Include="OpenCvSharp4.runtime.win" Version="4.8.0.20230708" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.88.6" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.Diagnostics.Process" Version="4.3.0" />
    <PackageReference Include="System.Drawing.Common" Version="10.0.0-preview.2.25163.9" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="8.1.2" />
    <!--<PackageReference Include="System.Text.Json" Version="8.0.4" />-->
    <PackageReference Include="xFFmpeg.NET" Version="7.1.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\ht.common.backend.models\ht.common.backend.shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="ffmpeg\linux\ffmpeg.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="ffmpeg\x64\ffmpeg.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="ffmpeg\x86\ffmpeg.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
