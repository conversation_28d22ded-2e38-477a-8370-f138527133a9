using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Linq;
using PeterPiper.Hl7.V2.Model;
using ht.common.backend.shared.models.hl7;
using ht.be.fn.Utils;
using ht.be.fn.Helpers;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;
using System.Text;

namespace ht.be.fn
{
    public static class ReceiveHl7Acknowledgment
    {
        [FunctionName("ReceiveHL7Acknowledgment")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {

            try
            {
                log.LogInformation("C# HTTP trigger function processed a request.");



                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

                // Deserialize the JSON data
                JObject jsonObject = JObject.Parse(requestBody);

                // Access the content field
                byte[] base64Content =  Convert.FromBase64String(jsonObject["message"]["$content"].ToString());
                

                if (string.IsNullOrEmpty(requestBody))
                {
                    throw new Exception("no body found for response");
                }
                string message = Encoding.UTF8.GetString(base64Content);


                #region save to db
                IMessage hl7Message = Creator.Message(message.Trim());

                var hl7Response = new
                {

                    AcknowledgmentMessage = hl7Message.ToString(),
                    MessageID = hl7Message.Segment("MSA").Field(2).AsStringRaw,
                    AcknowledgmentCode = hl7Message.Segment("MSA").Field(1).AsStringRaw,
                    ErrorMessage = hl7Message.Segment("MSA").Field(3).AsStringRaw,
                    AcknowledgmentTimestamp = hl7Message.MessageCreationDateTime.UtcDateTime,
                    SendingApplication = hl7Message.Segment("MSH").Field(3).AsStringRaw,
                    SendingEDI = hl7Message.Segment("MSH").Field(4).AsStringRaw,
                    ReceivingApplication = hl7Message.Segment("MSH").Field(5).AsStringRaw,
                    ReceivingEDI = hl7Message.Segment("MSH").Field(6).AsStringRaw,
                    Status = "Received"
                };

                var json = HTJsonSerialiser.Serialise(hl7Response);
                var res = await DBHelper.ExecSprocWithJsonAsync("dbo.upsertHl7acknowledgemet", json);
                #endregion


                return new OkObjectResult("Acknowledgment Received");
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        static string RemoveSpecialCharacters(string value, char[] specialCharacters)
        {
            return new String(value.Except(specialCharacters).ToArray());
        }
    }
}
