using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;
using System.Security.Cryptography;
using System.Web;
using ht.data.common.Extensions;

namespace ht.be.fn
{
    public static class mCareGetLoginDetails
    {
        [FunctionName("mCareGetLoginDetails")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            log.LogInformation("C# HTTP trigger function processed a request.");
            var user = "<EMAIL>";
            var pass = "!HealthTeams.123!";
            var nonce = GetNonce();
            var ts = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();

            var strToHash = $"{pass}{ts}.{nonce}{pass}";
            var hash = GetSha256Hash(strToHash);
            var res = new
            {
                userid = user,
                nonce = nonce,
                hash = hash,
                client = "ht-services",
                ts = ts,
                opt = ""
            };

            var call = new { method="Login", data=res};
            var callStr = HTJsonSerialiser.Serialise(call);
            var result =HttpUtility.UrlEncode($"{callStr}");

            return new OkObjectResult(callStr);
        }

        private static readonly Random Random = new Random();
        public static string GetNonce()
        {
            var bytes = new byte[48];
            Random.NextBytes(bytes);
            var result = Convert.ToBase64String(bytes);
            return result;
        }

        public static String GetSha256Hash(string value)
        {
            StringBuilder Sb = new StringBuilder();

            using (var hash = SHA256.Create())
            {
                Encoding enc = Encoding.UTF8;
                byte[] result = hash.ComputeHash(enc.GetBytes(value));

                foreach (byte b in result)
                    Sb.Append(b.ToString("x2"));
            }

            return Sb.ToString();
        }
    }
}
