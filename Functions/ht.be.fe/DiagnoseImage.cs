using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Net.Http;
using System.Drawing;
using ht.data.common.Wounds;
using ht.be.fn.Helpers;

namespace ht.be.fn
{
    public static class DiagnoseImage
    {
        [FunctionName("DiagnoseImage")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = null)] HttpRequestMessage req,
            ILogger log)
        {
            var br = new WoundDiagResponse
            {
                Images = new System.Collections.Generic.List<ImageDiag>(),
                Status = "Success"
            };
            log.LogInformation("ImageDiagnose - starting");

            var res = await req.Content.ReadAsAsync<WoundDiagRequest>();
            br.Images = res.Images; // capture
            br.Images = ImageDiagnosticsHelper.ProcessImages(res.Images,res.Detailed);
            return new OkObjectResult(br);

        }
    }
}
