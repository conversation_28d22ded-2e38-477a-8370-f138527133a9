using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Hl7.Fhir.Rest;
using ht.be.fn.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Host;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace ht.be.fn
{
    public class MedtechProcessor
    {
        [FunctionName("MedtechTimedProcessor")]
        // Timer trigger to run every 5 minutes
        public static void Run([TimerTrigger("0 */10 * * * *")] TimerInfo myTimer, ILogger log)
        {
            log.LogInformation($"MedtechTimedProcessor - Starting 10min Timer Job");

            try
            {
                // Get a list of all facilities that integrate with Medtech
                var facilityMappings = PartnerIntegrationHelper.GetFacilityPartnerMappingBySourceId("*", "Medtech").GetAwaiter().GetResult();
                if (facilityMappings.Count == 0)
                {
                    log.LogInformation("No Medtech mapped facilities found.");
                    return;
                }

                var helper = new MedtechIntegrationHelper(log);

                // Loop through each medtech facility defined in our system
                foreach (var facilityMapping in facilityMappings)
                {
                    log.LogInformation($"Processing providers for Medtech facility: {facilityMapping.FacilityId} - {facilityMapping.ExPartnerMapId}");

                    // Get a list of all providers in Medtech at the facility and upsert them into the database
                    var result = helper.GetAllProvidersFromMedtech(facilityMapping).GetAwaiter().GetResult();
                }

                // Loop again through each medtech facility defined in our system
                foreach (var facilityMapping in facilityMappings)
                {
                    log.LogInformation($"Processing appointments for Medtech facility: {facilityMapping.FacilityId} - {facilityMapping.ExPartnerMapId}");

                    // Get mapping details for the users of this facility
                    var facilityUserMappings = PartnerIntegrationHelper.GetFacilityPartnerMappingBySourceId(facilityMapping.ExPartnerMapId, "Medtech").GetAwaiter().GetResult();

                    // Loop through all provider users in this facility that are mapped to Medtech
                    foreach (var userMapping in facilityUserMappings)
                    {
                        // Skip if the entity type is a resident
                        if (userMapping.EntityType.ToLower() == "resident")
                            continue;

                        log.LogInformation($"Processing Medtech provider: {userMapping.EntityFullName} - {userMapping.SourceId} - {userMapping.EntityType}");

                        helper.GetAppointments(userMapping).GetAwaiter().GetResult();
                    }

                }

                log.LogInformation($"MedtechTimedProcessor - Completed Timer Job");
                return;
            }
            catch (Exception ex)
            {
                log.LogError($"MedtechTimedProcessor - Error: {ex.Message}");
            }
        }
    }
}
