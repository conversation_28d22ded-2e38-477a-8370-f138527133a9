using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Net.Http;
using System.Text;
using System.Linq;
using ht.be.fn.Helpers;

namespace ht.be.fn
{
    public static class MbsProcessor
    {
        [FunctionName("MbsProcessor")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = null)] HttpRequest req,
            ILogger log)
        {
            string runId = req.Query["runId"];
            log.LogInformation($"Running process MBS with {runId}");

            var baseUrl = Environment.GetEnvironmentVariable("MbsUrl");
            var dlPage = await new HttpClient().GetStringAsync(baseUrl + "downloads");
            var page = new HtmlAgilityPack.HtmlDocument();

            page.LoadHtml(dlPage);

            var sb = new StringBuilder();
            var links = page.DocumentNode.SelectNodes("//div[@id='read']//a");
            if (links?.Count > 0)
            {
                log.LogInformation($"There are {links?.Count} links");

                var l = (from lnk in links
                         where lnk.InnerText.ToLower().Contains("downloads page")
                         select lnk).FirstOrDefault();

                var dlUrl = baseUrl + l.GetAttributeValue("href", "");
                sb.AppendLine(dlUrl);

                dlPage = await new HttpClient().GetStringAsync(dlUrl);

                page.LoadHtml(dlPage);
                links = page.DocumentNode.SelectNodes("//a");
                l = (
                        from lnk in links
                        where lnk.InnerText.Contains("XML")
                        select lnk).FirstOrDefault();
                var xmlLink = baseUrl + l.GetAttributeValue("href", "");

                var mbs = new MbsHelper(log);
                var xdoc = await mbs.GetMbsXmlDocument(xmlLink);

                await mbs.ProcessMBSItems(xdoc);

                sb.AppendLine($"Processed {xdoc?.DocumentElement?.ChildNodes?.Count}");

            }
            return new OkObjectResult(sb.ToString());
        }
    }
}
