using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;
using System.Drawing.Text;

namespace ht.be.fn
{
    public static class diagInfo
    {
        [FunctionName("diagInfo")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = null)] HttpRequest req,
            ILogger log)
        {
            try
            {
                var sb = new StringBuilder();
                //var instFonts = new InstalledFontCollection();
                //foreach (var f in instFonts.Families)
                //    sb.AppendLine(f.Name);
                string date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                sb.AppendLine($"Function executed at: {date}");

                return new OkObjectResult(sb.ToString());
            }
            catch (Exception ex)
            {
                return new OkObjectResult(ex.Message);
            }
        }
    }
}
