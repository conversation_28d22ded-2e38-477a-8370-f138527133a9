// Default URL for triggering event grid function in the local environment.
// http://localhost:7071/runtime/webhooks/EventGrid?functionName={functionname}
using System;
using ht.be.fn.Helpers;
using Microsoft.Azure.EventGrid.Models;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.EventGrid;
using Microsoft.Azure.WebJobs.Host;

using Microsoft.Extensions.Logging;

namespace ht.be.fn
{
    public class IotEventTrigger
    {
        private readonly ILogger<IotEventTrigger> _logger;

        public IotEventTrigger(ILogger<IotEventTrigger> log)
        {
            _logger = log;
        }

        [FunctionName("IotEventTrigger")]
        public void Run([EventGridTrigger]EventGridEvent eventGridEvent)
        {
            var msg = eventGridEvent?.Data?.ToString();
            DBHelper.ExecSproc("sp_tblMonitoringData_Upsert", msg);
            _logger.LogInformation(eventGridEvent.Data.ToString());

        }
    }
}
