using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using ht.be.fn.Helpers;

namespace ht.be.fn
{
    public static class PbsProcessor
    {
        [FunctionName("PbsProcessor")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            try
            {
                string runId = req.Query["runId"];
                log.LogInformation($"Running process PBS with {runId}");

                var url = Environment.GetEnvironmentVariable("pbsUrl");
                var dlPage = await new HttpClient().GetStringAsync(url);
                var page = new HtmlAgilityPack.HtmlDocument();

                page.LoadHtml(dlPage);

                var links = page.DocumentNode.SelectNodes("//a[contains(@title,'PBS XML V3 file')]");
                if (links?.Count > 0)
                {
                    Console.WriteLine($"There are {links?.Count} links");

                    var dlFile = "https://www.pbs.gov.au" + links[0].GetAttributeValue("href", "");
                    var pbsHelper = new PbsHelper();
                    var xdoc = await pbsHelper.GetPBSMedicinesDocument(dlFile);

                    var res = await pbsHelper.ProcessOrganisations(xdoc);
                    res = await pbsHelper.ProcessDrugsList(xdoc);
                    res = await pbsHelper.ProcessPharmaList(xdoc);



                }
                return new OkObjectResult(new { });
            }

            catch (Exception ex)
            {
                return new OkObjectResult(ex.Message);
            }
        }
    }
}
