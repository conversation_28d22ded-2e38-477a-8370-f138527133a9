using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.be.fn.Helpers;
using ht.common.backend.shared.helpers;
using ht.be.fn.Utils;
using ht.data.common.Telehealth;
using System.Net.Http;
using ht.common.backend.shared.classes;
using Microsoft.Identity.Client;

namespace ht.be.fn
{
    public static class TestCaller
    {
        [FunctionName("TestCaller")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req,
            ILogger log, ExecutionContext ctx)
        {
            try
            {
                // Get the outbound IP address of the function app
                using (var httpClient = new HttpClient())
                {
                    var ip = await httpClient.GetStringAsync("https://api.ipify.org");
                    return new OkObjectResult($"Outbound IP: {ip}");
                }

                string version = "2025.01.19.002";
                return new OkObjectResult(version);

                return MediaFileTester(log);
                
                //test measurements
                var userUri = IotHubEventProcessor.UserBlobUri;
                var uploadUri = "https://htappuat.blob.core.windows.net/uploads/testDevice";

                var json = "{\"exTransactionId\":\"9b395ab8-a50e-41b0-8aca-91ac311be901\",\"userExId\":\"DC81F83D-A9CD-4CC5-A4BC-3D6EED4B9092\",\"exTaskId\":\"00000000-0000-0000-0000-000000000000\",\"dateCreatedUtc\":\"2022-08-14T01:37:11.701704Z\",\"readings\":[{\"readDateUtc\":\"2022-08-14T01:37:11.701732Z\",\"itemType\":\"BPSystolic\",\"iconUrl\":\"https://htappuat.blob.core.windows.net/pub/readings/icons/BPSystolic.png\",\"readValue\":\"130\",\"readUnits\":\"mmHg\",\"readDeviceId\":\"\",\"readStatus\":\"Sitting\",\"isManual\":true},{\"readDateUtc\":\"2022-08-14T01:37:11.701732Z\",\"itemType\":\"BPDiastolic\",\"iconUrl\":\"https://htappuat.blob.core.windows.net/pub/readings/icons/BPDiastolic.png\",\"readValue\":\"85\",\"readUnits\":\"mmHg\",\"readDeviceId\":\"\",\"readStatus\":\"Sitting\",\"isManual\":true},{\"readDateUtc\":\"2022-08-14T01:37:11.701732Z\",\"itemType\":\"HR\",\"iconUrl\":\"https://htappuat.blob.core.windows.net/pub/readings/icons/HR.png\",\"readValue\":\"79\",\"readUnits\":\"Bpm\",\"readDeviceId\":\"\",\"assessColour\":\"#22DC04\",\"isAlert\":false,\"isAbnormal\":false,\"isManual\":true,\"lowCriticalValue\":50,\"highCriticalValue\":120,\"lowWarningValue\":60,\"highWarningValue\":100},{\"readDateUtc\":\"2022-08-14T01:37:11.701732Z\",\"itemType\":\"SpO2\",\"iconUrl\":\"https://htappuat.blob.core.windows.net/pub/readings/icons/SpO2.png\",\"readValue\":\"94\",\"readUnits\":\"%\",\"readDeviceId\":\"\",\"isManual\":true,\"lowCriticalValue\":90,\"lowWarningValue\":93},{\"readDateUtc\":\"2022-08-14T01:37:11.701732Z\",\"itemType\":\"Temp\",\"iconUrl\":\"https://htappuat.blob.core.windows.net/pub/readings/icons/Temp.png\",\"readValue\":\"36.5\",\"readUnits\":\"C\",\"readDeviceId\":\"\",\"assessColour\":\"#22DC04\",\"isAlert\":false,\"isAbnormal\":false,\"isManual\":true,\"lowCriticalValue\":35,\"highCriticalValue\":38,\"lowWarningValue\":35.5,\"highWarningValue\":37.5},{\"readDateUtc\":\"2022-08-14T01:37:11.701732Z\",\"itemType\":\"Glucose\",\"iconUrl\":\"https://htappuat.blob.core.windows.net/pub/readings/icons/Glucose.png\",\"readValue\":\"4.4\",\"readUnits\":\"mmo/L\",\"readDeviceId\":\"\",\"readStatus\":\"\",\"assessColour\":\"#22DC04\",\"isAlert\":false,\"isAbnormal\":false,\"isManual\":true,\"lowCriticalValue\":3.5,\"highCriticalValue\":6.9,\"lowWarningValue\":4,\"highWarningValue\":5.5},{\"readDateUtc\":\"2022-08-14T01:37:11.701732Z\",\"itemType\":\"Weight\",\"iconUrl\":\"https://htappuat.blob.core.windows.net/pub/readings/icons/Weight.png\",\"readValue\":\"65.8\",\"readUnits\":\"Kg\",\"readDeviceId\":\"\",\"assessColour\":\"#22DC04\",\"isAlert\":false,\"isAbnormal\":false,\"isManual\":true,\"lowCriticalValue\":65,\"highCriticalValue\":67,\"lowWarningValue\":64,\"highWarningValue\":66}],\"thresholds\":{\"userExId\":\"DC81F83D-A9CD-4CC5-A4BC-3D6EED4B9092\",\"residentName\":\"Sarah Jones\",\"facilityName\":\"Care Home #2 Test facility\",\"limits\":[{\"itemType\":\"BPSystolic\",\"itemSubType\":\"Laying\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"mmHg\",\"lowCriticalValue\":110,\"highCriticalValue\":140,\"lowWarningValue\":100,\"highWarningValue\":130,\"expected\":120,\"lastReading\":125,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"BPDiastolic\",\"itemSubType\":\"Laying\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"mmHg\",\"lowCriticalValue\":80,\"highCriticalValue\":160,\"lowWarningValue\":90,\"highWarningValue\":140,\"expected\":120,\"lastReading\":82,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"BPSystolic\",\"itemSubType\":\"Sitting\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"mmHg\",\"lowCriticalValue\":90,\"highCriticalValue\":150,\"lowWarningValue\":100,\"highWarningValue\":140,\"expected\":120,\"lastReading\":125,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"BPDiastolic\",\"itemSubType\":\"Sitting\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"mmHg\",\"lowCriticalValue\":50,\"highCriticalValue\":100,\"lowWarningValue\":60,\"highWarningValue\":90,\"expected\":80,\"lastReading\":82,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"BPSystolic\",\"itemSubType\":\"Standing\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"mmHg\",\"lowCriticalValue\":90,\"highCriticalValue\":150,\"lowWarningValue\":100,\"highWarningValue\":140,\"expected\":120,\"lastReading\":125,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"BPDiastolic\",\"itemSubType\":\"Standing\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"mmHg\",\"lowCriticalValue\":50,\"highCriticalValue\":100,\"lowWarningValue\":60,\"highWarningValue\":90,\"expected\":80,\"lastReading\":82,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"Glucose\",\"itemSubType\":\"Fasting\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"mmo/L\",\"lowCriticalValue\":3.5,\"highCriticalValue\":6.9,\"lowWarningValue\":4,\"highWarningValue\":5.5,\"expected\":5.5,\"lastReading\":5.7,\"readingdDate\":\"2022-07-29T01:02:14.781746Z\",\"isSelected\":false},{\"itemType\":\"Glucose\",\"itemSubType\":\"Not Fasting\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"mmo/L\",\"lowCriticalValue\":3.5,\"highCriticalValue\":11,\"lowWarningValue\":4,\"highWarningValue\":5.5,\"expected\":5.5,\"lastReading\":5.7,\"readingdDate\":\"2022-07-29T01:02:14.781746Z\",\"isSelected\":false},{\"itemType\":\"Temp\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"C\",\"lowCriticalValue\":35,\"highCriticalValue\":38,\"lowWarningValue\":35.5,\"highWarningValue\":37.5,\"expected\":37,\"lastReading\":36.7,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"HR\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"Bpm\",\"lowCriticalValue\":50,\"highCriticalValue\":120,\"lowWarningValue\":60,\"highWarningValue\":100,\"expected\":72,\"lastReading\":74,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"SpO2\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"%\",\"lowCriticalValue\":90,\"lowWarningValue\":93,\"expected\":98,\"lastReading\":98,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"UA\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"Y/N\",\"highCriticalValue\":1,\"expected\":0,\"readingdDate\":\"2022-08-14T01:34:43.068443Z\",\"isSelected\":false},{\"itemType\":\"Weight\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"Kg\",\"lowCriticalValue\":65,\"highCriticalValue\":67,\"lowWarningValue\":64,\"highWarningValue\":66,\"expected\":65.3,\"lastReading\":91,\"readingdDate\":\"2022-08-10T06:08:13.005477Z\",\"isSelected\":false},{\"itemType\":\"Ear\",\"ageLow\":1,\"ageHigh\":120,\"readingdDate\":\"2022-08-10T06:08:13.005477Z\",\"isSelected\":false},{\"itemType\":\"Throat\",\"ageLow\":1,\"ageHigh\":120,\"readingdDate\":\"2022-08-10T06:08:13.005477Z\",\"isSelected\":false},{\"itemType\":\"Heart\",\"ageLow\":1,\"ageHigh\":120,\"readingdDate\":\"2022-08-10T06:08:13.005477Z\",\"isSelected\":false},{\"itemType\":\"Lungs\",\"ageLow\":1,\"ageHigh\":120,\"isSelected\":false},{\"itemType\":\"Cough\",\"ageLow\":1,\"ageHigh\":120,\"uom\":\"Y/N\",\"highCriticalValue\":1,\"expected\":0,\"isSelected\":false}]},\"alertNotifications\":{\"exTaskId\":\"00000000-0000-0000-0000-000000000000\",\"residentName\":\"Sarah Jones\",\"facilityName\":\"Care Home #2 Test facility\"}}";
                var md = HTJsonSerialiser.Deserialise<TelehealthMonitorData>(json);
            
                MonitoringThresholds th = null;
                var svcUser = Utils.Utilities.svc_acct_UserExId;
                var usrSecurity = Utilities.GetJsonSecurity();

                if (string.IsNullOrEmpty(md.ExTaskId))
                    md.ExTaskId = System.Guid.Empty.ToString();


                var msg = HTJsonSerialiser.Serialise(md);
                var res = DBHelper.ExecSprocByParams("sp_MonitorData_Get_Threshholds_ByTaskIdOrResidentId",
                        new System.Collections.Generic.Dictionary<string, string>
                        {
                                {"id",md.ExTaskId},{"exResidentId",md.UserExId},{"jsonSecurity",usrSecurity }
                        });

                if (string.IsNullOrEmpty(res))
                {
                    log.LogWarning($"MonitoringDataProcessor - no Thresholds found for Resident - {md?.UserExId} and TaskId - {md?.ExTaskId}");
                    DBHelper.ExecSproc("sp_MonitorData_Upsert", msg);
                    throw new HttpRequestException($"Unable to fetch Threshold values for data - {msg}");

                }
                th = HTJsonSerialiser.Deserialise<MonitoringThresholds>(res);
                md.Thresholds = th;
                log.LogInformation($"MonitoringDataProcessor - Thresholds obtained Resident-{md.UserExId} Task - {md.ExTaskId}");

                var rdr = new AssessReadings(log);
                var ctxMonitor = new MonitoringDataProcessorContext
                {
                    UploadUri = uploadUri,
                    ProcessFiles = true,
                };
                var man = rdr.AssessTelehealthMonitorData(md, userUri, ctxMonitor);
                log.LogInformation($"MonitoringDataProcessor - Assessments done -{md.UserExId} Task - {md.ExTaskId}");
                msg = HTJsonSerialiser.Serialise(md);
                //var results = DBHelper.ExecSprocWithJsonAsync("sp_MonitorData_Upsert", msg, usrSecurity).GetAwaiter().GetResult();
                //log.LogInformation($"MonitoringDataProcessor - Results Saved -{htMonitorData.UserExId} Task - {htMonitorData.ExTaskId}");
                        
                return new OkObjectResult(md);
            }
            catch (Exception ex)
            {
                log?.LogError(ex.Message + ex.StackTrace);
                //throw ex; 
                return (null);
            }

        }

        private static IActionResult MediaFileTester(ILogger _log)
        {
            HealthTeamsFile props = null;

            //UPLOADS - var url = "********************************************************************************************************************************************************************";
            var url = "**********************************************************************************************************************************************************************************************";
            var _usersStorageUrl = "*****************************************"; // Environment.GetEnvironmentVariable("BlobUsersUrl");
            var _uploadsSasToken = "sp=racwdli&st=2023-05-09T12:07:00Z&se=2032-10-04T20:07:00Z&sv=2022-11-02&sr=c&sig=dPue7Gd83tijmXou4umVgu%2Fxo7h0N36Wh4jGsGQohGw%3D"; // Environment.GetEnvironmentVariable("BlobUploadsSasToken");
            var _usersSasToken = "sp=racwdli&st=2023-05-09T12:03:50Z&se=2032-10-04T20:03:50Z&sv=2022-11-02&sr=c&sig=%2BaRejmsUU%2FNTw8exKti8FlmQr%2Bun6MgTNOTqLMGTu5s%3D"; // Environment.GetEnvironmentVariable("BlobUsersSasToken");
            var _storageAccount = "htapp"; // Environment.GetEnvironmentVariable("IotStorageAccountName");
            var _storageKey = "****************************************************************************************"; // Environment.GetEnvironmentVariable("IotStorageAccountKey");

            var bHelper = new BlobHelper(_storageAccount, _storageKey);

            //var props = bHelper.GetBlobMetadata(url).GetAwaiter().GetResult();

            //Check for file info
            var mh = new MediaHelper
            {
                _accountKey= _storageKey,
                _accountName = _storageAccount,
                _uploadsSasToken = _uploadsSasToken,
                _userSasToken = _usersSasToken,
                _usersUrl = _usersStorageUrl,
                _bHelper = new BlobHelper(_storageAccount, _storageKey),
                _log = _log

            };
            
            
            if (mh.IsMediaFile(url))
            {
                props = mh.CreateAndStoreThumbnail(url).GetAwaiter().GetResult();
            }


            return new OkObjectResult(props);
        }
    }
}
