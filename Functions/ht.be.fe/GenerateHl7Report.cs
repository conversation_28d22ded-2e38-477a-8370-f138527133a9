using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.be.fn.Helpers;
using ht.be.fn.Utils;
using ht.common.backend.shared.models;
using ht.data.common.Telehealth;
using System.Collections.Generic;
using ht.common.backend.shared.models.hl7;

namespace ht.be.fn
{
    public static class GenerateHl7Report
    {
        [FunctionName("GenerateHl7Report")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

            if (string.IsNullOrEmpty(requestBody))
            {
                throw new Exception("no body found for response");
            }
            var request = HTJsonSerialiser.Deserialise<Hl7Request>(requestBody);

            if (string.IsNullOrEmpty(request.ExResidentId))
            {
                throw new Exception("no resident found");

            }

            log.LogInformation("GenerateNoteReport function processed a request.");

            byte[] pdfDoc = await GenerateReport(log, request);

            return new FileContentResult(pdfDoc, "application/pdf");

        }

        static async Task<List<TelehealthProgressNote>> FetchNote(ILogger log, Hl7Request req)
        {
            log.LogInformation($"Executing procedure sp_Get_Notes_by_UserexId to fetch notes: ExResidentId: {HTJsonSerialiser.Serialise(req)}");

            GetNotesResponse br = new();
            try
            {
                var usrSecurity = Utilities.GetJsonSecurity();
                var res = string.Empty;
                if (req.IsAllProgressNotes)
                {
                    var json = HTJsonSerialiser.Serialise(new { ExResidentId = req.ExResidentId });

                    res = await DBHelper.ExecSprocWithJsonAsync("sp_Get_Notes_by_UserexId", json, usrSecurity);
                }
                else
                {
                    var json = HTJsonSerialiser.Serialise(req);

                    res = await DBHelper.ExecSprocWithJsonAsync("sp_Get_Resident_Notes_By_ExNoteId", json);

                }
                br.Notes = HTJsonSerialiser.Deserialise<List<TelehealthProgressNote>>(res);

                log.LogInformation("JSON data" + JsonConvert.SerializeObject(br.Notes));

                return br.Notes;
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return null;
            }
        }

        static async Task<byte[]> GenerateReport(ILogger log, Hl7Request req)
        {
            log.LogInformation($"GenerateReport running for {HTJsonSerialiser.Serialise(req)}");

            var pn = await FetchNote(log, req);

            //Report Generation
            var pdf = PDFManager.GetInstance(log);
            var document = await pdf.GetHl7Report(pn,req);

            return document;
        }

    }
}
