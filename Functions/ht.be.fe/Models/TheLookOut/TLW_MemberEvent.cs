﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.fn.Models.TheLookOut
{
    public class TLW_MemberEvent
    {
        public int event_id { get; set; }
        public string event_type { get; set; }
        public int source_id { get; set; }
        public ClientData data { get; set; }
    }
        public class ClientData
        {
            public int id { get; set; }
            public object commenced_on { get; set; }
            public object signed_up_on { get; set; }
            public object archive_reason { get; set; }
            public object archived_at { get; set; }
            public DateTime created_at { get; set; }
            public DateTime updated_at { get; set; }
            public object next_review_on { get; set; }
            public Plan plan { get; set; }
            public object auth_rep { get; set; }
            public object emergency_contact { get; set; }
            public Community community { get; set; }
            public object funder { get; set; }
            public object primary_contact { get; set; }
            public Profile profile { get; set; }
            public List<object> requisites { get; set; }
            public List<CustomAttribute> custom_attributes { get; set; }
            public List<object> sharers { get; set; }
            public List<object> billing_recipients { get; set; }
            public List<object> matching_preferences { get; set; }
            public List<object> hcp_package_levels { get; set; }
            public List<object> supplement_and_fees { get; set; }
        }

        public class Plan
        {
            public int id { get; set; }
            public string name { get; set; }
            public Price care_management_price { get; set; }
            public Price package_management_price { get; set; }
            public string payment_frequency { get; set; }
        }

        public class Price
        {
            public int cents { get; set; }
            public string currency { get; set; }
            public string formatted { get; set; }
        }

        public class Community
        {
            public int id { get; set; }
            public string name { get; set; }
            public DateTime updated_at { get; set; }
            public DateTime created_at { get; set; }
        }

        public class Profile
        {
            public int id { get; set; }
            public string uuid { get; set; }
            public string email { get; set; }
            public string full_name { get; set; }
            public string preferred_name { get; set; }
            public string given_names { get; set; }
            public string family_name { get; set; }
            public DateTime created_at { get; set; }
            public DateTime updated_at { get; set; }
            public string telephone1 { get; set; }
            public string telephone2 { get; set; }
            public string date_of_birth { get; set; }
            public Address address { get; set; }
            public object[] custom_attributes { get; set; }
            public string avatar_url { get; set; }
            public string time_zone { get; set; }
        }

        public class Address
        {
            public string street1 { get; set; }
            public string street2 { get; set; }
            public string locality { get; set; }
            public string region { get; set; }
            public string postcode { get; set; }
        }

        public class CustomAttribute
        {
            public int id { get; set; }
            public string name { get; set; }
            public object value { get; set; }
            public string key { get; set; }
            public string field_type { get; set; }
            public bool required { get; set; }
            public bool unique { get; set; }
        }

    }