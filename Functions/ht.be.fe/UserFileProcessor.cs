using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using ht.common.backend.shared.helpers;

namespace ht.be.fn
{
    public static class UserFileProcessor
    {
        [FunctionName("UserFileProcessor")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function,"post", Route = null)] HttpRequest req,
            ILogger log)
        {
            var bProfilePic = false;
            
            try
            {
                var uploadsSasToken = Environment.GetEnvironmentVariable("BlobUploadsSasToken");
                var usersStorageUrl = Environment.GetEnvironmentVariable("BlobUsersUrl");
                var usersSasToken = Environment.GetEnvironmentVariable("BlobUsersSasToken");
                var storageAccount = Environment.GetEnvironmentVariable("IotStorageAccountName");
                var storageKey = Environment.GetEnvironmentVariable("IotStorageAccountKey");
                var userUrl = string.Empty;

                var props = new Dictionary<string, string>();

                var userId = req?.Query["userid"].ToString();
                var fname = req?.Query["fname"].ToString();
                var ftype = req?.Query["ftype"].ToString();

                log.LogInformation($"UserFileProcessor userId-{userId} fname-{fname} ftype-{ftype}");

                var ms = new MemoryStream();
                await req.Body.CopyToAsync(ms);
                var bytes = ms.ToArray();

                var bHelper = new BlobHelper(storageAccount, storageKey);
                var contentType = Utils.Utilities.GetMimeType(System.IO.Path.GetFileName(fname));
               
                if (ftype=="profile")
                {
                    fname = "pic.png";
                    bProfilePic = true;
                    userUrl = $"{usersStorageUrl}/{userId?.ToUpper()}/profile/{fname}";
                }
                if (ftype == "logo")
                {
                    fname = "logo.png";
                    bProfilePic = true;
                    userUrl = $"{usersStorageUrl}/{userId?.ToUpper()}/profile/{fname}";
                }

                if (!string.IsNullOrEmpty(userUrl))
                {
                    bHelper.SaveBlob(userUrl, usersSasToken, bytes, props).GetAwaiter().GetResult();
                    if (bProfilePic)
                    {
                        var sas = bHelper.GetSasToken(userUrl, int.MaxValue).GetAwaiter().GetResult();
                        userUrl += "?" + sas;
                        Utils.Utilities.UpdateProfile(userId, userUrl,ftype);
                    }
                }

                return new OkObjectResult(userUrl);
                
            }
            catch (Exception ex)
            {
                log.LogError(ex, "UserFileProcessor");
                return new BadRequestObjectResult($"ERROR:{ex.Message}");
            }
        }
    }
}
