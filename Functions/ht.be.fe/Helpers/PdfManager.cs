﻿
using GemBox.Document;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using ht.data.common.Telehealth;
using System.Net.Http;
using ht.common.backend.shared.helpers;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Reflection.Metadata;
using System.Web;
using System.Net;
using System.Text.RegularExpressions;
using GemBox.Document.Tables;
using ht.common.backend.shared.models.hl7;

namespace ht.be.fn.Helpers
{
    public class PDFManager
    {
        public static string StorePath;// @"https://htappuat.blob.core.windows.net/pub/docs";
        public static string DocReport = "HealthTeamsDiagnosticReport.docx";
        public static string DocProgressNote = "HealthTeamsProgressNote.docx";
        public static string DocHl7Report = "HealthTeamsHl7Report.docx";
        public static string Lic = "DN-2022Apr25-vBukBWLH2kUR0fYZpMmHgyRgz/bOjbSxacxUUxcg/yigwwASdxVI8QWZsxysiTL4MCmi9ww9yroVHEn3awQX6Xivs2w==A";

        public static string AccountName;
        public static string AccountKey;
        public static string UsersUrl;
        public static string UserSasToken;

        private ILogger log;

        static PDFManager()
        {
            ComponentInfo.SetLicense(Lic);
            AccountName = Environment.GetEnvironmentVariable("IotStorageAccountName");
            AccountKey = Environment.GetEnvironmentVariable("IotStorageAccountKey");
            UsersUrl = Environment.GetEnvironmentVariable("BlobUsersUrl");
            UserSasToken = Environment.GetEnvironmentVariable("BlobUsersSasToken");
            StorePath = Environment.GetEnvironmentVariable("StorePath");


        }
        public PDFManager(ILogger _log)
        {
            log = _log;
        }

        public static PDFManager GetInstance(ILogger _log)
        {
            return new PDFManager(_log);
        }

        public string GetDocsPath()
        {
            return ($"{StorePath}/docs/");
        }

        public static string GetReportPath()
        {
            return $"{StorePath}/{DocReport}";
        }

        public Stream GetReportDocStream(string docName)
        {
            var url = $"{StorePath}/{docName}";
            var ms = new MemoryStream();
            using (var c = new HttpClient())
            {
                var stream = new HttpClient().GetStreamAsync(url).GetAwaiter().GetResult();
                stream.CopyTo(ms);
                ms.Position = 0;
            }
            return (ms);
        }
        public async Task<string> GenerateMonitoringReport(TelehealthMonitorData md)
        {
            try
            {
                log.LogInformation($"Starting the GenerateMonitoringData Report");
                var bHelper = new BlobHelper(AccountName, AccountKey);
                var doc = await GenerateMonitoringResults(md);
                var dt = System.DateTime.UtcNow.ToString("yyyyMMdd-hhmm");
                var fname = $"{md.UserExId?.ToUpper()}/reports/HealthTeamsReport-{md.ExTaskId}-{dt}.pdf";

                var ms = new MemoryStream();
                doc.Save(ms, SaveOptions.PdfDefault); // save to string.
                var url = $"{UsersUrl}/{fname}";
                if (await bHelper.SaveBlob(url, UserSasToken, ms.ToArray(), new Dictionary<string, string>()))
                {
                    var sas = bHelper.GetSasToken(url, int.MaxValue).GetAwaiter().GetResult();
                    url += "?" + sas;
                }
                log.LogInformation($"Completed.");
                return (url);
            }
            catch (Exception ex)
            {
                log.LogError(ex, "ERROR: GenerateMonitoringData failed");
                throw;
            }

        }
        public async Task<byte[]> GetProgressNoteReport(List<TelehealthProgressNote> data)
        {
            log.LogInformation($"Starting the GetProgressNote Report");
            var doc = await GenerateProgressNoteResult(data);
            var ms = new MemoryStream();
            doc.Save(ms, SaveOptions.PdfDefault); // save to string.
            log.LogInformation($"Completed.");
            return (ms.ToArray());
        }
        public async Task<byte[]> GetHl7Report(List<TelehealthProgressNote> data, Hl7Request req)
        {
            log.LogInformation($"Starting the GetProgressNote Report");
            var doc = await GenerateHl7ReportResult(data, req);
            var ms = new MemoryStream();
            doc.Save(ms, SaveOptions.PdfDefault); // save to string.
            log.LogInformation($"Completed.");
            return (ms.ToArray());
        }
        public async Task<DocumentModel> GenerateMonitoringResults(TelehealthMonitorData data)
        {
            try
            {
                var stk = Stopwatch.StartNew();
                var stm = GetReportDocStream(DocReport);
                var doc = DocumentModel.Load(stm);


                #region set vitals
                var tbl = GetTable(doc, "tblVitals");
                if (tbl != null)
                {
                    foreach (var r in data?.Readings)
                    {
                        var row = new GemBox.Document.Tables.TableRow(doc);
                        tbl.Rows.Add(row);

                        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.ItemType.ToString() + "</b><br /><img src='" + r.IconUrl + "'>"));
                        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.ReadUnits + "</b>"));

                        var th = "<ul>"
                                + "<li>Low Critical: " + r.LowCriticalValue + "</li>"
                                + "<li>Low Warning: " + r.LowWarningValue + "</li>"
                                + "<li>High Warning: " + r.HighWarningValue + "</li>"
                                + "<li>High Critical: " + r.HighCriticalValue + "</li>"
                                + "</ul>";

                        if (!r.LowCriticalValue.HasValue && !r.LowWarningValue.HasValue && !r.HighCriticalValue.HasValue && !r.HighCriticalValue.HasValue)
                            th = "";

                        row.Cells.Add(AddHTMLCell(doc, th));

                        var v = "<b>" + r.ReadValue + "</b><br />" + (r.IsManual.GetValueOrDefault(false) == true ? "(manual)" : "");
                        if (string.IsNullOrEmpty(th))
                            v = "";
                        row.Cells.Add(AddHTMLCell(doc, v));

                        var txt = "Normal";
                        if (r.IsAbnormal.GetValueOrDefault(false) == true)
                            txt = "Warning";
                        if (r.IsAlert.GetValueOrDefault(false) == true)
                            txt = "Alert";


                        if (txt == "Normal" && th == "")
                            txt = "";
                        row.Cells.Add(AddHTMLCell(doc, "<b>" + txt + "</b>"));

                        row.Cells.Add(AddHTMLCell(doc, "<span style='background-color:" + r.AssessColour + ";width:100%'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>"));


                    }
                }
                #endregion

                #region set the doc properties
                var frmData = doc.Content.FormFieldsData;
                var vitals = string.Join(",", data.Readings?.Select(r => r.ItemType.ToString()));
                SetProperty<FormTextData>(frmData["HTReportNum"], "001");
                SetProperty<FormTextData>(frmData["HTResident"], data.AlertNotifications?.ResidentName);
                SetProperty<FormTextData>(frmData["HTNurse"], data.AlertNotifications?.CarerName);
                SetProperty<FormTextData>(frmData["HTFacility"], data.AlertNotifications?.FacilityName);
                SetProperty<FormTextData>(frmData["HTReadDate"], "");
                SetProperty<FormTextData>(frmData["HTVitals"], vitals);
                SetProperty<FormTextData>(frmData["HTDescription"], data.AlertNotifications?.TaskName);
                #endregion

                doc.Content.Replace("[ppp]", "001");


                return doc;
            }
            catch (Exception ex)
            {
                var x = ex.Message;
                throw;
            }
        }
        public async Task<DocumentModel> GenerateProgressNoteResult(List<TelehealthProgressNote> data)
        {
            try
            {

                var stk = Stopwatch.StartNew();
                var stm = GetReportDocStream(DocProgressNote);
                var doc = DocumentModel.Load(stm);

                //var tbl = GetTable(doc, "tblNotes");
                //if (tbl != null)
                //{
                //    foreach (var r in data)
                //    {
                //        var row = new GemBox.Document.Tables.TableRow(doc);
                //        tbl.Rows.Add(row);

                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.NoteDateUtc.Value.ToLongDateString() + "</b>"));
                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.Title + "</b>"));
                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.NoteDetails + "</b>"));
                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.SentTo + "</b>"));
                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.Doctor?.FullName + "</b>"));

                //    }
                //}

                #region section
                SpecialCharacter lineBreakElement = new SpecialCharacter(doc, SpecialCharacterType.LineBreak);
                //List<Paragraph> p = new List<Paragraph>();
                // Create a table for `NoteDetails`.
                var table = new Table(doc);
                table.TableFormat.PreferredWidth = new TableWidth(100, TableWidthUnit.Percentage);
                doc.Sections.Add(new Section(doc, table));
                var paragraphStyle = new ParagraphStyle("CustomStyle")
                {
                    CharacterFormat = new CharacterFormat()
                    {
                        Size = 10, // Set the font size to 10 points.
                        Bold = false, // Remove bold formatting.
                        Italic = false, // Remove italic formatting.
                    }
                };
                doc.Styles.Add(paragraphStyle);

                // Add paragraphs and table cells for each `data` object.
                foreach (var r in data)
                {
                    // Concatenate all the information into a single string.
                    var rowData = $" <br /> <b>Date: </b> {r.NoteDateUtc.Value.ToLongDateString()} <br /> " +
                                  $"<b>Title: </b> {r.Title} <br /> " +
                                  $"<b>Sent To: </b> {r.SentTo} <br /> " +
                                  $"<b>Created by: </b> {r.CarerName} <br /> " +
                                  $"<b>NoteDetails: </b> {r.NoteDetails} <br />";

                    // Add the concatenated string to the table cell.
                    var row = new TableRow(doc);

                    row.Cells.Add(AddHTMLCell(doc, rowData));

                    table.Rows.Add(row);
                }

                //    doc.Sections.Add(
                //new Section(doc, p));

                #endregion

                #region set the doc properties
                var frmData = doc.Content.FormFieldsData;
                var residentInfo = data?.FirstOrDefault();
                SetProperty<FormTextData>(frmData["HTResident"], residentInfo.Resident.FullName);
                SetProperty<FormTextData>(frmData["HtEmail"], residentInfo.Resident.Email);
                SetProperty<FormTextData>(frmData["HtMobile"], residentInfo.Resident.Mobile);
                SetProperty<FormTextData>(frmData["frmReportDate"], DateTime.Now.ToShortDateString());
                SetProperty<FormTextData>(frmData["HtDob"], residentInfo.Resident.DOB.Value.ToShortDateString());
                #endregion
                doc.Content.Replace("[ppp]", "001");


                return await Task.FromResult(doc);
            }
            catch (Exception ex)
            {
                var x = ex.Message;
                log.LogError(ex.Message);
                throw;
            }
        }
        public async Task<DocumentModel> GenerateHl7ReportResult(List<TelehealthProgressNote> data, Hl7Request req)
        {
            try
            {

                var stk = Stopwatch.StartNew();
                var stm = GetReportDocStream(DocHl7Report);
                var doc = DocumentModel.Load(stm);

                //var tbl = GetTable(doc, "tblNotes");
                //if (tbl != null) 
                //{
                //    foreach (var r in data)
                //    {
                //        var row = new GemBox.Document.Tables.TableRow(doc);
                //        tbl.Rows.Add(row);

                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.NoteDateUtc.Value.ToLongDateString() + "</b>"));
                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.Title + "</b>"));
                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.NoteDetails + "</b>"));
                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.SentTo + "</b>"));
                //        row.Cells.Add(AddHTMLCell(doc, "<b>" + r.Doctor?.FullName + "</b>"));

                //    }
                //}

                #region section
                SpecialCharacter lineBreakElement = new SpecialCharacter(doc, SpecialCharacterType.LineBreak);
                //List<Paragraph> p = new List<Paragraph>();
                // Create a table for `NoteDetails`.
                var table = new Table(doc);
                table.TableFormat.PreferredWidth = new TableWidth(100, TableWidthUnit.Percentage);
                doc.Sections.Add(new Section(doc, table));
                var paragraphStyle = new ParagraphStyle("CustomStyle")
                {
                    CharacterFormat = new CharacterFormat()
                    {
                        Size = 10, // Set the font size to 10 points.
                        Bold = false, // Remove bold formatting.
                        Italic = false, // Remove italic formatting.
                    }
                };
                doc.Styles.Add(paragraphStyle);

                // Add paragraphs and table cells for each `data` object.
                foreach (var r in data)
                {
                    // Concatenate all the information into a single string.
                    var rowData = $" <br /> <b>Date: </b> {r.NoteDateUtc.Value.ToLongDateString()} <br /> " +
                                  $"<b>Title: </b> {r.Title} <br /> " +
                                  $"<b>Sent To: </b> {r.SentTo} <br /> " +
                                  $"<b>Created by: </b> {r.CarerName} <br /> " +
                                  $"<b>NoteDetails: </b> {r.NoteDetails} <br />";

                    // Add the concatenated string to the table cell.
                    var row = new TableRow(doc);

                    row.Cells.Add(AddHTMLCell(doc, rowData));

                    table.Rows.Add(row);
                }

                //    doc.Sections.Add(
                //new Section(doc, p));

                #endregion

                #region set the doc properties
                var frmData = doc.Content.FormFieldsData;
                var residentInfo = data?.FirstOrDefault();
                SetProperty<FormTextData>(frmData["HTResident"], residentInfo.Resident.FullName);
                SetProperty<FormTextData>(frmData["HtEmail"], residentInfo.Resident.Email);
                SetProperty<FormTextData>(frmData["HtMobile"], residentInfo.Resident.Mobile);
                SetProperty<FormTextData>(frmData["frmReportDate"], DateTime.Now.ToLongDateString());
                SetProperty<FormTextData>(frmData["HtDob"], residentInfo.Resident.DOB.Value.ToShortDateString());
                SetProperty<FormTextData>(frmData["HtDoctor"], $"{req.ReferToProvider.Prefix} {req.ReferToProvider.GivenName} {req.ReferToProvider.FamilyNameLastNamePrefix}");


                if (req.IsAmended)
                {
                    SetProperty<FormTextData>(frmData["txtAmend"], $"Amended");

                }

                if (req.CopyToProvider != null) {
                    string cc = "CC: \n";
                    cc += $"{req.CopyToProvider.Prefix} {req.CopyToProvider.GivenName} {req.CopyToProvider.FamilyNameLastNamePrefix}";

                    SetProperty<FormTextData>(frmData["frmCc"], cc);
                }
                
                #endregion
                doc.Content.Replace("[ppp]", "001");


                return await Task.FromResult(doc);
            }
            catch (Exception ex)
            {
                var x = ex.Message;
                log.LogError(ex.Message);
                throw;
            }
        }
        /*
        public async Task<GenerateCertificateResponse> GenerateCertificate(GeneratePermitCertificateResponse appmodel)
        {
            var stk = Stopwatch.StartNew();
            var res = new GenerateCertificateResponse();
            var sm = StorageManager.GetInstance();

            var ftype = appmodel.AppType;
            var ms2 = await sm.GetDocFile($"{ftype}Certificate.docx");

            //create and save the PDF / Certificate
            var doc = DocumentModel.Load(ms2);

            AddConditions(doc,appmodel);
            AdjustDocumentForType(doc, appmodel);
            doc.Content.Replace("[ppp]", appmodel.PermitNumber);
            #region set the doc properties
            var frmData = doc.Content.FormFieldsData;
            SetProperty<FormTextData>(frmData["CLCLandTrust"], appmodel.LandTrusts);
            SetProperty<FormTextData>(frmData["CLCStartDate"], appmodel.StartDate);
            SetProperty<FormTextData>(frmData["CLCEndDate"], appmodel.EndDate);
            SetProperty<FormTextData>(frmData["CLCPersons"], appmodel.PersonsJSON);
            SetProperty<FormTextData>(frmData["CLCAddress"], appmodel.Address);
            SetProperty<FormTextData>(frmData["CLCLongDate"], getLongDate(appmodel.IssueDate));
            SetProperty<FormTextData>(frmData["CLCEntryType"], appmodel.AppType);
            SetProperty<FormTextData>(frmData["CLCAreas"], appmodel.Roads);
            SetProperty<FormTextData>(frmData["CLCVehicles"], appmodel.VehiclesJSON);
            #endregion
            using (var ms = new MemoryStream())
            {
                var fname = $"{appmodel.AppType}Certificate.pdf";
                doc.Save(ms, SaveOptions.PdfDefault);
                ms.Seek(0, SeekOrigin.Begin);

                res.CertificatePath = await sm.SavePermitFile(appmodel.PermitNumber, fname, ms);
                //res.CertificatePathFull = StorePath + (!StorePath.EndsWith('/')?"/":"")+ res.CertificatePath;

                var certInfo = await sm.GetCertificate(appmodel.PermitNumber);
                res.CertificatePathFull = certInfo.CertificatePathFull;

            }

            stk.Stop();
            res.ProcessTime = stk.ElapsedMilliseconds;
            return (res);
        }

        private void AdjustDocumentForType(DocumentModel doc, GeneratePermitCertificateResponse appmodel)
        {
            if (appmodel.AppType=="Entry") // we need to show hide a map in the document
            {
                if (!appmodel.Details.Contains("Madigan"))
                {
                    var range = doc.Content.Find("Map of ");
                    if (range.Count()>0)
                    {
                        //remove the title and map.
                        var r = range.First();
                        var newRange = new ContentRange(r.Start, doc.Content.End);
                        newRange.Delete();
                    }
                }
            }
        }

        private void AddConditions(DocumentModel doc, GeneratePermitCertificateResponse appmodel)
        {
            var style = doc.Styles["no list"];

            //General Conditions
            var lst = doc.Content.Find("[ggg]");
            var pos = lst?.FirstOrDefault();
            if (pos!=null)
            {
                var sb = new StringBuilder();
                sb.Append("<ol>");
                foreach (var c in appmodel.GeneralConditions)
                    sb.AppendLine($"<li>{c.Condition?.Replace(@"\/", "/")}</li>");
                sb.Append("</ol>");
                var p = $"<p style='font:arial 9pt'>";
                var cr = pos.LoadText(p + sb.ToString() + "</p>", new HtmlLoadOptions());
            }

            //special conditions.
            lst = doc.Content.Find("[sss]") as List<ContentRange>;
            pos = lst?.FirstOrDefault();
            if (pos != null)
            {
                var sb = new StringBuilder();
                sb.Append("<ol>");
                foreach (var c in appmodel.SpecialConditions)
                    sb.AppendLine($"<li>{c.Condition?.Replace(@"\/", "/")}</li>");
                sb.Append("</ol>");
                var p = $"<p style='font:arial 9pt'>";
                pos.LoadText(p + sb.ToString() + "</p>", new HtmlLoadOptions());
            }


        }

        private string getLongDate(string issueDate)
        {
            try
            {
                var dt = DateTime.Parse(issueDate);
                return dt.ToLongDateString();
            }
            catch (Exception)
            {

                return issueDate;
            }
        }

        public void SetProperty<T>(FormFieldData fld,string val)
        {
            try
            {
                if (!string.IsNullOrEmpty(val))
                {

                    if (fld.GetType().Name == "FormTextData")
                    {
                        ((FormTextData)fld).Value = val;
                    }
                }
            }
            catch(Exception ex)
            {
                //swallowed :)
            }

        }

        private static void ComponentInfo_FreeLimitReached(object sender, FreeLimitEventArgs e)
        {
            e.FreeLimitReachedAction = FreeLimitReachedAction.ContinueAsTrial;
        }

        */
        private static string RemoveHtmlTags(string html)
        {
            return Regex.Replace(html, "<.*?>", string.Empty);
        }
        private GemBox.Document.Tables.TableCell AddHTMLCell(DocumentModel doc, string txt)
        {
            var cell = new GemBox.Document.Tables.TableCell(doc);
            cell.Content.LoadText(txt, HtmlLoadOptions.HtmlDefault);
            return (cell);
        }

        private GemBox.Document.Tables.TableCell AddTextCell(DocumentModel doc, string txt)
        {
            var cell = new GemBox.Document.Tables.TableCell(doc);
            var p = new GemBox.Document.Paragraph(doc, $"{txt}");
            cell.Blocks.Add(p);
            return (cell);
        }

        private GemBox.Document.Tables.Table GetTable(DocumentModel doc, string tblName)
        {
            var tbls = doc.Content.GetChildElements(new ElementType[] { ElementType.Table });
            var tbl = from t in tbls
                      where ((GemBox.Document.Tables.Table)t).Metadata?.Title == tblName
                      select (GemBox.Document.Tables.Table)t;

            return (tbl?.FirstOrDefault());

        }

        public void SetProperty<T>(FormFieldData fld, string val)
        {
            try
            {
                if (!string.IsNullOrEmpty(val))
                {

                    if (fld.GetType().Name == "FormTextData")
                    {
                        ((FormTextData)fld).Value = val;
                    }
                }
            }
            catch (Exception ex)
            {
                //swallowed :)
            }

        }
    }
}
