﻿using ht.common.backend.shared.helpers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.fn.Helpers
{
    public class AcsManager
    {
        private ILogger _logger;

        public string AccountName { get; }
        public string AccountKey { get; }
        public string UsersUrl { get; }
        public string UserSasToken { get; }

        public string AcsAccessKey { get; }
        public string AcsUrl { get; }

        public AcsManager(ILogger logger)
        {
            _logger = logger;
            AccountName = Environment.GetEnvironmentVariable("IotStorageAccountName");
            AccountKey = Environment.GetEnvironmentVariable("IotStorageAccountKey");
            UsersUrl = Environment.GetEnvironmentVariable("BlobUsersUrl");
            UserSasToken = Environment.GetEnvironmentVariable("BlobUsersSasToken");

            AcsAccessKey = Environment.GetEnvironmentVariable("AcsAccessKey");
            AcsUrl = Environment.GetEnvironmentVariable("AcsUrl");
        }

        public async Task<string> SaveRecording(string docId)
        {
            var vidUrl = $"{AcsUrl}/recording/download/{docId}?api-version=2021-04-15-preview1"; ;
            var metaUrl = $"{AcsUrl}/recording/download/{docId}/metadata?api-version=2021-04-15-preview1"; ;

            var json = await GetAcsRecordingAsString(docId, metaUrl);
            var stm = await GetAcsRecordingAsStream(docId, vidUrl);

            var bh = new BlobHelper(AccountName, AccountKey, _logger);
            var url = UsersUrl + $"/meetings/recordings/{docId}.mp4";
            //var sas = await bh.GetSasToken(url, int.MaxValue);
            using (var ms = new MemoryStream())
            {
                stm.CopyTo(ms);
                ms.Position = 0;
                await bh.SaveBlob(url, UserSasToken, ms.ToArray(), new Dictionary<string, string> { { "meetingid", "0" } });
            };
            return ($"{url}");

        }

        public async Task<Stream> GetAcsRecordingAsStream(string docId,string docUrl)
        {
            //var url = $"{AcsUrl}/recording/download/{docId}?api-version=2021-04-15-preview1";
            var url = docUrl;
            using (var clnt = new HttpClient())
            {
                StringContent content = null;
                var req = new HttpRequestMessage
                {
                    Method = HttpMethod.Get,
                    RequestUri = new Uri(url),
                    Content = content
                };
                var hashedContent = CreateContentHash(string.Empty); //as per the ACS recording documentation.
                AddHmacHeaders(req, hashedContent, AcsAccessKey);
                var resp = await clnt.SendAsync(req).ConfigureAwait(false);
                resp.EnsureSuccessStatusCode();
                return resp.Content.ReadAsStream();
            }
        }

        public async Task<string> GetAcsRecordingAsString(string docId, string docUrl)
        {
            var stm = await GetAcsRecordingAsStream(docId, docUrl);
            using (var sr = new StreamReader(stm))
                return (sr.ReadToEnd());

        }

        public static string CreateContentHash(string content)
        {
            var alg = SHA256.Create();

            using (var memoryStream = new MemoryStream())
            using (var contentHashStream = new CryptoStream(memoryStream, alg, CryptoStreamMode.Write))
            {
                using (var swEncrypt = new StreamWriter(contentHashStream))
                {
                    if (content != null)
                    {
                        swEncrypt.Write(content);
                    }
                }
            }

            return Convert.ToBase64String(alg.Hash);
        }

        public static void AddHmacHeaders(HttpRequestMessage requestMessage, string contentHash, string accessKey)
        {
            var utcNowString = DateTimeOffset.UtcNow.ToString("r", CultureInfo.InvariantCulture);
            var uri = requestMessage.RequestUri;
            var host = uri.Authority;
            var pathAndQuery = uri.PathAndQuery;

            var stringToSign = $"{requestMessage.Method}\n{pathAndQuery}\n{utcNowString};{host};{contentHash}";
            var hmac = new HMACSHA256(Convert.FromBase64String(accessKey));
            var hash = hmac.ComputeHash(Encoding.ASCII.GetBytes(stringToSign));
            var signature = Convert.ToBase64String(hash);
            var authorization = $"HMAC-SHA256 SignedHeaders=date;host;x-ms-content-sha256&Signature={signature}";

            requestMessage.Headers.Add("x-ms-content-sha256", contentHash);
            requestMessage.Headers.Add("Date", utcNowString);
            requestMessage.Headers.Add("Authorization", authorization);
        }


        public static string ExtractServerCallId(string subject)
        {
            var res = string.Empty;
            var parts = subject?.Split("/");
            
            if (parts?.Length > 0)
            {
                for (int i = 0; i < parts?.Length; i++)
                {
                    var p = parts[i]?.ToLower();
                    if (p == "servercallid")
                    {
                        if (i <= parts?.Length - 2)
                            res = parts[i + 1]; //this is the server call Id
                        break;
                    }
                }

            }


            return (res);
        }

    }
}
