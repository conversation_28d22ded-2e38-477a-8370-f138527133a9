﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.fn.Helpers
{
    public class TempFile : IDisposable
    {
        private string fName { get; set; }
        public string UrlExt { get; set; }
        public TempFile()
        {
            fName = Path.GetTempFileName();
        }
        public TempFile(string ext)
        {
            var fn = Path.GetTempFileName();
            fName = $"{fn}.{ext}";
            DeleteFile(fn);
        }
        public byte[] GetBytes()
        {
            if (File.Exists(this.fName))
                return File.ReadAllBytes(this.fName);
            else
                return null;
        }

        public static TempFile CopyFromUrl(string url,byte[] bytes=null,string ext="")
        {
            if (string.IsNullOrEmpty(ext))
                ext = Path.GetExtension(new Uri(url).AbsolutePath)?.Replace(".", "");
            if (bytes==null)
                bytes = (new HttpClient().GetByteArrayAsync(url)).GetAwaiter().GetResult();
            var inputStream = new MemoryStream(bytes);
            TempFile fName = new TempFile(ext) { UrlExt = ext };

            using (var fs = new FileStream(fName.FileName, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {
                inputStream.CopyTo(fs);
                inputStream.Flush();
                fs.Close();
            }

            return fName;
        }
        private void DeleteFile(string fName)
        {
            try
            {
                File.Delete(fName);
            }
            catch { }
        }

        public string FileName
        {
            get
            {
                return fName;
            }
        } 
        
           

        public override string ToString()
        {
            return fName;
        }
        public void Dispose()
        {
            if (!string.IsNullOrEmpty(fName))
                DeleteFile(fName);
        }
    }
}
