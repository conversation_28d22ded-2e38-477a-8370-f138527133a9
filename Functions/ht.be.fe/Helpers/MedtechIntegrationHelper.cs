﻿using Hl7.Fhir.Model;
using Hl7.Fhir.Rest;
using Hl7.Fhir.Support;
using ht.be.fn.Utils;
using ht.common.backend.shared.helpers;
using ht.data.common.partner;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using Microsoft.CSharp.RuntimeBinder;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace ht.be.fn.Helpers
{
    // Hold Access Token for Medtech and expiry time for the token in memory
    public static class MedtechIntegrationAccessToken
    {
        public static string AccessToken = "";
        public static DateTime ExpiryTime = DateTime.MinValue;

        // Return true if the token is still valid, false if it has expired
        public static bool IsTokenValid()
        {
            // If the token is empty, it is not valid
            if (string.IsNullOrEmpty(AccessToken))
            {
                return false;
            }

            // If the expiry time is in the past, it is not valid
            // Use a 5 minute buffer for token expiry
            return DateTime.UtcNow < ExpiryTime.AddMinutes(-5);
        }
    }

    public class MedtechIntegrationHelper
    {
        private ILogger _logger;

        public MedtechIntegrationHelper(ILogger log)
        {
            _logger = log;
        }

        public async Task<string> GetMedtechAccessToken(PartnerIntegrationMapping partner)
        {
            try
            {
                // Check if the token is still valid
                if (MedtechIntegrationAccessToken.IsTokenValid())
                {
                    _logger.LogInformation("Medtech access token is still valid.");
                    return MedtechIntegrationAccessToken.AccessToken;
                }

                // Medtech uses microsoft authentication.
                var client_id = partner.UserName; //"74b8c8fb-42e6-4545-a342-cd5b7363817f";
                var client_secret = partner.Password; // "****************************************";
                // Get the auth endpoint and scope from SettingsJSON
                string endpoint = "";
                string auth_scope = "";
                dynamic partnerSettings = JsonConvert.DeserializeObject(partner.SettingsJSON);
                if (partnerSettings != null)
                {
                    endpoint = partnerSettings.auth_endpoint; //"https://login.microsoftonline.com/8a024e99-aba3-4b25-b875-28b0c0ca6096/oauth2/v2.0/token";
                    auth_scope = partnerSettings.auth_scope; //"api://bf7945a6-e812-4121-898a-76fea7c13f4d/.default";
                }
                var scope = new string[] { auth_scope };

                // Setup MSAL
                var client = ConfidentialClientApplicationBuilder
                    .Create(client_id)
                    .WithAuthority(endpoint)
                    .WithClientSecret(client_secret)
                    .Build();

                // Cache the token
                // TPC, 20/11/2024: Adding Microsoft.Identity.Web stops all functions from working. We will cache the token in MedtechIntegrationAccessToken for now.
                //client.AddInMemoryTokenCache();

                // Retrieve an access token
                var authResult = await client.AcquireTokenForClient(scope).ExecuteAsync();

                // The access token is in authResult.AccessToken
                if (string.IsNullOrEmpty(authResult.AccessToken))
                {
                    _logger.LogError("Error when authenticating for Medtech access token. Access token is empty.");
                    return "";
                }

                // Cache the access token and expiry time
                MedtechIntegrationAccessToken.AccessToken = authResult.AccessToken;
                MedtechIntegrationAccessToken.ExpiryTime = authResult.ExpiresOn.UtcDateTime;
                _logger.LogInformation("Medtech access token expires at: " + MedtechIntegrationAccessToken.ExpiryTime.ToString() + ". Current time is: " + DateTime.UtcNow.ToString());
                _logger.LogDebug("Medtech access token:" + authResult.AccessToken);

                return authResult.AccessToken;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error when authenticating for Medtech access token: " + ex.Message);
                return "";
            }
        }

        public bool IsPartnerMedtech(List<PartnerIntegrationMapping> partnerMappingDetails)
        {
            return partnerMappingDetails.Any(p => p.PartnerName == "Medtech");
        }

        // Generate a FhirClient
        private FhirClient GetFhirClient(PartnerIntegrationMapping partner)
        {
            string token = GetMedtechAccessToken(partner).GetAwaiter().GetResult();
            var messageHandler = new HttpClientEventHandler();
            messageHandler.OnBeforeRequest += (object sender, BeforeHttpRequestEventArgs e) =>
            {
                e.RawRequest.Headers.Add("Authorization", $"Bearer {token}");
                e.RawRequest.Headers.Add("mt-facilityid", partner.ExPartnerMapId);
            };
            FhirClientSettings settings = new FhirClientSettings()
            {
                PreferredFormat = ResourceFormat.Json,
                VerifyFhirVersion = true
            };
            return new FhirClient($"{partner.IntegrationURL}fhir/", messageHandler: messageHandler, settings: settings);
        }

        public async Task<bool> SendVitalsToMedtech(string userExId, string patientExId, string exTransactionId, List<PartnerIntegrationMapping> partner)
        {
            SystemLog("Information", "SendVitalsToMedtech", $"SendVitalsToMedtech() - Fetching monitoring data for transaction id {exTransactionId}");
            _logger.LogInformation($"SendVitalsToMedtech() - Fetching monitoring data for transaction id {exTransactionId}");

            try
            {
                var telehealthMonitorDataList = PartnerIntegrationHelper.FetchMonitorData(exTransactionId);

                _logger.LogInformation($"Data for transaction {exTransactionId}: {HTJsonSerialiser.Serialize(telehealthMonitorDataList)}");

                // Get patient mapping
                var patientMap = PartnerIntegrationHelper.GetResidentMap(partner, patientExId);
                if (patientMap == null)
                {
                    _logger.LogError($"Patient mapping not found for userExId {patientExId}");
                    return false;
                }

                // Get doctor mapping
                var doctorMap = PartnerIntegrationHelper.GetStaffMap(partner, userExId);
                if (doctorMap == null)
                {
                    doctorMap = partner.FirstOrDefault(p => p.EntityType.ToLower() == "doctor");
                    if (doctorMap == null)
                    {
                        _logger.LogError($"Doctor mapping not found for userExId {userExId}");
                        return false;
                    }
                }

                #region Get mappings for vitals
                // Get all partner settings
                dynamic partnerSettings = JsonConvert.DeserializeObject(doctorMap.SettingsJSON);

                // Get the vitals regex search strings into a dictionary - these are for finding the screening terms setup in Medtech for the 
                // vitals we need to send as anatomic data
                Dictionary<string, string> vitalsSearches = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonConvert.SerializeObject(partnerSettings.vitals));

                // Get mappings for vitals setup in Medtech
                var medtechVitals = await GetVitalsSetupInMedtech(doctorMap, vitalsSearches);

                #endregion Get mappings for vitals

                _logger.LogInformation($"telehealthMonitorDataList: {HTJsonSerialiser.Serialize(telehealthMonitorDataList)}");

                var bpList = new List<TelehealthDataItem>();
                var observation = new Observation();
                int vitalsCount = 0;

                // Medtech takes atomic data, so we need to send each reading individually
                foreach (var telehealthMonitorData in telehealthMonitorDataList)
                {
                    _logger.LogInformation($"Monitor data: {HTJsonSerialiser.Serialize(telehealthMonitorData)}");

                    // Vitals of type audio/image will be sent as links in a consultation note
                    var consultNote = new StringBuilder();

                    // There appears to be usually only 1 reading in the list, but we will loop through them anyway
                    foreach (var reading in telehealthMonitorData.Readings)
                    {
                        _logger.LogInformation($"Reading for {reading.ItemType.ToString()}: {HTJsonSerialiser.Serialize(reading)}");

                        if (reading?.Files?.Count > 0)
                        {
                            // Handle audio/image readings by combining them into a consultation note
                            // This should cover the following types:
                            //MonitorType.Ear:
                            //MonitorType.Throat:
                            //MonitorType.Heart:
                            //MonitorType.Lungs:
                            //MonitorType.Wound:    ???
                            //MonitorType.ECG:      ???

                            reading.Files.ForEach(p =>
                            {
                                consultNote.AppendLine(@$"<b>{reading.ItemType.ToString()}:<b>");
                                consultNote.AppendLine(@$"{p.Comment}");
                                p.FileUrls.ForEach(f =>
                                {
                                    // In order for links to be clickable in Medtech, we need the url as the filename too
                                    consultNote.AppendLine(@$"<a target='_blank' href='{f.FileUrl}'>{f.FileUrl}</a><br>");
                                });
                                consultNote.AppendLine(@$"<br>");
                            });
                        }
                        else if (reading.ItemType == MonitorType.UA)
                        {
                            PartnerIntegrationHelper.AppendUA(consultNote, reading);
                        }
                        else
                        {
                            // Handle other readings as atomic data if they are supported by Medtech
                            var vitalsCode = "";
                            switch (reading.ItemType)
                            {
                                case MonitorType.BPSystolic:
                                    bpList.Add(reading);
                                    break;
                                case MonitorType.BPDiastolic:
                                    bpList.Add(reading);
                                    break;
                                case MonitorType.SpO2:
                                case MonitorType.Temp:
                                case MonitorType.Glucose:
                                case MonitorType.Weight:
                                case MonitorType.HR:
                                case MonitorType.Height:
                                case MonitorType.BMI:
                                    vitalsCode = reading.ItemType.ToString();
                                    break;
                                default:
                                    break;
                            }
                            if (vitalsCode != "")
                            {
                                // Perform a mapping of the vital to the Medtech format
                                observation = null; // Reset observation for each reading
                                if (vitalsSearches.ContainsKey(vitalsCode))
                                {
                                    var vitalsRegex = vitalsSearches[vitalsCode];
                                    observation = GetMappedObservation(vitalsRegex, medtechVitals);
                                }
                                if (observation != null)
                                {
                                    _logger.LogInformation($"Mapping for {vitalsCode} found. Sending vital to Medtech.");
                                    vitalsCount++;
                                    if (!await SendVitalToMedtech(new List<TelehealthDataItem> { reading }, observation, patientMap, doctorMap, vitalsCount))
                                    {
                                        _logger.LogError($"Error sending vital {vitalsCode} to Medtech.");
                                    }
                                }
                                else
                                    _logger.LogInformation($"Mapping for {vitalsCode} not found, ignoring.");
                            }
                        }
                    }

                    if (consultNote.Length > 0)
                    {
                        var takenDateUtc = telehealthMonitorData.Readings.FirstOrDefault().ReadDateUtc ?? DateTime.UtcNow;
                        // Send the consultation note to Medtech
                        if (!await SendConsultNoteToMedtech(consultNote.ToString(), takenDateUtc, patientMap, doctorMap))
                        {
                            _logger.LogError($"Error sending consultation note to Medtech.");
                        }
                    }
                }
                // If we have a BP reading, send it as a single observation
                if (bpList.Count > 0)
                {
                    // Perform a mapping of the vital to the Medtech format
                    observation = null;
                    if (vitalsSearches.ContainsKey("BP"))
                    {
                        var vitalsRegex = vitalsSearches["BP"];
                        observation = GetMappedObservation(vitalsRegex, medtechVitals);
                    }
                    if (observation != null)
                    {
                        _logger.LogInformation($"Mapping for BP found. Sending vital to Medtech.");
                        vitalsCount++;
                        if (!await SendVitalToMedtech(bpList, observation, patientMap, doctorMap, vitalsCount))
                        {
                            _logger.LogError($"Error sending vital BP to Medtech.");
                        }
                    }
                    else
                        _logger.LogInformation($"Mapping for BP not found, ignoring.");
                }
            }
            catch (Exception ex)
            {
                SystemLog("Error", "SendVitalsToMedtech", $"Transation: {exTransactionId.ToString()}, Exception: {ex.ToString()}" );
                _logger.LogError($"Error in SendVitalsToMedtech: {exTransactionId.ToString()}");
            }
            return true;
        }

        private async Task<bool> SendVitalToMedtech(List<TelehealthDataItem> vitals, Observation medtechScreeningSetup, PartnerIntegrationMapping residentMap, PartnerIntegrationMapping doctorMap, int vitalsCount = 0)
        {
            SystemLog("Information", "SendVitalToMedtech", $"Begin SendVitalToMedtech(). Vitals count: {vitals.Count}");
            _logger.LogInformation($"Begin SendVitalToMedtech(). Vitals count: {vitals.Count}");
            _logger.LogInformation($"Screening setup: {HTJsonSerialiser.Serialize(medtechScreeningSetup)}");
            try
            {
                // Setup new observation to be sent to Medtech
                Coding medtechCode = medtechScreeningSetup.Code.Coding.FirstOrDefault();
                var observation = new Observation
                {
                    Status = ObservationStatus.Final,
                    Subject = new ResourceReference($"{doctorMap.IntegrationURL}Patient/{residentMap.SourceId}", residentMap.EntityFullName),
                    Performer = new List<ResourceReference>
                    {
                        new ResourceReference($"{doctorMap.IntegrationURL}Practitioner/{doctorMap.SourceId}")
                    },
                    Code = new CodeableConcept($"https://alexapi.medtechglobal.com/fhir/screening-term", medtechCode.Code, medtechCode.Display, medtechCode.Display),
                };

                // Get the first ReadDateUtc from the list of vitals, set as the effectiveInstant
                var takenDateUtc = vitals.FirstOrDefault().ReadDateUtc ?? DateTime.UtcNow;
                if (vitalsCount > 1)
                {
                    // If there are multiple vitals, we need to offset the effectiveInstant beacause Medtech does not allow another vital with the same datetime, even if a different vital type
                    takenDateUtc = DateTime.UtcNow.AddMilliseconds(vitalsCount-1);
                }
                TimeZoneInfo newZealandTimeZone = TimeZoneInfo.FindSystemTimeZoneById(doctorMap.FacilityLocaleZone);
                DateTime nzDate = TimeZoneInfo.ConvertTimeFromUtc(takenDateUtc, newZealandTimeZone);
                DateTimeOffset facilityDatetimeOffset = new DateTimeOffset(nzDate, newZealandTimeZone.BaseUtcOffset);
                observation.Effective = new Instant(facilityDatetimeOffset);

                _logger.LogInformation($"Effective date: {observation.Effective}, vitals count: {vitalsCount}");

                var components = new List<Observation.ComponentComponent>();
                bool high = false, low = false;
                foreach (var vital in vitals)
                {
                    // Some vitals (like BP) will have multiple readings, so we need to combine them as a single observation for Medtech
                    _logger.LogInformation($"Vital {vital.ItemType.ToString()} processing before sending to Medtech.");

                    // Search the components in medtechScreeningSetup to find code = "sys" and use this for the code
                    Coding medtechComponentCode = null;
                    if (medtechScreeningSetup.Component.Count > 1)
                    {
                        // There are multiple codes defined in Medtech for this screening type, so we need to find the appropriate code
                        // This is the case for BP, where we have Systolic and Diastolic readings
                        if (vital.ItemType == MonitorType.BPSystolic)
                            medtechComponentCode = medtechScreeningSetup.Component.SelectMany(c => c.Code.Coding).FirstOrDefault(c => c.Code.StartsWith("Sys", StringComparison.OrdinalIgnoreCase));
                        else if (vital.ItemType == MonitorType.BPDiastolic)
                            medtechComponentCode = medtechScreeningSetup.Component.SelectMany(c => c.Code.Coding).FirstOrDefault(c => c.Code.StartsWith("Dia", StringComparison.OrdinalIgnoreCase));
                    }
                    else
                    {
                        // There's only 1 code defined in Medtech for this screening type, so use that. This is the case for most vitals, eg Weight, Height, BMI etc.
                        medtechComponentCode = medtechScreeningSetup.Component.FirstOrDefault().Code.Coding.FirstOrDefault();
                    }
                    if (medtechComponentCode == null)
                    {
                        _logger.LogError($"Error sending vital {vital.ItemType.ToString()} to Medtech. Component code mapping not found.");
                        return false;
                    }
                    _logger.LogInformation($"Component Code mapping found: {medtechComponentCode.Code}");
                    var compponent = new Observation.ComponentComponent
                    {
                        Code = new CodeableConcept(medtechComponentCode.System, medtechComponentCode.Code, medtechComponentCode.Display, medtechComponentCode.Display),
                    };
                    string extensionValueType = "Text";
                    // Get the component.code.extension from the medtechScreeningSetup and use this to determing what format to provide the vitals value
                    // There could be multiple components, but appears to be only 1 extension per component
                    Extension extension = medtechScreeningSetup.Component.FirstOrDefault().Code.Extension.FirstOrDefault();

                    // If we need to get the specific component if multiple, then will need something like this..
                    //Observation.ComponentComponent testComponent = medtechScreeningSetup.Component.FirstOrDefault(c => c.Code.Coding.Any(cd => cd.Code == "Dia"));
                    //if (testComponent != null) {
                    //    _logger.LogInformation($"Component Code found: {HTJsonSerialiser.Serialize(testComponent)}");
                    //    Extension testExtension = testComponent.Code.Extension.FirstOrDefault();
                    //    if (testExtension != null)
                    //    {
                    //        _logger.LogInformation($"Component Code extension found: {HTJsonSerialiser.Serialize(testComponent.Code.Extension)}");
                    //    }
                    //}

                    if (extension != null)
                    {
                        FhirString fhirString = extension.Value as FhirString;
                        extensionValueType = fhirString.Value;
                        _logger.LogInformation($"Component Code extension \"{extensionValueType}\" found: {HTJsonSerialiser.Serialize(compponent.Code.Extension)}");
                    }
                    switch (extensionValueType)
                    {
                        case "Decimal":
                            compponent.Value = new Quantity { Value = decimal.Parse(vital.ReadValue) };
                            break;
                        default:
                            compponent.Value = new FhirString(vital.ReadValue);
                            break;
                    }

                    components.Add(compponent);

                    // Check ranges and remember for the interpretation
                    if ((vital.IsAlert.HasValue && vital.IsAlert.Value) || (vital.IsAbnormal.HasValue && vital.IsAbnormal.Value))
                    {
                        // if we need to get the expected value, this comes from the thresholds collection (of 1) in the telehealthMonitorData
                        //var th = telehealthMonitorData.Thresholds.Limits.FirstOrDefault();
                        //string expected;
                        //if (th != null && th?.Expected != null)
                        //    expected = $"{th.Expected} {vital.ReadUnits} expected";

                        var vitalValue = float.Parse(vital.ReadValue);
                        // Since we are mapping both our warning and alert levels to a single low or high indicator, we only need to check for warning ranges
                        float lowValue = vital.LowWarningValue.HasValue ? vital.LowWarningValue.Value : 0;
                        float highValue = vital.HighWarningValue.HasValue ? vital.HighWarningValue.Value : 0;
                        if (vitalValue < lowValue)
                            low = true;
                        if (vitalValue > highValue)
                            high = true;
                    }
                }
                _logger.LogInformation($"Interpretation levels, low = {low}, high = {high}.");

                observation.Component = components;

                // Search the interpretations in medtechScreeningSetup for the normal, high, low interpretations
                if (medtechScreeningSetup.Interpretation != null && medtechScreeningSetup.Interpretation.Count > 0)
                {
                    Coding interpretationCode;
                    if (high)
                        interpretationCode = medtechScreeningSetup.Interpretation.FirstOrDefault().Coding.FirstOrDefault(c => c.Code.Equals("H", StringComparison.OrdinalIgnoreCase) || c.Display.Equals("High", StringComparison.OrdinalIgnoreCase));
                    else if (low)
                        interpretationCode = medtechScreeningSetup.Interpretation.FirstOrDefault().Coding.FirstOrDefault(c => c.Code.Equals("L", StringComparison.OrdinalIgnoreCase) || c.Display.Equals("Low", StringComparison.OrdinalIgnoreCase));
                    else
                        interpretationCode = medtechScreeningSetup.Interpretation.FirstOrDefault().Coding.FirstOrDefault(c => c.Code.Equals("N", StringComparison.OrdinalIgnoreCase) || c.Display.Equals("Normal", StringComparison.OrdinalIgnoreCase));
                    if (interpretationCode != null)
                    {
                        observation.Interpretation = new List<CodeableConcept>
                        {
                            new CodeableConcept(interpretationCode.System, interpretationCode.Code, interpretationCode.Display, interpretationCode.Display)
                        };
                    }
                }
                _logger.LogInformation($"Observation setup: {HTJsonSerialiser.Serialize(observation)}");

                try
                {
                    //FhirClient client = GetFhirClient(doctorMap);
                    var result = await SendOrQueueObservation(doctorMap, observation);
                    if (result != null)
                    {
                        SystemLog("Information", "SendVitalToMedtech", $"Vital sent to Medtech successfully. Medtech Id: {result.Id}");
                        _logger.LogInformation($"Vital sent to Medtech successfully.");
                        return true;
                    }
                    else
                    {
                        _logger.LogError($"Error sending vital to Medtech, null returned.");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    SystemLog("Error", "SendVitalToMedtech", $"Error sending vital to Medtech: {ex.ToString()}");
                    _logger.LogError($"Error sending vital to Medtech: {ex.ToString()}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                SystemLog("Error", "SendVitalToMedtech", $"Error in SendVitalToMedtech(): {ex.ToString()}");
                _logger.LogError($"Error in SendVitalToMedtech(): {ex.ToString()}");
                return false;
            }
        }

        private async Task<Observation> SendOrQueueObservation(PartnerIntegrationMapping doctorMap, Observation observation)
        {
            try
            {
                FhirClient client = GetFhirClient(doctorMap);
                return await client.CreateAsync(observation);
            }
            catch (Exception ex)
            {
                //TODO: place observation in a queue to be sent later
                SystemLog("Error", "SendOrQueueObservation", ex.ToString());
                _logger.LogInformation($"Observation added to queue, will send to Medtech later.");
                throw;
            }
        }

        public async Task<bool> SendConsultNoteToMedtech(string note, DateTime takenDateUtc, PartnerIntegrationMapping patientMap, PartnerIntegrationMapping doctorMap)
        {
            _logger.LogInformation($"Sending a consult note to Medtech. Mappings:{HTJsonSerialiser.Serialise(doctorMap)}");

            try
            {
                // I think we pass UTC to medtech
                //TimeZoneInfo newZealandTimeZone = TimeZoneInfo.FindSystemTimeZoneById("New Zealand Standard Time");
                //DateTime nzDate = TimeZoneInfo.ConvertTimeFromUtc(takenDateUtc, newZealandTimeZone);
                //DateTimeOffset facilityDatetimeOffset = new DateTimeOffset(nzDate, newZealandTimeZone.BaseUtcOffset);

                Hl7.Fhir.Model.DocumentReference documentReference = new Hl7.Fhir.Model.DocumentReference();
                documentReference.Author = new List<ResourceReference> { new ResourceReference { Reference = $"{doctorMap.IntegrationURL}fhir/Practitioner/{doctorMap.SourceId}" } };
                documentReference.Status = DocumentReferenceStatus.Current;
                documentReference.Type = new CodeableConcept("http://loinc.org", "11488-4", "Consult Note", "Consult Note");
                documentReference.Description = "Clinical data from Medtech Connect";
                documentReference.Subject = new ResourceReference { Reference = $"{doctorMap.IntegrationURL}fhir/Patient/{patientMap.SourceId}" };

                var progressNoteHtml = new StringBuilder();
                progressNoteHtml.Append("<style>body {font-family: Verdana,Arial, Helvetica, sans-serif; font-size: 11px;}</style>");
                progressNoteHtml.Append($"<p>Clinical data collected in Medtech Connect</p>");
                progressNoteHtml.Append($"<p>{note}</p>");

                RTFHelper rtfHelper = new RTFHelper(_logger);
                string noteRtf = rtfHelper.ConvertHTMLtoRTF(progressNoteHtml.ToString());

                _logger.LogInformation($"SendConsultNoteToMedtech - consult note converted to RTF: {noteRtf}");

                // Alex documentation says the content should be base64 encoded, but that doesnt work with the FHIR client, so we will send the RTF string as a byte array
                documentReference.Content = new List<DocumentReference.ContentComponent>
                {
                new DocumentReference.ContentComponent
                {
                    Attachment = new Attachment
                    {
                        ContentType = "application/rtf",
                        Data = Encoding.UTF8.GetBytes(noteRtf)
                    },
                    Format = new Coding("http://loinc.org", "61149-1", "Objective Narrative")
                    //Format = new Coding("http://loinc.org", "61150-9", "Subjective Narrative")
                }
                };

                documentReference.Context = new DocumentReference.ContextComponent
                {
                    FacilityType = new CodeableConcept("http://snomed.info/sct", "Other", "Consult note from Medtech Connect", "Other"),
                    Period = new Period
                    {
                        Start = takenDateUtc.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                    }
                };

                string ipAddr = await GetIPAddress();

                var documentReferenceResponse = await SendOrQueueConsultNote(patientMap, documentReference);
                if (documentReferenceResponse != null)
                {
                    _logger.LogInformation($"Consult note sent. Medtech Id: {documentReferenceResponse.Id}");

                    //TODO: Log the consult note sent to Medtech somewhere

                    return true;
                }
                else
                {
                    _logger.LogError($"Consult note not sent. Medtech response: {HTJsonSerialiser.Serialize(documentReferenceResponse)}");
                    return false;
                }
            }

            catch (Exception ex)
            {
                _logger.LogError($"Error when sending consult note to Medtech: {ex.ToString()}");
                return false;
            }
        }

        private async Task<DocumentReference> SendOrQueueConsultNote(PartnerIntegrationMapping partnerMap, DocumentReference document)
        {
            try
            {
                FhirClient client = GetFhirClient(partnerMap);
                return await client.CreateAsync<DocumentReference>(document);
            }
            catch (Exception ex)
            {
                //TODO: place note in a queue to be sent later
                _logger.LogInformation($"Consult Notes added to queue, will send to Medtech later.");
                throw;
            }
        }

        public async Task<List<Observation>> GetVitalsSetupInMedtech(PartnerIntegrationMapping partner, Dictionary<string, string> regexPatterns)
        {
            // Each Medtech facility can have their own setup for what vitals they want to receive, these are called "Screening Terms" in Medtech.
            // This routine will get a list of fhir observation objects that are a list of codes we can send vitals for.
            // We use regex strings defined in the db to identify the applicable observations and return only those from this routine.
            var mappedObservations = new List<Observation>();
            FhirClient client = GetFhirClient(partner);

            _logger.LogInformation($"GetVitalsSetupInMedtech mapping: {HTJsonSerialiser.Serialize(partner)}");
            _logger.LogInformation($"GetVitalsSetupInMedtech url: {$"subject:Location.identifier=https://standards.digital.health.nz/ns/hpi-facility-id|{partner.ExPartnerMapId}"}");

            try
            {
                Bundle bundle = await client.SearchAsync<Observation>(new string[] { $"subject:Location.identifier=https://standards.digital.health.nz/ns/hpi-facility-id|{partner.ExPartnerMapId}" });

                foreach (var entry in bundle.Entry)
                {
                    var resource = (Observation)entry.Resource;
                    // for each coding in the code element of the observation
                    foreach (var coding in resource.Code.Coding)
                    {
                        //if (Array.Exists(codes, element => element.Equals(coding.Code, StringComparison.OrdinalIgnoreCase)) || Array.Exists(codes, element => element.Equals(coding.Display, StringComparison.OrdinalIgnoreCase)))
                        // Check if either code or display field matches any regex
                        if (regexPatterns.Values.Any(pattern => Regex.IsMatch(coding.Code, pattern) || Regex.IsMatch(coding.Display, pattern)))
                        {
                            _logger.LogInformation($"Mapping for {coding.Code} - {coding.Display} found");
                            mappedObservations.Add(resource);
                        }
                        else
                            _logger.LogInformation($"Mapping for {coding.Code} - {coding.Display} not needed, ignoring");
                    }
                }
                return mappedObservations;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in GetVitalsSetupInMedtech: " + ex.ToString());
                return null;
            }
        }

        public static Observation GetMappedObservation(string vitalsRegex, List<Observation> mappedObservations)
        {
            return mappedObservations.FirstOrDefault(obs => obs.Code.Coding.Any(coding => Regex.IsMatch(coding.Code, vitalsRegex) || Regex.IsMatch(coding.Display, vitalsRegex)));
        }

        public async Task<(string MeetingLink, bool IncompatibleHost)> GetOrCreateTeleConfMeeting(string resident_userExId, string doctor_userExId, List<PartnerIntegrationMapping> partner, ILogger logger)
        {
            string meetingLink = string.Empty;
            string meetingEndpoint = "";
            string dbResponse = "";
            bool incompatibleHosting = false; // If the site is using a hosting company that is incompatible with our telehealth system - will offer the user to copy the link to clipboard

            try
            {
                // Get resident mapping
                var residentMap = PartnerIntegrationHelper.GetResidentMap(partner, resident_userExId);
                if (residentMap == null)
                {
                    logger.LogError($"Resident mapping not found for userExId {resident_userExId}");
                    return (string.Empty, incompatibleHosting);
                }
                //var resident_userExId = residentMap.UserExId;

                // Get doctor mapping
                var doctorMap = PartnerIntegrationHelper.GetStaffMap(partner, doctor_userExId);
                if (doctorMap == null)
                {
                    logger.LogError($"Doctor mapping not found for userExId {doctor_userExId}");
                    //return string.Empty;
                    doctorMap = new PartnerIntegrationMapping();
                    doctorMap.UserExId = "";
                }
                //var doctor_userExId = doctorMap.UserExId;

                logger.LogInformation($"We have a request for a teleconf call for Doctor userExId: {doctor_userExId}, and Resident userExId: {resident_userExId}");

                // Get meeting base url
                try
                {
                    dynamic partnerConfig = JsonConvert.DeserializeObject(residentMap.SettingsJSON);
                    if (partnerConfig != null)
                        meetingEndpoint = partnerConfig.meet_endpoint;
                    if (residentMap.SettingsJSON.Contains("incompatible_host"))
                        incompatibleHosting = partnerConfig.incompatible_host;
                }
                catch (RuntimeBinderException)
                {
                    logger.LogError("Error when getting meeting endpoint from partner configuration. Check SettingsJSON. Using a default for production.");
                    meetingEndpoint = "https://portal.healthteams.com.au/meet/{0}";
                }

                // Get the meeting code from the database
                dynamic req = new
                {
                    residentUserExId = resident_userExId,
                    doctorUserExId = doctor_userExId
                };
                //  var json = HTJsonSerialiser.Serialise(req);
                // var res = await DBHelper.ExecSprocWithJsonAsync("dbo.sp_Get_Meeting_Resident_Doctor", json, "");

                dbResponse = await GetMeetingLinkFromDatabase(req);
                logger.LogInformation($"First meeting lookup, link : {dbResponse}");

                if (!string.IsNullOrEmpty(dbResponse))
                {
                    return (dbResponse, incompatibleHosting);
                }
                else
                {
                    //create new telehealth for today
                    var meetingResponse = await CreateTelehealthTaskAsync(residentMap, doctorMap, doctor_userExId);
                    logger.LogInformation($"Create meeting response, link : {meetingResponse}");

                    if (meetingResponse)
                    {
                        dbResponse = await GetMeetingLinkFromDatabase(req);
                        logger.LogInformation($"Second meeting lookup, link : {dbResponse}");

                        if (!string.IsNullOrEmpty(dbResponse))
                        {
                            return (dbResponse, incompatibleHosting);
                        }
                    }

                    logger.LogError("No meeting link found in the database for the given userExIds.");
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Error in GetNextConsultLink : " + ex.ToString());
            }
            logger.LogInformation($"Meeting link to use : {meetingLink}");
            return (meetingLink, incompatibleHosting);

            // See if we have an existing meeting link in the database
            async Task<string> GetMeetingLinkFromDatabase(dynamic req)
            {
                try
                {
                    var json = HTJsonSerialiser.Serialise(req);

                    logger.LogInformation($"calling sp_Get_Meeting_Resident_Doctor json : {json}");

                    var res = await DBHelper.ExecSprocWithJsonAsync("dbo.sp_Get_Meeting_Resident_Doctor", json, "");

                    logger.LogInformation($"return sp_Get_Meeting_Resident_Doctor return : {res}");

                    if (!string.IsNullOrEmpty(res))
                    {
                        dynamic meeting = JsonConvert.DeserializeObject(res);
                        string meetingCode = meeting.MeetingCode; //need to explicitly cast to string
                        if (!String.IsNullOrEmpty(meetingCode))
                        {
                            meetingLink = String.Format(meetingEndpoint, meetingCode);
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Error in GetMeetingLinkFromDatabase : " + ex.ToString());
                }
                return meetingLink;
            }
        }

        private async Task<bool> CreateTelehealthTaskAsync(PartnerIntegrationMapping residentMap, PartnerIntegrationMapping doctorMap, string doctorUserExId)
        {
            try
            {
                var upsertTask = new UpsertTaskRequest
                {
                    Task = new HealthTeamsTask
                    {
                        TaskDateTimeUtc = DateTime.UtcNow, // This is an adhoc telehealth call, so set the time to now
                        AssignedToId = doctorUserExId,
                        ExResidentId = residentMap.UserExId,
                        AssignedToName = doctorMap.EntityFullName,
                        ExFacilityId = doctorMap.ExFacilityId,
                        TaskLocalLocale = doctorMap.FacilityLocaleZone,
                        TaskType = TaskTypes.Telehealth,
                        ExDoctorId = doctorUserExId,
                        TaskStatus = "Pending",
                        IsVitalsRequired = false,
                        TaskDescription = "Telehealth call from Medtech",
                        TaskName = $"Telehealth appointment for {residentMap.EntityFullName} with {doctorMap.EntityFullName}, Dr"
                    }
                };

                var token = TokenHelper.GenerateToken(doctorMap);
                using HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(AzureKeyvaultHelper.Backend_URL);
                client.DefaultRequestHeaders.Add("x-client-tok", token);
                client.DefaultRequestHeaders.Add("x-ExFacilityId", upsertTask.Task.ExFacilityId);
                client.DefaultRequestHeaders.Add("withholdNotifications", "true");

                var resp = await client.PostAsJsonAsync("/api/Task/UpsertTask", upsertTask);

                if (resp.IsSuccessStatusCode)
                {
                    _logger.LogInformation($"/api/Task/UpsertTask call success");
                    var data = await resp.Content.ReadFromJsonAsync<UpsertTaskResponse>();
                    return true;
                }
                _logger.LogError($"/api/Task/UpsertTask status code " + resp.StatusCode.ToString());
                _logger.LogError($"/api/Task/UpsertTask reason " + resp.ReasonPhrase);
                var responseContent = await resp.Content.ReadAsStringAsync();
                _logger.LogError($"/api/Task/UpsertTask response " + responseContent);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in CreateTelehealthTaskAsync : " + ex.ToString());
            }
            return false;
        }

        private async Task<bool> UpsertTelehealthTaskAsync(PartnerIntegrationMapping residentMap, PartnerIntegrationMapping doctorMap, Appointment appointment)
        {
            try
            {
                if (!appointment.Start.HasValue)
                {
                    _logger.LogError($"Appointment {appointment.Id} does not have a start time, we have to skip");
                    return false;
                }

                // Setup the task to upsert
                DateTimeOffset apptStart = appointment.Start.GetValueOrDefault();
                DateTimeOffset apptLastUpdate = appointment.Meta.LastUpdated ?? DateTimeOffset.MinValue;
                var upsertTask = new UpsertTaskRequest();
                upsertTask.Task = new HealthTeamsTask
                {
                    TaskDateTimeUtc = apptStart.UtcDateTime,
                    AssignedToId = doctorMap.UserExId,
                    ExResidentId = residentMap.UserExId,
                    AssignedToName = doctorMap.EntityFullName,
                    ExFacilityId = doctorMap.ExFacilityId,
                    TaskLocalLocale = doctorMap.FacilityLocaleZone,
                    TaskType = TaskTypes.Telehealth,
                    ExDoctorId = doctorMap.UserExId,
                    TaskStatus = "Pending",
                    IsVitalsRequired = false,
                    TaskDescription = "Telehealth appointment scheduled in Medtech",
                    TaskName = $"Telehealth appointment for {residentMap.EntityFullName} with {doctorMap.EntityFullName}, Dr",

                    // Add the appointment id to the task so we can update it later
                    ExternalSourceId = appointment.Id, // Medtech appointment id, mapping so we know which appointment this task is for and can update it later
                    ExternalDateModifiedUtc = apptLastUpdate.UtcDateTime // Last updated time in Medtech, so we can check if the appointment has been updated
                };

                // Get any existing task in our system for this appointment by the source id
                string existingTaskId;
                DateTimeOffset lastUpdated;
                dynamic req = new
                {
                    exFacilityId = doctorMap.ExFacilityId,
                    externalSourceId = appointment.Id
                };
                var json = HTJsonSerialiser.Serialise(req);
                var res = await DBHelper.ExecSprocWithJsonAsync("sp_Get_Task_by_ExternalSourceId", json, "");

                if (!string.IsNullOrEmpty(res))
                {
                    List<HealthTeamsTask>? tasks = HTJsonSerialiser.Deserialise<List<HealthTeamsTask>>(res);
                    var task = tasks.FirstOrDefault();
                    if (task != null)
                    {
                        existingTaskId = task.ExTaskId ?? string.Empty;
                        lastUpdated = task.ExternalDateModifiedUtc ?? DateTimeOffset.MinValue;
                        if (!string.IsNullOrEmpty(existingTaskId))
                        {
                            _logger.LogInformation($"Existing task with Id {existingTaskId} and last updated {lastUpdated} found for appointment {appointment.Id}");

                            // Check if the appointment has been updated in Medtech since the last time we checked
                            if (lastUpdated.ToUniversalTime() >= apptLastUpdate.ToUniversalTime())
                            {
                                _logger.LogInformation($"Appointment {appointment.Id} was last updated {lastUpdated.ToUniversalTime()} and not updated since, skipping");
                                return true;
                            }
                            _logger.LogInformation($"Appointment {appointment.Id} was updated in Medtech {apptLastUpdate.ToUniversalTime()} and in our system {lastUpdated.ToUniversalTime()}, updating");

                            // Update the existing task in our system
                            upsertTask.Task.ExTaskId = existingTaskId;
                        }
                    }
                    else
                        // Create a new task in our system
                        _logger.LogInformation($"Creating a new task as no existing task found for appointment {appointment.Id}");
                }
                else
                    // Create a new task in our system
                    _logger.LogInformation($"Creating a new task as no existing task found for appointment {appointment.Id}");

                var token = TokenHelper.GenerateToken(doctorMap);
                using HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(AzureKeyvaultHelper.Backend_URL);
                client.DefaultRequestHeaders.Add("x-client-tok", token);
                client.DefaultRequestHeaders.Add("x-ExFacilityId", upsertTask.Task.ExFacilityId);
                client.DefaultRequestHeaders.Add("withholdNotifications", "true");
                var resp = await client.PostAsJsonAsync("/api/Task/UpsertTask", upsertTask);
                if (resp.IsSuccessStatusCode)
                {
                    _logger.LogInformation($"/api/Task/UpsertTask call success");
                    var data = await resp.Content.ReadFromJsonAsync<UpsertTaskResponse>();
                    return true;
                }
                _logger.LogError($"/api/Task/UpsertTask status code " + resp.StatusCode.ToString());
                _logger.LogError($"/api/Task/UpsertTask reason " + resp.ReasonPhrase);
                var responseContent = await resp.Content.ReadAsStringAsync();
                _logger.LogError($"/api/Task/UpsertTask response " + responseContent);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in CreateTelehealthTaskAsync : " + ex.ToString());
            }
            return false;
        }

        public async Task<string> CreateResidentDetailsAsync(PartnerIntegrationMapping residentMap, PartnerIntegrationMapping doctorMap, string doctorUserExId)
        {
            try
            {
                var inviteUser = new InviteUserOnCallRequest
                {
                    ExFacilityId = doctorMap.ExFacilityId,
                    FirstName = doctorMap.EntityFullName,
                    LastName = "Medtech Connect",
                    ExResidentId = residentMap.UserExId,
                    Role = "Doctor",
                    // not sending alerts to the doctor
                    Email = "",
                    Mobile = "",
                    MobileCC = ""
                };

                var token = TokenHelper.GenerateToken(doctorMap);

                //_logger.LogInformation($"Token for SendResidentDirectLink call generated for doctor {doctorUserExId} : {token}");

                using HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(AzureKeyvaultHelper.Backend_URL);
                client.DefaultRequestHeaders.Add("x-client-tok", token);
                client.DefaultRequestHeaders.Add("x-ExFacilityId", inviteUser.ExFacilityId);
                client.DefaultRequestHeaders.Add("withholdNotifications", "true");

                //_logger.LogInformation($"BaseAddress : {client.BaseAddress}");
                //_logger.LogInformation($"Request header x-ExFacilityId : {inviteUser.ExFacilityId}");

                var resp = await client.PostAsJsonAsync("/api/Client/SendResidentDirectLink", inviteUser);

                if (resp.IsSuccessStatusCode)
                {
                    //_logger.LogInformation($"/api/Client/SendResidentDirectLink call success");
                    //_logger.LogInformation($"/api/Client/SendResidentDirectLink: {JsonConvert.SerializeObject(resp)}");

                    var data = await resp.Content.ReadFromJsonAsync<InviteUserOnCallResponse>();

                    if (data == null)
                    {
                        _logger.LogError($"/api/Client/SendResidentDirectLink response is NULL");
                    }

                    _logger.LogInformation($"/api/Client/SendResidentDirectLink content response: {JsonConvert.SerializeObject(data)}");

                    return data.InviteLink;
                }
                _logger.LogError($"/api/Client/SendResidentDirectLink status code " + resp.StatusCode.ToString());
                _logger.LogError($"/api/Client/SendResidentDirectLink reason " + resp.ReasonPhrase);
                var responseContent = await resp.Content.ReadAsStringAsync();
                _logger.LogError($"/api/Client/SendResidentDirectLink response " + responseContent);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in CreateResidentDetailsAsync : " + ex.ToString());
            }
            return "";
        }

        public async Task<string> UpsertEntityFromMedtechToHT(List<PartnerIntegrationMapping> partnerIntegrations, string medtecUsertId, string userType = null)
        {
            string userExId = "";
            try
            {
                var partner = partnerIntegrations.FirstOrDefault();

                if (userType == "patient")
                {
                    partner.SourceId = medtecUsertId;
                    var patientResponse = await GetPatientFromMedtech(partner);
                    if (patientResponse.isPatientExist)
                    {
                        var patient = patientResponse.patient;
                        patient.UserExId = medtecUsertId;
                        patient.ExFacilityId = partner.PartnerId.ToString();
                        patient.Role = "Resident";

                        userExId = await DBHelper.ExecSprocWithJsonAsync("dbo.sp_Medtech_User_Upsert", JsonConvert.SerializeObject(patient));
                    }
                }
                else if (userType == "doctor")
                {
                    partner.SourceId = medtecUsertId;
                    var doctorResponse = await GetDoctorFromMedtech(partner);

                    _logger.LogInformation($"GetDoctorFromMedtech response : {JsonConvert.SerializeObject(doctorResponse)}");

                    if (doctorResponse.isDoctorExist)
                    {
                        var doctor = doctorResponse.doctor;
                        doctor.UserExId = medtecUsertId;
                        doctor.Role = "Doctor";
                        doctor.ExFacilityId = partner.PartnerId.ToString();

                        userExId = await DBHelper.ExecSprocWithJsonAsync("dbo.sp_Medtech_User_Upsert", JsonConvert.SerializeObject(doctor));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in UpsertPatientFromMedtechToHT: " + ex.ToString());
                return userExId;
            }
            return userExId;
        }

        // Get a list of all providers in Medtech at the facility - upsert each one into the database
        public async Task<bool> GetAllProvidersFromMedtech(PartnerIntegrationMapping partner)
        {
            try
            {
                string ipAddress = await GetIPAddress();

                FhirClient client = GetFhirClient(partner);
                Bundle bundle = await client.SearchAsync<Practitioner>();

                _logger.LogInformation($"Found {bundle.Entry.Count} providers in Medtech");

                foreach (var entry in bundle.Entry)
                {
                    Practitioner practitionerEntry = (Practitioner)entry.Resource;
                    // Get the id and if active
                    string providerId = practitionerEntry.Id;
                    bool? active = practitionerEntry.Active;

                    // TODO: Maybe we need to deactivate the user in our system, but for now just dont update
                    if (active != true)
                    {
                        _logger.LogInformation($"Provider {providerId} is not active in Medtech, skipping");
                        continue;
                    }

                    // Skip if user has not been updated since last time we upserted to HT
                    var lastUpdated = (practitionerEntry.Meta.LastUpdated ?? DateTimeOffset.MinValue).UtcDateTime;
                    var userMapping = PartnerIntegrationHelper.GetUserPartnerMappingBySourceId(providerId, partner.FacilityId.GetValueOrDefault()).GetAwaiter().GetResult();
                    // Checking if datetime is the same as an update in either system should trigger a new upsert
                    if (lastUpdated == userMapping?.UserDetails?.UserCreatedUtc)
                    {
                        _logger.LogInformation($"Provider {providerId} has not been updated since last time, skipping");
                        continue;
                    }

                    // Upsert the doctor to our system
                    _logger.LogInformation($"Upserting Provider {providerId} from Medtech");
                    var doctor_userExId = await UpsertEntityFromMedtechToHT(new List<PartnerIntegrationMapping> { partner }, providerId, "doctor");
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in GetAllProvidersFromMedtech: " + ex.ToString());
                return false;
            }
        }

        // Get a list of all appointments of a certain type and date range for the provider
        public async Task<List<Appointment>> GetAppointments(PartnerIntegrationMapping doctorMap)
        {
            try
            {
                // Get appointment settings from tblPartnerIntegrationSettings.SettingsJSON
                int appointment_days_future = 14; //default value for how many days into the future to import appts
                string appointment_type_filter = "(?i)Medtech.*(Connect|Telehealth)"; //default value for types of appointments
                dynamic partnerSettings = JsonConvert.DeserializeObject(doctorMap.SettingsJSON);
                if (partnerSettings != null)
                {
                    appointment_days_future = partnerSettings.appointment_days_future;
                    appointment_type_filter = partnerSettings.appointment_type_filter;
                }

                List<Appointment> appointments = new List<Appointment>();
                FhirClient client = GetFhirClient(doctorMap);

                // Get current date (no time) in the local timezone
                DateTime utcNow = DateTime.UtcNow;
                TimeZoneInfo newZealandTimeZone = TimeZoneInfo.FindSystemTimeZoneById(doctorMap.FacilityLocaleZone);
                DateTime nzDate = TimeZoneInfo.ConvertTimeFromUtc(utcNow, newZealandTimeZone).Date;
                DateTimeOffset newZealandDate = new DateTimeOffset(nzDate, newZealandTimeZone.BaseUtcOffset);

                // Format the date and time as a string
                string formattedStart = newZealandDate.ToString("yyyy-MM-ddTHH:mm:sszzz");
                string formattedEnd = newZealandDate.AddDays(appointment_days_future).ToString("yyyy-MM-ddTHH:mm:sszzz");

                // Search for appointments for the specific practitioner within the date range
                var searchParams = new SearchParams()
                    //.Where($"participant=Practitioner/{practitionerId}")
                    .Where($"practitioner._id={doctorMap.SourceId}")
                    .Where($"date=ge{formattedStart}")
                    .Where($"date=le{formattedEnd}");

                Bundle bundle = await client.SearchAsync<Appointment>(searchParams);

                _logger.LogInformation($"Found {bundle.Entry.Count} appointments for provider {doctorMap.SourceId}");

                foreach (var entry in bundle.Entry)
                {
                    Appointment appointment = (Appointment)entry.Resource;

                    // We only want to import appointments from Medtech that are of a certain type
                    // 
                    string appointmentType = appointment.AppointmentType?.Text ?? String.Empty;
                    if (Regex.IsMatch(appointmentType, appointment_type_filter))
                    {
                        // Get the patient details
                        string patientId = appointment.Participant[0].Actor.Reference;
                        // Loop through the participants to find the patient
                        foreach (var participant in appointment.Participant)
                        {
                            if (participant.Actor.Reference.Contains("Patient"))
                            {
                                patientId = participant.Actor.Reference;
                                int lastSlashIndex = patientId.LastIndexOf('/');
                                if (lastSlashIndex >= 0)
                                    patientId = patientId.Substring(lastSlashIndex + 1);
                                break;
                            }
                        }

                        // Get the patient details from Medtech and upsert them into our system
                        var patient_userExId = await UpsertEntityFromMedtechToHT(new List<PartnerIntegrationMapping> { doctorMap }, patientId, "patient");

                        // Get the patient mapping
                        var facilityUserMappings = PartnerIntegrationHelper.GetFacilityPartnerMappingBySourceId(doctorMap.ExPartnerMapId, "Medtech").GetAwaiter().GetResult();
                        var residentMap = PartnerIntegrationHelper.GetResidentMapBySourceId(facilityUserMappings, patientId);
                        if (residentMap == null)
                        {
                            _logger.LogError($"Resident mapping not found for sourceId {patientId}");
                            return null;
                        }
                        _logger.LogInformation($"We need to upsert appointment {appointment.Id} for Resident userExId: {residentMap.UserExId}, and Doctor userExId: {doctorMap.UserExId}");

                        // Upsert the appointment as a telehealth meeting for the appointment
                        var meetingResponse = await UpsertTelehealthTaskAsync(residentMap, doctorMap, appointment);
                    }
                }
                return appointments;
            }
            catch (FhirOperationException ex)
            {
                if (ex.Status == HttpStatusCode.NotFound)
                {
                    _logger.LogInformation($"No appointments found for {doctorMap.SourceId}");
                }
                else
                {
                    _logger.LogError($"Error in GetAppointments for {doctorMap.SourceId}: " + ex.ToString());
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in GetAppointments for {doctorMap.SourceId}: " + ex.ToString());
                return null;
            }
        }

        public async Task<(ResidentUser patient, bool isPatientExist)> GetPatientFromMedtech(PartnerIntegrationMapping partner)
        {
            try
            {
                var htResident = new ResidentUser();

                FhirClient client = GetFhirClient(partner);
                Patient patient = await client.ReadAsync<Patient>($"Patient/{partner.SourceId}");

                if (patient != null)
                {
                    SystemLog("Information", "GetPatientFromMedtech", $"Patient returned with source iD: {partner.SourceId}", HTJsonSerialiser.Serialize(patient));
                    if (patient.Id == String.Empty)
                    {
                        _logger.LogError($"patient not found with source iD- {partner.SourceId}");
                        return (null, false);
                    }
                    if (patient.Name.Count > 0)
                    {
                        HumanName name = patient.Name[0];
                        htResident.FirstName = String.Join(" ", name.Given);
                        htResident.LastName = name.Family;
                    }
                    string address = "";
                    if (patient.Address.Count > 0)
                    {
                        Address addr = patient.Address[0];
                        string lines = String.Join(", ", addr.Line);
                        address = lines + ", " + addr.City + ", " + addr.State + ", " + addr.PostalCode;
                    }

                    foreach (ContactPoint contactPoint in patient.Telecom)
                    {
                        if (contactPoint.System == ContactPoint.ContactPointSystem.Sms)
                        {
                            htResident.ContactMethod = contactPoint.Value;
                        }
                        if (contactPoint.System == ContactPoint.ContactPointSystem.Email)
                        {
                            htResident.Email = contactPoint.Value;
                        }
                        if (contactPoint.System == ContactPoint.ContactPointSystem.Phone && contactPoint.Use == ContactPoint.ContactPointUse.Mobile)
                        {
                            htResident.Mobile = contactPoint.Value;
                        }
                    }
                    htResident.Gender = patient.Gender.ToString();
                    htResident.DOB = DateTime.Parse(patient.BirthDateElement.ToString());
                    htResident.UserCreatedUtc = (patient.Meta.LastUpdated ?? DateTimeOffset.MinValue).UtcDateTime;

                    // Get the patients NHI number (NZ ecquivalent of Medicare number and/or IHI)
                    // Not sure yet if we need to store this, and if so where, either medicare or IHI field, which will need label changes in the UI.
                    string nhi = "";
                    foreach (Identifier identifier in patient.Identifier)
                    {
                        // https://standards.digital.health.nz/ns/nhi-id
                        if (identifier.System.Contains("nhi"))
                        {
                            htResident.IHI = identifier.Value;
                        }
                    }
                    _logger.LogInformation($"Patient found: {htResident.FirstName} {htResident.LastName}");

                    #region Get patient health summary details from Medtech
                    var patientId = partner.SourceId;
                    htResident.Summary ??= new HealthSummary();
                    try
                    {
                        #region Medical History - Long term conditions
                        // Medical History - Long term conditions
                        var searchParams = new SearchParams()
                            .Where($"patient._id={patientId}")
                            .Where("long-term-condition=true")
                            .Where("clinical-status=active"); // Assuming 'active' status for ongoing conditions
                        Bundle bundle = await client.SearchAsync<Condition>(searchParams);
                        SystemLog("Information", "GetPatientFromMedtech", $"Medical history for: {patientId}", HTJsonSerialiser.Serialize(bundle));

                        foreach (var entry in bundle.Entry)
                        {
                            Condition condition = (Condition)entry.Resource;
                            htResident.Summary.ChronicDiseases ??= new List<String>();
                            bool active = condition.ClinicalStatus.Coding[0].Code == "active";
                            if (active)
                            {
                                string codeText = string.Join(", ", condition.Code.Coding.Select(c => $"{c.Display}"));
                                string conditionText = string.Join(", ", condition.Note.Select(n => $"{n.Text}"));
                                DateTime recordedDate;
                                DateTime.TryParse(condition.RecordedDate, out recordedDate);
                                DateOnly dateOnly = DateOnly.FromDateTime(recordedDate);

                                string line = $"{dateOnly.ToString("dd/MM/yy")} - {conditionText}";
                                if (codeText != null)
                                    line += $" ({codeText})";
                                htResident.Summary.ChronicDiseases.Add(line);
                            }
                        }
                        #endregion Medical History - Long term conditions
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error in GetPatientFromMedtech-Medical History: " + ex.ToString());
                        SystemLog("Error", "GetPatientFromMedtech-Medical History", ex.ToString());
                    }
                    try
                    {
                        #region Family History - In Medtech a History created and added to Classifications
                        // Medical History - Long term conditions
                        var searchParams = new SearchParams()
                        .Where($"patient._id={patientId}")
                        .Where("clinical-status=active"); // Assuming 'active' status required
                        Bundle bundle = await client.SearchAsync<Condition>(searchParams);
                        SystemLog("Information", "GetPatientFromMedtech", $"Family history for: {patientId}", HTJsonSerialiser.Serialize(bundle));

                        string family_history_filter = @"^(12\.00|Family History)$"; //default value
                        dynamic partnerSettings = JsonConvert.DeserializeObject(partner.SettingsJSON);
                        try
                        {
                            family_history_filter = partnerSettings.family_history_filter;
                        }
                        catch (Exception) 
                        {
                            SystemLog("Error", "GetPatientFromMedtech-Family History","family_history_filter not defined");
                        }
                        _logger.LogInformation($"Family history filter set to {family_history_filter}");
                        //SystemLog("Information", "GetPatientFromMedtech-Family History", $"Family history filter set to {family_history_filter}");
                        Regex family_history_regex = new Regex(family_history_filter, RegexOptions.IgnoreCase);

                        foreach (var entry in bundle.Entry)
                        {
                            Condition condition = (Condition)entry.Resource;
                            htResident.Summary.FamilyHistory ??= new List<String>();
                            bool active = condition.ClinicalStatus.Coding[0].Code == "active";
                            if (active)
                            {
                                // Find any family history codes
                                var filteredCodings = condition.Code.Coding.Where(coding => family_history_regex.IsMatch(coding.Code) || family_history_regex.IsMatch(coding.Display)).ToList();
                                if (filteredCodings.Count == 0)
                                    continue;
                                // This condition contains family history
                                string codeText = string.Join(", ", condition.Code.Coding.Select(c => $"{c.Display}"));
                                string conditionText = string.Join(", ", condition.Note.Select(n => $"{n.Text}"));
                                DateTime recordedDate;
                                DateTime.TryParse(condition.RecordedDate, out recordedDate);
                                DateOnly dateOnly = DateOnly.FromDateTime(recordedDate);

                                string line = $"{dateOnly.ToString("dd/MM/yy")} - {conditionText}";
                                //if (codeText != null)
                                //    line += $"(Code: {codeText})";
                                htResident.Summary.FamilyHistory.Add(line);
                            }
                        }
                        #endregion Family History
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error in GetPatientFromMedtech-Family History: " + ex.ToString());
                        SystemLog("Error", "GetPatientFromMedtech-Family History", ex.ToString());
                    }
                    try
                    {
                        #region Allergies - Medical Warnings in Medtech
                        var searchParams = new SearchParams()
                        .Where($"patient._id={patientId}")
                        .Where("clinical-status=active"); // Assuming 'active' status required
                        Bundle bundle = await client.SearchAsync<AllergyIntolerance>(searchParams);
                        SystemLog("Information", "GetPatientFromMedtech", $"Allergies for: {patientId}", HTJsonSerialiser.Serialize(bundle));

                        foreach (var entry in bundle.Entry)
                        {
                            AllergyIntolerance allergy = (AllergyIntolerance)entry.Resource;
                            string codeText = string.Join(", ", allergy.Code.Coding.Select(c => $"{c.Display}"));
                            string text = string.Join(", ", allergy.Note.Select(n => $"{n.Text}"));
                            DateTime recordedDate;
                            DateTime.TryParse(allergy.RecordedDate, out recordedDate);
                            DateOnly dateOnly = DateOnly.FromDateTime(recordedDate);
                            htResident.Summary.Allergies ??= new List<String>();
                            string line = $"{dateOnly.ToString("dd/MM/yy")} - {text}";
                            if (codeText != null)
                                line += $" ({codeText})";
                            htResident.Summary.Allergies.Add(line);
                        }
                        #endregion Allergies
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error in GetPatientFromMedtech-Allergies and Medical Warnings: " + ex.ToString());
                        SystemLog("Error", "GetPatientFromMedtech-Allergies and Medical Warnings", ex.ToString());
                    }
                    try
                    {
                        #region Medications - All long term + Last 6 months in Medtech
                        DateTime utcNow = DateTime.UtcNow;
                        TimeZoneInfo newZealandTimeZone = TimeZoneInfo.FindSystemTimeZoneById(partner.FacilityLocaleZone);
                        DateTime nzDate = TimeZoneInfo.ConvertTimeFromUtc(utcNow, newZealandTimeZone).Date;
                        DateTimeOffset newZealandDate = new DateTimeOffset(nzDate, newZealandTimeZone.BaseUtcOffset);
                        string formattedStart = newZealandDate.AddMonths(-6).ToString("yyyy-MM-ddTHH:mm:sszzz");

                        // Get all medications for the last 6 months
                        var searchParams = new SearchParams()
                            .Where($"patient._id={patientId}")
                            .Where($"authoredon=ge{formattedStart}")
                            .Where("status=active"); // Assuming 'active' status required
                        Bundle bundle = await client.SearchAsync<MedicationRequest>(searchParams);
                        SystemLog("Information", "GetPatientFromMedtech", $"Medications-recent for: {patientId}", HTJsonSerialiser.Serialize(bundle));

                        foreach (var entry in bundle.Entry)
                        {
                            MedicationRequest medication = (MedicationRequest)entry.Resource;
                            CodeableConcept medicationCodeableConcept = (CodeableConcept)medication.Medication;
                            string codeText = null;
                            if (medicationCodeableConcept != null)
                            {
                                codeText = string.Join(", ", medicationCodeableConcept.Coding.Select(c => $"{c.Display}"));
                            }
                            string dosage = string.Join(", ", medication.DosageInstruction.Select(d => $"{d.Text}"));
                            DateTime authoredDate;
                            DateTime.TryParse(medication.AuthoredOn, out authoredDate);
                            DateOnly dateOnly = DateOnly.FromDateTime(authoredDate);
                            htResident.Summary.Medications ??= new List<String>();

                            string line = $"{dateOnly.ToString("dd/MM/yy")} - {codeText}";
                            if (!String.IsNullOrEmpty(dosage))
                                line += $" (Dosage: {dosage})";
                            htResident.Summary.Medications.Add(line);
                        }

                        // Get all long term medications
                        searchParams = new SearchParams()
                            .Where($"patient._id={patientId}")
                            .Where("nzeps-long-term-medication=true")
                            .Where("status=active"); // Assuming 'active' status required
                        bundle = await client.SearchAsync<MedicationRequest>(searchParams);
                        SystemLog("Information", "GetPatientFromMedtech", $"Medications-longterm for: {patientId}", HTJsonSerialiser.Serialize(bundle));

                        foreach (var entry in bundle.Entry)
                        {
                            MedicationRequest medication = (MedicationRequest)entry.Resource;
                            CodeableConcept medicationCodeableConcept = (CodeableConcept)medication.Medication;
                            string codeText = null;
                            if (medicationCodeableConcept != null)
                            {
                                codeText = string.Join(", ", medicationCodeableConcept.Coding.Select(c => $"{c.Display}"));
                            }
                            string dosage = string.Join(", ", medication.DosageInstruction.Select(d => $"{d.Text}"));
                            DateTime authoredDate;
                            DateTime.TryParse(medication.AuthoredOn, out authoredDate);
                            DateOnly dateOnly = DateOnly.FromDateTime(authoredDate);
                            htResident.Summary.Medications ??= new List<String>();

                            string line = $"{dateOnly.ToString("dd/MM/yy")} - {codeText}";
                            if (!String.IsNullOrEmpty(dosage))
                                line += $" (Dosage: {dosage})";
                            htResident.Summary.Medications.Add(line);
                        }

                        #endregion Medications
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error in GetPatientFromMedtech-Medications: " + ex.ToString());
                        SystemLog("Error", "GetPatientFromMedtech-Medications", ex.ToString());
                    }
                    #endregion Get patient health summary details from Medtech
                }
                else
                {
                    _logger.LogError($"patient not found with source iD- {partner.SourceId}");
                }
                return (htResident, true);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in GetPatientFromMedtech: " + ex.ToString());
                SystemLog("Error", "GetPatientFromMedtech", $"Error getting patient with source iD: {partner.SourceId} - {ex.ToString()}");
                return (null, false);
            }
        }

        public async Task<(HealthTeamsUser doctor, bool isDoctorExist)> GetDoctorFromMedtech(PartnerIntegrationMapping partner)
        {
            try
            {
                var htDoctor = new HealthTeamsUser();
                FhirClient client = GetFhirClient(partner);
                Practitioner practitioner = await client.ReadAsync<Practitioner>($"Practitioner/{partner.SourceId}");

                if (practitioner != null)
                {
                    SystemLog("Information", "GetDoctorFromMedtech", $"Practitioner returned with source iD: {partner.SourceId}", HTJsonSerialiser.Serialize(practitioner));

                    if (practitioner.Id == String.Empty)
                    {
                        _logger.LogError($"Doctor not found with source iD- {partner.SourceId}");

                        return (null, false);
                    }

                    htDoctor.UserExId = practitioner.Id;
                    htDoctor.DOB = string.IsNullOrEmpty(practitioner.BirthDate) ? null : DateTime.Parse(practitioner.BirthDate);
                    htDoctor.UserCreatedUtc = (practitioner.Meta.LastUpdated ?? DateTimeOffset.MinValue).UtcDateTime;

                    if (practitioner.Name.Count > 0)
                    {
                        HumanName name = practitioner.Name[0];
                        htDoctor.FirstName = String.Join(" ", name.Given);
                        htDoctor.LastName = name.Family;
                        if (String.IsNullOrEmpty(htDoctor.LastName) && String.IsNullOrEmpty(htDoctor.FirstName))
                        {
                            var fullName = name.Text.Split(" ");
                            htDoctor.FirstName = fullName[0];
                            htDoctor.LastName = fullName.Length > 0 ? fullName[1] : fullName[0];
                        }

                    }
                    htDoctor.Gender = string.IsNullOrEmpty(practitioner.Gender.ToString()) ? "Male" : practitioner.Gender.ToString();

                    foreach (ContactPoint contactPoint in practitioner.Telecom)
                    {
                        if (contactPoint.System == ContactPoint.ContactPointSystem.Sms)
                        {
                            htDoctor.ContactMethod = contactPoint.Value;
                        }
                        if (contactPoint.System == ContactPoint.ContactPointSystem.Email)
                        {
                            htDoctor.Email = contactPoint.Value;
                        }
                        if (contactPoint.System == ContactPoint.ContactPointSystem.Phone && contactPoint.Use == ContactPoint.ContactPointUse.Mobile)
                        {
                            htDoctor.Mobile = contactPoint.Value;
                        }
                    }
                    htDoctor.Mobile = htDoctor.Mobile == "" ? htDoctor.ContactMethod : htDoctor.Mobile;

                    _logger.LogInformation($"Doctor found: {htDoctor.FirstName} {htDoctor.LastName}");
                }
                else
                {
                    _logger.LogError($"Doctor not found with source iD- {partner.SourceId}");
                }
                return (htDoctor, true);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in GetDoctorFromMedtech: " + ex.Message);
                SystemLog("Error", "GetDoctorFromMedtech", $"Error getting Practitioner with source iD: {partner.SourceId} - {ex.ToString()}");
                return (null, false);
            }
        }

        public async Task<string> GetIPAddress()
        {
            string ipAddress = "";
            using (HttpClient client = new HttpClient())
            {
                try
                {
                    HttpResponseMessage response = await client.GetAsync("https://whatsmyip.dev/api/ip");
                    response.EnsureSuccessStatusCode();
                    string responseBody = await response.Content.ReadAsStringAsync();

                    // Parse the JSON response
                    JObject json = JObject.Parse(responseBody);
                    ipAddress = json["addr"].ToString();
                    _logger.LogInformation($"The outbound IP Address is: {ipAddress}");
                }
                catch (HttpRequestException e)
                {
                    _logger.LogError($"GetIPAddress error: {e.ToString()}");
                }
            }
            return ipAddress;
        }

        public void SystemLog(string loggingLevel, string whereOccurred, string message, string json = "")
        {
            string process = "Medtech";
            try
            {
                var res = DBHelper.ExecSprocByParams("sp_Insert_Syslog",
                    new System.Collections.Generic.Dictionary<string, string>
                    {
                        {"loggingLevel",loggingLevel},
                        {"process",process},
                        {"whereOccurred",whereOccurred},
                        {"message",message},
                        {"json",json }
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in MedtechIntegratoinHelper.SystemLog: " + ex.ToString());
            }
        }

    }
}
