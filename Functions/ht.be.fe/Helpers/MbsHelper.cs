﻿using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Logging;

namespace ht.be.fn.Helpers
{
    public class MbsHelper
    {
        public string ZipFilePath = string.Empty;
        public int XmlBatchSize = 10;

        private ILogger _logger;

        public MbsHelper(ILogger log)
        {
            _logger=log;
        }

        public async Task<XmlDocument> GetMbsXmlDocument(string xmlLink)
        {
            var xdoc = new XmlDocument();
            xdoc.Load(xmlLink);
            _logger.LogInformation($"Read {xmlLink} and loaded into an XmlDocument {xdoc?.DocumentElement?.ChildNodes.Count}");
            return xdoc;
        }

        public async Task<string> ProcessMBSItems(XmlDocument xdoc)
        {

            return await ProcessMBSElements(xdoc, "dbo.sp_MBSItems_Upsert", "//*[local-name()='MBS_XML']");


        }

       
        public async Task<string> ProcessMBSElements(XmlDocument xdoc, string sproc, string xpath)
        {
            try
            {
                var orgList = xdoc.DocumentElement.SelectSingleNode(xpath);
                var orgs = orgList.ChildNodes;
                var json = JsonConvert.SerializeObject(orgs);
                var jArr = JsonConvert.DeserializeObject<List<JObject>>(json);
                //DBHelper.ExecSproc(sproc, JsonConvert.SerializeObject(jArr));

                //return String.Empty;
                
                var chunks = jArr.Chunk(XmlBatchSize);
                foreach (var c in chunks)
                {
                    _logger.LogInformation($"ProcessMBSElement - {c.Length} of {jArr?.Count}");
                    var jsonArr = JsonConvert.SerializeObject(c);
                    var res = DBHelper.ExecSproc(sproc, jsonArr);
                }
                
                GC.Collect();
            }
            catch (Exception ex)
            {
                throw;
            }

            return null;


        }
    }
}
