﻿using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ht.be.fn.Helpers
{
    public class PbsHelper
    {
        public string ZipFilePath = string.Empty;
        public int XmlBatchSize = 20;

        public async Task<XmlDocument> GetPBSMedicinesDocument(string zipFilePath)
        {
            var xdoc = new XmlDocument();
            var pbsStreamZip = await new HttpClient().GetStreamAsync(zipFilePath);

            using (var zipArch = new ZipArchive(pbsStreamZip))
            {

                if (zipArch.Entries.Count > 0)
                {
                    var entry = zipArch.Entries[0];
                    var fname = entry.FullName;
                    using (var sr = new StreamReader(entry.Open()))
                    {
                        xdoc.LoadXml(sr.ReadToEnd());
                    }
                }
            }
            return xdoc;
        }

        public async Task<string> ProcessOrganisations(XmlDocument xdoc)
        {

            return await ProcessPBSElements(xdoc, "dbo.sp_PBSOrgs_Upsert", "//*[local-name()='organisations-list']");


        }

        public async Task<string> ProcessPharmaList(XmlDocument xdoc)
        {

            return await ProcessPBSElements(xdoc, "dbo.sp_PBSPIL_Upsert", "//*[local-name()='pharmaceutical-items-list']");


        }

        public async Task<string> ProcessDrugsList(XmlDocument xdoc)
        {

            return await ProcessPBSElements(xdoc, "dbo.sp_PBSDrugs_Upsert", "//*[local-name()='drugs-list']");


        }

        public async Task<string> ProcessPBSElements(XmlDocument xdoc, string sproc, string xpath)
        {
            try
            {
                var orgList = xdoc.DocumentElement.SelectSingleNode(xpath);
                var orgs = orgList.ChildNodes;
                var json = JsonConvert.SerializeObject(orgs);
                var jArr = JsonConvert.DeserializeObject<List<JObject>>(json);

                for (int i = 0; i < jArr.Count; i = i + XmlBatchSize)
                {
                    var arrToDB = new List<JObject>();

                    arrToDB.AddRange(jArr.GetRange(i, i + XmlBatchSize < jArr.Count ? XmlBatchSize : jArr.Count - i));
                    var jsonArr = JsonConvert.SerializeObject(arrToDB);

                    var res = DBHelper.ExecSproc(sproc, jsonArr);
                    if (i % 50 == 0)
                    {
                        GC.Collect();
                        System.Diagnostics.Trace.WriteLine($"{i}/{jArr.Count}");
                    }

                }
            }
            catch (Exception ex)
            {
                var x = ex.Message;
            }

            return null;


        }
    }
}
