﻿using ht.be.fn.Utils;
using ht.data.common.partner;
using ht.data.common.Partner.TheLookOut;
using ht.data.common.Telehealth;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
//using Microsoft.Identity.Client;

namespace ht.be.fn.Helpers
{
    public static class PartnerIntegrationHelper
    {
        #region Partner Integration Mapping

        public static async Task<List<PartnerIntegrationMapping>> GetFacilityPartnerMapping(string exFacilityId, string residentExId)
        {
            try
            {
                var mapReq = new GetPartnerIntegrationMappingDetailsRequest
                {
                    ExFacilityId = exFacilityId,
                    UserExId = residentExId
                };
                var json = HTJsonSerialiser.Serialise(mapReq);
                var res = await DBHelper.ExecSprocWithJsonAsync("dbo.sp_Get_Facility_Partner_Mapping_Details", json, "");
                var partnerIntegrationMappingDetails =
                    string.IsNullOrEmpty(res) ? new List<PartnerIntegrationMapping>() :
                       HTJsonSerialiser.Deserialise<List<PartnerIntegrationMapping>>(res);


                return partnerIntegrationMappingDetails;

            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static async Task<List<PartnerIntegrationMapping>> GetFacilityPartnerMappingBySourceId(string sourceId, string sourceName)
        {
            try
            {
                dynamic mapReq = new
                {
                    exPartnerId = sourceId,
                    sourceName = sourceName
                };
                var json = HTJsonSerialiser.Serialise(mapReq);
                var res = await DBHelper.ExecSprocWithJsonAsync("dbo.sp_Get_Facility_Partner_Mapping_By_SourceId", json, "");
                var partnerIntegrationMappingDetails =
                    string.IsNullOrEmpty(res) ? new List<PartnerIntegrationMapping>() :
                       HTJsonSerialiser.Deserialise<List<PartnerIntegrationMapping>>(res);

                return partnerIntegrationMappingDetails;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static async Task<PartnerIntegrationMapping> GetUserPartnerMappingBySourceId(string userSourceId, int facilityId)
        {
            try
            {
                dynamic mapReq = new
                {
                    sourceId = userSourceId,
                    facilityId = facilityId
                };
                var json = HTJsonSerialiser.Serialise(mapReq);
                var res = await DBHelper.ExecSprocWithJsonAsync("dbo.sp_Get_User_Partner_Mapping_By_SourceId", json, "");
                List<PartnerIntegrationMapping> mappingList = string.IsNullOrEmpty(res) ? new List<PartnerIntegrationMapping>() : HTJsonSerialiser.Deserialise<List<PartnerIntegrationMapping>>(res);
                return mappingList.FirstOrDefault();
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static PartnerIntegrationMapping GetStaffMap(List<PartnerIntegrationMapping> partner, string userExId)
        {
            return partner.FirstOrDefault(p => string.IsNullOrEmpty(userExId) ? p.EntityType.ToLower() == "nurse" : p.UserExId.ToUpper() == userExId.ToUpper());
        }

        public static PartnerIntegrationMapping GetResidentMap(List<PartnerIntegrationMapping> partner, string residentExId)
        {
            return partner.FirstOrDefault(p => p.UserExId.ToUpper() == residentExId.ToUpper());
        }

        public static PartnerIntegrationMapping GetResidentMapBySourceId(List<PartnerIntegrationMapping> partner, string sourceId)
        {
            return partner.FirstOrDefault(p => p.SourceId.ToLower() == sourceId.ToLower() && p.EntityType.ToLower() == "resident");
        }

        public static PartnerIntegrationMapping GetDoctorMapBySourceId(List<PartnerIntegrationMapping> partner, string sourceId)
        {
            return partner.FirstOrDefault(p => p.SourceId.ToLower() == sourceId.ToLower() && p.EntityType.ToLower() == "doctor");
        }

        public static bool IsPartnerTheLookOut(List<PartnerIntegrationMapping> partnerMappingDetails)
        {
            return partnerMappingDetails.Any(p => p.PartnerName == "TheLookOut");
        }

        #endregion Partner Integration Mapping

        #region Sending Vitals To Integration Partner

        public static async Task SendVitalToLookOut(string userExId, string residentExId, string exTransactionId, List<PartnerIntegrationMapping> partner, ILogger _logger)
        {
            var data = FetchMonitorData(exTransactionId);
            LogInformation(_logger, data);

            await SendNormalVitals(data, userExId, residentExId, partner, _logger);
            await SendAlertVitals(data, userExId, residentExId, partner, _logger);

        }

        private static async Task SendNormalVitals(List<TelehealthMonitorData> data,
            string userExId, string residentExId, List<PartnerIntegrationMapping> partner, ILogger _logger)
        {

            var vitalTaskNote = new StringBuilder();
            bool isVitalData = false;
            vitalTaskNote.Append("The following vitals have been recorded in Health Teams Connect:<br>");

            foreach (var read in data)
            {
                foreach (var vital in read.Readings)
                {

                    if (vital?.IsAbnormal == null || vital?.IsAbnormal.Value == false || vital?.Files?.Count > 0)
                    {

                        isVitalData = true;
                        var th = read?.Thresholds?.Limits?.FirstOrDefault();
                        if (vital?.Files?.Count > 0)
                        {
                            AppendFileVitals(vitalTaskNote, vital);
                        }
                        else if (!string.IsNullOrEmpty(vital.AdditionalJSON))
                        {
                            AppendUA(vitalTaskNote, vital);
                        }
                        else
                        {
                            _logger.LogInformation($"Appending these vitals -{vital}");
                            _logger.LogInformation($"Using these thresholds -{th}");
                            AppendVitals(vitalTaskNote, vital, th);
                        }
                    }
                }
            }
            if (isVitalData)
            {
                // var req = CreateLookOutVitalRequest(vitalTaskNote.ToString(), userExId, partner);

                var staffMap = GetStaffMap(partner, userExId);
                var resdeintMap = GetResidentMap(partner, residentExId);
                dynamic partnerConfig = JsonConvert.DeserializeObject(staffMap.SettingsJSON);

                var req = new TheLookOutProgressNoteRequest()
                {
                    Data = new ProgressNoteRequest()
                    {
                        Note_type = "condition_report",
                        Title = $"Vitals Taken for {resdeintMap.EntityFullName}",
                        Creator_id = Convert.ToInt32(staffMap.SourceId),
                        Content = vitalTaskNote.ToString(),

                    }
                };
                await SendVitalRequestToLookOut(userExId, residentExId, req, partner);

            }
            await Task.CompletedTask;
        }

        private static async Task SendAlertVitals(List<TelehealthMonitorData> data, string userExId, string residentExId, List<PartnerIntegrationMapping> partner, ILogger _logger)
        {
            bool isAlert = false;
            var vitalTaskNote = new StringBuilder();
            vitalTaskNote.Append("The following vitals have triggered an alert from Health Teams Connect:<br>");

            foreach (var read in data)
            {
                foreach (var vital in read.Readings)
                {
                    if ((vital.IsAlert.HasValue && vital.IsAlert.Value) || (vital.IsAbnormal.HasValue && vital.IsAbnormal.Value))
                    {
                        var th = read.Thresholds.Limits.FirstOrDefault();

                        AppendAlertOrWarningVitals(vitalTaskNote, vital, th);
                        isAlert = true;
                    }

                }
            }
            if (isAlert)
            {
                var staffMap = GetStaffMap(partner, userExId);
                var residentMap = GetResidentMap(partner, residentExId);

                var req = new TheLookOutObservationRequest()
                {
                    Data = new ObservationRequest()
                    {
                        Reporter_kind = "external",
                        Categories = new[] { "risk" },
                        Client_id = Convert.ToInt32(residentMap.SourceId),
                        Note = vitalTaskNote.ToString(),
                        Observed_at = DateTime.UtcNow,
                        Reporter_source = staffMap.EntityFullName
                    }
                };

                await SendAlertVitalRequestToLookOut(req, staffMap);
            }

        }

        private static void AppendFileVitals(StringBuilder vitalTaskNote, TelehealthDataItem vital)
        {
            vital.Files.ForEach(p =>
            {
                vitalTaskNote.AppendLine(@$"<b>{vital.ItemType}:<b>");
                vitalTaskNote.AppendLine(@$"{p.Comment}");
                p.FileUrls.ForEach(f =>
                {
                    vitalTaskNote.AppendLine(@$"<a target='_blank' href='{f.FileUrl}'>{p.FileName}</a><br>");
                });
            });
        }

        private static void AppendVitals(StringBuilder vitalTaskNote, TelehealthDataItem vital, MonitoringThreshold th)
        {
            if (th != null && th?.Expected != null)
            {
                vitalTaskNote.AppendLine(@$"<b>{vital.ItemType}</b> {vital?.ReadValue} {vital?.ReadUnits} ({th?.Expected} {vital?.ReadUnits} expected) - Normal Range<br>");
            }
            else
            {
                vitalTaskNote.AppendLine(@$"<b>{vital?.ItemType}</b> {vital?.ReadValue} {vital?.ReadUnits} ");
            }
        }

        public static void AppendUA(StringBuilder vitalTaskNote, TelehealthDataItem vital)
        {
            List<Dictionary<string, string>> UA = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(vital.AdditionalJSON);
            vitalTaskNote.AppendLine(@$"<b>{vital?.ItemType.ToString()}</b> <br>");

            foreach (var item in UA)
            {
                foreach (var pair in item)
                {
                    vitalTaskNote.AppendLine(@$"{pair.Key}:         {pair.Value}<br>");
                }

            }

        }

        private static void AppendAlertOrWarningVitals(StringBuilder vitalTaskNote, TelehealthDataItem vital, MonitoringThreshold th)
        {
            string alertType = !vital.IsAlert.HasValue && vital.IsAbnormal == true
                              ? "Warning" :
                               vital.IsAlert == true ? "Urgent"
                              : "Warning";

            if (th != null && th?.Expected != null)
            {

                vitalTaskNote.Append(@$"<b>{vital.ItemType}</b> {vital.ReadValue} {vital.ReadUnits} ({th.Expected} {vital.ReadUnits} expected) - {alertType}<br>");
            }
            else
            {
                vitalTaskNote.Append(@$"<b>{vital.ItemType}</b> {vital.ReadValue} {vital.ReadUnits} ");
            }
        }

        private static async Task SendVitalRequestToLookOut(string userExId, string residentExId, TheLookOutProgressNoteRequest tlwReq, List<PartnerIntegrationMapping> partner)
        {
            var staffMap = GetStaffMap(partner, userExId);
            var residentMap = GetResidentMap(partner, residentExId);

            dynamic partnerConfig = JsonConvert.DeserializeObject(residentMap.SettingsJSON);
            using HttpClient client = new HttpClient { BaseAddress = new Uri($"{residentMap.IntegrationURL}") };
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", (string)partnerConfig.authorization);

            var json = new StringContent(HTJsonSerialiser.Serialise(tlwReq), Encoding.UTF8, "application/json");
            var res = await client.PostAsync($"{residentMap.PartnerId}/clients/{residentMap.SourceId}/notes", json);
        }

        private static async Task SendAlertVitalRequestToLookOut(TheLookOutObservationRequest tlwReq, PartnerIntegrationMapping partner)
        {

            dynamic partnerConfig = JsonConvert.DeserializeObject(partner.SettingsJSON);
            using HttpClient client = new HttpClient { BaseAddress = new Uri($"{partner.IntegrationURL}") };
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", (string)partnerConfig.authorization);

            var json = new StringContent(HTJsonSerialiser.Serialise(tlwReq), Encoding.UTF8, "application/json");
            var res = await client.PostAsync($"{partner.PartnerId}/observations", json);
        }

        #endregion Sending Vitals To Integration Partner

        #region Misc Helpers

        private static void LogInformation(ILogger logger, object data)
        {
            logger.LogInformation(HTJsonSerialiser.Serialize(data));
        }

        public static List<TelehealthMonitorData> FetchMonitorData(string exTransactionId)
        {
            var res = DBHelper.ExecSprocById("dbo.sp_MonitorData_Get_ByTransactionId", exTransactionId);
            var data = HTJsonSerialiser.Deserialise<List<TelehealthMonitorData>>(res);

            return data;
        }

        #endregion Misc Helpers

    }


}
