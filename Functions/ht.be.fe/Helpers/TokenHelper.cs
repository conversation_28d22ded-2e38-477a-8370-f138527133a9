﻿using ht.data.common.partner;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.fn.Helpers
{
    public class TokenHelper
    {
        public string _signingKey { get; set; }
        public SymmetricSecurityKey _secretKey { get; set; }

        public long _secsToLive = 1800;

        public TokenHelper(string key = "")
        {
            if (key != null)
            {
                _signingKey = key;
                _secretKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(key));
            }
        }
        public TokenHelper(string key, long secsToLive = 1800)
        {
            _signingKey = key;
            _secretKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(key));
            _secsToLive = secsToLive;

        }
        public static string GenerateToken(PartnerIntegrationMapping mapping)
        {
            var mySecret = "asdv234234^&%&^%&^hjsdfb2%%%%%%%%%%%%%%%";
            var mySecurityKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(mySecret));

            var myIssuer = "http://mysite.com";
            var myAudience = "http://myaudience.com";

            var tokenHandler = new JwtSecurityTokenHandler();
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new Claim[]
                {
                new Claim(ClaimTypes.NameIdentifier, mapping.UserExId),
               new Claim("ht_roles", Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new JwtUserAndRoles()
               {
                   Roles= new ()
                   {
                       new JwtUserAndRoles.HTRole(){ExFacilityId=mapping.ExFacilityId,Name=mapping.EntityFullName,Role="Doctor"}
                   },
                   UserExId = mapping.UserExId,

               })))),

                }),
                Expires = DateTime.UtcNow.AddDays(7),
                Issuer = myIssuer,
                Audience = myAudience,
                SigningCredentials = new SigningCredentials(mySecurityKey,
                    SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        public ClaimsPrincipal ValidateToken(string tok)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var tokParams = new TokenValidationParameters
            {
                ValidateAudience = false,
                ValidateIssuer = false,
                ValidateLifetime = false
            };

            try
            {
                var principal = tokenHandler.ValidateToken(tok, tokParams, out var securityToken);
                if (!(securityToken is JwtSecurityToken jwtSecurityToken) || !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                {
                    var x = 5;
                }
                return principal;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }

    class JwtUserAndRoles
    {
        public string UserExId { get; set; }
        public List<HTRole> Roles { get; set; }

        public class HTRole
        {
            public string Role { get; set; }
            public string ExFacilityId { get; set; }
            public string Name { get; set; }
        }
    }
}
