﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.fn.Helpers
{
    public class DBHelper
    {
        public static string dbConnStr = null;
        static DBHelper()
        {
            dbConnStr = Environment.GetEnvironmentVariable("htdb_connStr");
        }

        public static SqlConnection GetConnection()
        {
            var cb = new SqlConnectionStringBuilder(dbConnStr);
            return new SqlConnection(cb.ConnectionString);
        }

        public static bool TestDBConnection()
        {
            using (var cnn = GetConnection())
            {
                cnn.Open();
                cnn.Close();
            }
            return true;
        }

        public static async Task<string> ExecSprocWithJsonAsync(string sprocName, string json)
        {
            return ExecSprocByParams(sprocName, new Dictionary<string, string> { { "json", json } });
        }
        public static async Task<string> ExecSprocWithJsonAsync(string sprocName, string json,string jsonSecurity)
        {
            return ExecSprocByParams(sprocName, new Dictionary<string, string> { { "json", json }, { "jsonSecurity", jsonSecurity } });
        }

        public static string ExecSprocByParams(string sprocName, Dictionary<string, string> props = null)
        {
            try
            {
                using (var cmd = new SqlCommand(sprocName, GetConnection()) { CommandType = System.Data.CommandType.StoredProcedure, CommandTimeout = 240 })
                {
                    cmd.Connection.Open();
                    if (props != null)
                    {
                        foreach (var p in props)
                        {
                            cmd.Parameters.Add($"@{p.Key}", System.Data.SqlDbType.VarChar, -1).Value = p.Value;
                            cmd.Parameters[$"@{p.Key}"].Direction = System.Data.ParameterDirection.Input;
                        }
                    }
                    var sb = new StringBuilder();
                    using (var sr = cmd.ExecuteReader())
                    {
                        while (sr.Read())
                            sb.Append(sr[0].ToString());
                    }
                    cmd.Connection.Close(); //force close a connection.
                    return sb.ToString();
                }

            }
            catch (Exception ex)
            {
                throw;
                var temp = ex;
            }
            return string.Empty;
        }

        public static string ExecSproc(string sprocName, string json, Dictionary<string, string> additionalParameters = null)
        {

            try
            {
                using (var cmd = new SqlCommand(sprocName, GetConnection()) { CommandType = System.Data.CommandType.StoredProcedure })
                {
                    cmd.Connection.Open();
                    cmd.Parameters.Add("@json", System.Data.SqlDbType.VarChar, -1).Value = json;
                    cmd.Parameters["@json"].Direction = System.Data.ParameterDirection.Input;
                    if (additionalParameters != null)
                    {
                        foreach (var p in additionalParameters)
                        {
                            cmd.Parameters.Add(p.Key, System.Data.SqlDbType.VarChar, 100).Value = p.Value;
                        }
                    }
                    //cmd.Parameters.Add("@format", System.Data.SqlDbType.VarChar, 1).Value = 'J';
                    //cmd.Parameters["@format"].Direction = System.Data.ParameterDirection.Input;
                    var sb = new StringBuilder();
                    using (var sr = cmd.ExecuteReader())
                    {
                        while (sr.Read())
                            sb.Append(sr[0].ToString());
                    }
                    cmd.Connection.Close();
                    return sb.ToString();
                }

            }
            catch (Exception ex)
            {
                throw;
                var temp = ex;
            }
            return string.Empty;
        }

        public static string ExecSprocById(string sprocName, string id, string jsonSecurity)
        {
            try
            {
                using (var cmd = new SqlCommand(sprocName, GetConnection()) { CommandType = System.Data.CommandType.StoredProcedure })
                {
                    cmd.Connection.Open();
                    cmd.Parameters.Add("@id", System.Data.SqlDbType.VarChar).Value = id;
                    cmd.Parameters["@id"].Direction = System.Data.ParameterDirection.Input;
                    cmd.Parameters.Add("@jsonSecurity", System.Data.SqlDbType.VarChar,-1).Value = id;
                    cmd.Parameters["@jsonSecurity"].Direction = System.Data.ParameterDirection.Input;

                    var sb = new StringBuilder();
                    using (var sr = cmd.ExecuteReader())
                    {
                        while (sr.Read())
                            sb.Append(sr[0].ToString());
                    }
                    cmd.Connection.Close();
                    return sb.ToString();
                }

            }
            catch (Exception ex)
            {
                var temp = ex;
            }
            return string.Empty;
        }

        public static string ExecSprocById(string sprocName, string id)
        {

            try
            {
                using (var cmd = new SqlCommand(sprocName, GetConnection()) { CommandType = System.Data.CommandType.StoredProcedure })
                {
                    cmd.Connection.Open();
                    cmd.Parameters.Add("@id", System.Data.SqlDbType.VarChar).Value = id;
                    cmd.Parameters["@id"].Direction = System.Data.ParameterDirection.Input;
                    var sb = new StringBuilder();
                    using (var sr = cmd.ExecuteReader())
                    {
                        while (sr.Read())
                            sb.Append(sr[0].ToString());
                    }
                    cmd.Connection.Close();
                    return sb.ToString();
                }

            }
            catch (Exception ex)
            {
                var temp = ex;
            }
            return string.Empty;
        }

        public static string ExecSprocById(string sprocName, long id)
        {

            try
            {
                using (var cmd = new SqlCommand(sprocName, GetConnection()) { CommandType = System.Data.CommandType.StoredProcedure })
                {
                    cmd.Connection.Open();
                    cmd.Parameters.Add("@id", System.Data.SqlDbType.BigInt).Value = id;
                    cmd.Parameters["@id"].Direction = System.Data.ParameterDirection.Input;
                    //cmd.Parameters.Add("@format", System.Data.SqlDbType.VarChar, 1).Value = 'J';
                    //cmd.Parameters["@format"].Direction = System.Data.ParameterDirection.Input;
                    var sb = new StringBuilder();
                    using (var sr = cmd.ExecuteReader())
                    {
                        while (sr.Read())
                            sb.Append(sr[0].ToString());
                    }
                    cmd.Connection.Close();
                    return sb.ToString();
                }

            }
            catch (Exception ex)
            {
                var temp = ex;
            }
            return string.Empty;
        }

        public static string ExecSprocBySearch(string sprocName, string search)
        {

            try
            {
                using (var cmd = new SqlCommand(sprocName, GetConnection()) { CommandType = System.Data.CommandType.StoredProcedure })
                {
                    cmd.Connection.Open();
                    cmd.Parameters.Add("@search", System.Data.SqlDbType.NVarChar).Value = search;
                    cmd.Parameters["@search"].Direction = System.Data.ParameterDirection.Input;
                    var sb = new StringBuilder();
                    using (var sr = cmd.ExecuteReader())
                    {
                        while (sr.Read())
                            sb.Append(sr[0].ToString());
                    }
                    cmd.Connection.Close();
                    return sb.ToString();
                }

            }
            catch (Exception ex)
            {
                var temp = ex;
            }
            return string.Empty;
        }
    }
}

