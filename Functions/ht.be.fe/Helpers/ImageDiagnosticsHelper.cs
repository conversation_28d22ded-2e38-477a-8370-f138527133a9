﻿using OpenCvSharp;
using OpenCvSharp.Aruco;
using AR = OpenCvSharp.Aruco;
using OpenCvSharp.Extensions;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.Azure.Amqp.Serialization.SerializableType;
using ht.data.common.Wounds;
using System.IO;
using System.Net;
using static System.Net.Mime.MediaTypeNames;

namespace ht.be.fn.Helpers;

public static class ImageDiagnosticsHelper
{

    public static List<ImageDiag> ProcessImages(List<ImageDiag> imageDiags, bool? detailed=false)
    {
        //we need to loop through the images and process them.
        foreach (var imdiag in imageDiags)
        {
            var img = GetImage(imdiag);
            if (img != null) {
                Bitmap complexBmp = null;
                //start the work.
                var edged = ReturnEdgedImage(new Bitmap(img));
                var contours = FindContours(edged);
                if (contours != null) {
                    
                    var lst = new List<object>();
                    lst.AddRange(contours);
                    imdiag.Contours = lst;
                }
                if (detailed.GetValueOrDefault(false)==true) {
                    complexBmp = GetCircles(contours, edged)?.Item2;
                    imdiag.CompositeBytes = GetBytesFromImage(complexBmp);
                    imdiag.GreyScaleBytes = GetBytesFromImage(edged);
                }
                else {
                    imdiag.Bytes = null;
                }
                
            }
        }


        return imageDiags;
    }

    private static System.Drawing.Image GetImage(ImageDiag imgdiag)
    {     
        byte[] bytes= null;
        if (!string.IsNullOrEmpty(imgdiag.ImageUrl))
        {
            using (var wc = new WebClient())
            {
                bytes = wc.DownloadDataTaskAsync(new Uri(imgdiag.ImageUrl)).GetAwaiter().GetResult();
            }
        } 
        else if (imgdiag?.Bytes?.Length > 0)
        {
            bytes = imgdiag.Bytes;
        }

        if (bytes != null)
        {
            using (var ms = new MemoryStream(imgdiag.Bytes))
            {
                return System.Drawing.Image.FromStream(ms);
            }
        }
        return null;

    }

    public static byte[] GetBytesFromImage(this System.Drawing.Bitmap bmp)
    {
        if (bmp == null)
            return null;
        using(var ms = new MemoryStream())
        {
            bmp?.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
            return ms.ToArray();
        }
    }

    public static byte[] GetBytesFromImage(this System.Drawing.Image img)
    {
        return GetBytesFromImage(new Bitmap(img));
    }



    // F = (P x D)/W   - W known width, P - width in pixels, D istance from marker.
    public static Bitmap ReturnEdgedImage(Bitmap img)
    {
        var rect = new RectangleF(0, 0, img.Width, img.Height);
        Bitmap imgOut = img.Clone(rect, img.PixelFormat);

        var src = BitmapConverter.ToMat(img);
        var dst = BitmapConverter.ToMat(imgOut);
        Mat edged = new Mat();

        Cv2.CvtColor(src, dst, ColorConversionCodes.BGR2GRAY);
        Cv2.GaussianBlur(dst, dst, new OpenCvSharp.Size(5, 5), 0);
        Cv2.Canny(dst, edged, 35, 125);

        return edged.ToBitmap();
    }

    public static IOrderedEnumerable<ContourInfo> FindContours(Bitmap img)
    {
        var src = BitmapConverter.ToMat(img);
        var cnts = Cv2.FindContoursAsArray(src, RetrievalModes.List, ContourApproximationModes.ApproxSimple);


        var sorted = GetSortedList(cnts);
        //var rr = MaxContour(sorted);

        return sorted;
    }

    public static Tuple<IEnumerable<ContourInfo>, Bitmap> GetCircles(IOrderedEnumerable<ContourInfo> cnts, Bitmap img)
    {
        var kr = new List<ContourInfo>();
        var src = BitmapConverter.ToMat(img);

        Cv2.CvtColor(src, src, ColorConversionCodes.GRAY2BGR);
        src = new Mat(src.Size(), src.Type());
        foreach (var c in cnts)
        {
            var arry = new[] { c.Contour }.ToList();
            var colour = Scalar.Red;
            //Cv2.DrawContours(src, arry, 0, colour,-1);
            var approx = Cv2.ApproxPolyDP(c.Contour, 0.03 * Cv2.ArcLength(c.Contour, true), true);
            if (approx?.Length == 3)
            {
                //lst.Items.Add($"triangle");
                //Cv2.DrawContours(src,arry , 0, Scalar.Red);
            }
            else if (approx?.Length == 4)
            {
                //lst.Items.Add($"square");
                //Cv2.DrawContours(src, arry, 0, Scalar.Yellow);
            }
            else if (approx?.Length == 8)
            {
                //lst.Items.Add($"8 sides");
                var k = Cv2.IsContourConvex(approx);
                if (k)
                {
                    //lst.Items.Add("   CIRCLE");
                    kr.Add(c);
                }
                Cv2.DrawContours(src, arry, 0, Scalar.Red, 4);

                //Get bounding box.
                c.Box = Cv2.MinAreaRect((IEnumerable<OpenCvSharp.Point>)arry.First());
                var lines = new List<List<OpenCvSharp.Point>>();
                var pts = new List<OpenCvSharp.Point>();

                foreach (var p in c.Box.Points())
                    pts.Add(p.ToPoint());

                lines.Add(pts);
                Cv2.Polylines(src, lines, true, Scalar.Blue, 4);
                Cv2.Circle(src, c.Box.Center.ToPoint(), 5, Scalar.Red, -1);
                var cp = c.Box.Center.ToPoint();
                cp.X -= 80;
                cp.Y -= 15;
                Cv2.PutText(src, $"Width: {(int)c.Box.Size.Width}", cp, HersheyFonts.HersheyPlain, 2, Scalar.Green, 2);
                cp.Y += 30;
                Cv2.PutText(src, $"Height: {(int)c.Box.Size.Height}", cp, HersheyFonts.HersheyPlain, 2, Scalar.Green, 2);


            }
        }
        var img2 = BitmapConverter.ToBitmap(src);

        return Tuple.Create<IEnumerable<ContourInfo>, Bitmap>(kr, img2);
    }

    public static RotatedRect MaxContour(IOrderedEnumerable<ContourInfo> orderedList)
    {

        var one = orderedList?.FirstOrDefault();
        return (Cv2.MinAreaRect(one.Contour));
    }
    public static IOrderedEnumerable<ContourInfo> GetSortedList(OpenCvSharp.Point[][] contours)
    {
        var dict = new Dictionary<int, ContourInfo>();
        foreach (var cnt in contours)
        {
            var ci = new ContourInfo
            {
                Contour = cnt,
                Area = Cv2.ContourArea(cnt),
                Rank = dict.Count
            };
            if (ci?.Area > 0)
                dict[dict.Count] = ci;
        }

        var sorted = dict.Values.OrderByDescending(s => s.Area);
        return (sorted);
    }

    public static ContourInfo GetContourFromPoint(Point2f pt, IOrderedEnumerable<ContourInfo> cnts)
    {
        if (cnts?.Count() > 0)
            foreach (var c in cnts?.Where(x => x.Area > 0))
            {
                IEnumerable<OpenCvSharp.Point> points = c?.Contour?.ToList();
                if (Cv2.PointPolygonTest(c.Contour.ToList(), pt, false) > -1)
                {
                    return (c);
                }
            }
        return null;
    }

    public static double GetMMPerPixel(double pixelArea)
    {
        var knownObjectArea = 282857.14; ;
        var result = Math.Sqrt(knownObjectArea / pixelArea);
        return result;

    }

    public static Point2f[][] MarkerDetection(Bitmap img)
    {
        var src = BitmapConverter.ToMat(img);
        //DetectorParameters parms = DetectorParameters.Create();
        DetectorParameters parms = new DetectorParameters();
        AR.Dictionary dict = AR.CvAruco.GetPredefinedDictionary(PredefinedDictionaryName.Dict6X6_250);

        AR.CvAruco.DetectMarkers(src, dict, out Point2f[][] corners, out int[] ids, parms, out Point2f[][] rejectedPoints);
        return (corners);
    }

    public static Mat PrintMarker(Mat img)
    {
        var dict = AR.CvAruco.GetPredefinedDictionary(PredefinedDictionaryName.Dict6X6_250);
        dict.GenerateImageMarker(23, 200, img, 1);
        //AR.CvAruco.DrawMarker(dict, 23, 200, img, 1);
        return (img);
    }


}

public class ContourInfo
{
    public OpenCvSharp.Point[] Contour { get; set; }
    public double Area { get; set; }
    public int Rank { get; set; }
    public RotatedRect Box { get; set; }
}

