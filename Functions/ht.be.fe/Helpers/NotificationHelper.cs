﻿using ht.be.fn.Utils;
using ht.common.backend.shared.classes;
using ht.common.backend.shared.classes.Notifications;

using Microsoft.Extensions.Logging;

using PhoneNumbers;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
namespace ht.be.fn.Helpers
{
    public class NotificationHelper
    {
        ILogger _log;
        public static Dictionary<string, string> LinkUrls { get; set; }
        public static string EmailUrl { get; set; }
        public static string apimKey { get; set; }
        public static string SMSUrl { get; set; }

        private Dictionary<string, NotificationTemplate> Templates { get; set; } = new();


        public NotificationHelper(ILogger log)
        {
            _log = log;
        }

        public async Task<string> ProcessNotifications()
        {
            _log.LogInformation("Starting ProcessNotifications");
            DBHelper.ExecSproc("sp_JOBS_Scheduled_60mins", null);
            var json = DBHelper.ExecSproc("sp_Notifications_LogicApps_Get", null);
            var notifications = HTJsonSerialiser.Deserialise<NotificationsRequest>(json);
            if (notifications?.Notifications?.Count > 0)
            {
                var lst = new List<HTNotification>();
                //Populate Templates
                foreach (var t in notifications.Templates)
                    Templates[t.TemplateType] = t;

                //process either emails or sms
                foreach (var n in notifications.Notifications)
                {

                    LinkUrls.TryGetValue(n.NoficationType, out var url);
                    if (!IsValidTime(n.EntityDateUtc))
                    {
                        n.Status = "Error";
                        n.NotificationResult = $"ERROR: Notification Date occurrs too far in the past.";
                        lst.Add(n);
                    }
                    else if (n.Status == "Created")
                    {
                        switch (n.NoficationType)
                        {
                            case "ApptReminder":
                                {
                                    Templates.TryGetValue(n.NoficationType, out var tmp);
                                    //  var t = processAppointmentReminders(n, url, tmp);
                                    //  lst.Add(t);
                                    break;
                                }
                            case "VitalsAlert":
                                {
                                    var t = ProcessAlertNotification(n);
                                    lst.Add(t);
                                    break;
                                }
                        }
                    }
                    //else if (Templates.TryGetValue(n.NoficationType,out var tmp))
                    //{
                    //    switch (n.NoficationType)
                    //    {
                    //        case "ApptReminder":
                    //            {
                    //                var t = processAppointmentReminders(tmp,n,url);
                    //                lst.Add(t);
                    //                break;
                    //            }
                    //    }
                    //}
                    //else
                    //{
                    //    n.Status = "Error";
                    //    n.NotificationResult = $"ERROR: Template {n.NoficationType} not found in Template Collection";
                    //    lst.Add(n);
                    //}

                }

                //Save the notifications.
                var jsNotify = HTJsonSerialiser.Serialise(lst);
                DBHelper.ExecSprocWithJsonAsync("dbo.sp_Notifications_Processing_Upsert", jsNotify).GetAwaiter().GetResult();

            }
            else
            {
                _log.LogInformation($"ProcessNotifications - No records found {json} ");
            }
            return null;
        }

        private bool IsValidTime(DateTime eventDateUtc)
        {
            var dt = DateTime.UtcNow;
            var ts = eventDateUtc.Subtract(dt);
            return (ts.TotalMinutes > -60 * 10);  //10 hours late

        }

        //anything specific we need to process here.
        private HTNotification processAppointmentReminders(HTNotification n, string url, NotificationTemplate tmp = null)
        {
            var msg = tmp.Template;
            var msgUrl = string.Empty + url?.Replace("{exTaskId}", n.ExEntityId);

            var sb = new StringBuilder();


            if (!string.IsNullOrEmpty(n.NotificationMinutes))
            {
                if (int.TryParse(n.NotificationMinutes, out var minutes))
                {

                }

            }



            //replace characters
            //msg = msg.Replace("{url}", msgUrl).Replace("{first_name}", "");

            if (n.DeliveryMethod?.ToLower() == "email")
            {
                sb.AppendLine("Hi, welcome to your HealthTeams Appointment reminder.");
                sb.AppendLine("");
                sb.AppendLine("Your appointment is scheduled shortly, we trust you'll have a productive meeting.");


                sb.AppendLine("Appointment Details are as follow:");
                sb.AppendLine($"{n.Name}\r\nTime:{n.EntityDateLocal}");
                sb.AppendLine($"{msgUrl}");

                n.Message = sb.ToString();
                n.NotificationResult = sendToEmail(n.DeliveryDestination, sb.ToString(), n.Name);
                n.NotificationSentUtc = DateTime.UtcNow;
                n.Status = "Success";
            }
            else if (n.DeliveryMethod?.ToLower() == "mobile")
            {
                if (!n.DeliveryDestination.StartsWith("+"))
                    n.DeliveryDestination = "+" + n.DeliveryDestination;

                var phUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
                var smsNum = phUtil.Parse(n.DeliveryDestination, null);
                if (smsNum.HasCountryCode && smsNum.HasNationalNumber)
                {
                    sb.AppendLine("HealthTeams Appointment reminder.");
                    sb.AppendLine($"{n.Name}\r\nTime:{n.EntityDateLocal}");
                    sb.AppendLine($"{msgUrl}");

                    var sms = $"+{smsNum.CountryCode}{smsNum.NationalNumber}";
                    n.DeliveryDestination = sms;
                    n.NotificationResult = sendToSms(sms, sb.ToString(), n.Name, $"{n.NotificationId}");
                    n.NotificationSentUtc = DateTime.UtcNow;
                    n.Status = "Success";
                }
                else
                {
                    n.NotificationResult = $"ERROR: {n.DeliveryDestination} is not in the corrected international format";
                    n.Status = "Error";
                }
            }
            else
            {
                n.Status = "Error";
                n.NotificationResult = $"ERROR: No matching delivery method Email/Mobile was found {n.DeliveryMethod}";
            }

            return n;
        }

        private HTNotification ProcessAlertNotification(HTNotification n)
        {
            _log.LogInformation("Starting ProcessNotifications Main Function");
            var sb = new StringBuilder();

            if (n.DeliveryMethod?.ToLower() == "email")
            {
                _log.LogInformation("Email Notification send started for:" + n.RecipientName);
                sb.AppendLine($"Hi, {n.RecipientName}.");
                sb.AppendLine("<br>");
                sb.AppendLine(n.Message);

                n.Message = n.Message;
                n.NotificationResult = sendToEmail(n.DeliveryDestination, sb.ToString(), n.Name);
                n.NotificationSentUtc = DateTime.UtcNow;
                n.Status = n.NotificationResult.Contains("ERROR:") ? "Error" : "Success";
                _log.LogInformation("Email Notification send ended for " + n.RecipientName + " Result:" + n.NotificationResult);
            }
            else if (n.DeliveryMethod?.ToLower() == "mobile")
            {
                if (!n.DeliveryDestination.StartsWith("+") && n.DeliveryDestination.StartsWith("0"))
                {
                    // Remove the leading 0
                    n.DeliveryDestination = n.DeliveryDestination.Substring(1);
                    n.DeliveryDestination = $"+61{n.DeliveryDestination}";
                }
                else if (!n.DeliveryDestination.StartsWith("+"))
                {
                    n.DeliveryDestination = "+" + n.DeliveryDestination;
                }

                var phUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
                var smsNum = phUtil.Parse(n.DeliveryDestination, null);
                if (smsNum.HasCountryCode && smsNum.HasNationalNumber)
                {
                    sb.AppendLine(n.Message);
                    var sms = $"+{smsNum.CountryCode}{smsNum.NationalNumber}";
                    n.DeliveryDestination = sms;
                    n.NotificationResult = sendToSms(sms, sb.ToString(), n.Name, $"{n.NotificationId}");
                    n.NotificationSentUtc = DateTime.UtcNow;
                    n.Status = n.NotificationResult.Contains("ERROR:") ? "Error" : "Success";
                }
                else
                {
                    n.NotificationResult = $"ERROR: {n.DeliveryDestination} is not in the corrected international format";
                    n.Status = "Error";
                }
            }
            else
            {
                n.Status = "Error";
                n.NotificationResult = $"ERROR: No matching delivery method Email/Mobile was found {n.DeliveryMethod}";
            }

            return n;
        }
        private string sendToSms(string toAddress, string content, string name, string uniqueId)
        {
            using (var clnt = new HttpClient())
            {
                var sms = new SmsContent
                {
                    ToAddress = toAddress,
                    Message = content,
                    FromAddress = "HealthTeams",
                    UniqueId = uniqueId
                };
                var sJson = HTJsonSerialiser.Serialize(sms);

                var json = new StringContent(sJson, Encoding.UTF8, "application/json");
                var res = clnt.PostAsync(SMSUrl, json).GetAwaiter().GetResult();

                if (res.IsSuccessStatusCode)
                    return (res.Content.ReadAsStringAsync().GetAwaiter().GetResult());
                else
                    return "ERROR: " + res.ReasonPhrase;
            }
        }

        private string sendToEmail(string toAddress, string content, string subject)
        {
            using (var clnt = new HttpClient())
            {
                var email = new EmailContent
                {
                    To = toAddress,
                    Content = content,
                    Subject = subject
                };
                var sJson = HTJsonSerialiser.Serialize(email);

                var json = new StringContent(sJson, Encoding.UTF8, "application/json");
                clnt.DefaultRequestHeaders.Add("x-api-key", apimKey);
                var res = clnt.PostAsync(EmailUrl, json).GetAwaiter().GetResult();

                if (res.IsSuccessStatusCode)
                {
                    return (res.Content.ReadAsStringAsync().GetAwaiter().GetResult());
                }
                else
                {
                    return "ERROR: " + res.ReasonPhrase;

                }
            }
        }
    }
}
