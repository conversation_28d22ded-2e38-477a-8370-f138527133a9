﻿using ht.be.fn.Utils;
using ht.common.backend.shared.classes;
using ht.common.backend.shared.helpers;
using ht.data.common.Telehealth;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.fn.Helpers;

public class AssessReadings
{
    public static string ReadIconBaseUrl;

    public readonly string _red = "#FF0000";
    public readonly string _amber = "#FFA800";
    public readonly string _green = "#22DC04";

    private ILogger _log = null;
    private MediaHelper _mh;
    private BlobHelper _blobHelper;
    public AssessReadings(ILogger log)
    {
        try
        {
            _mh = new MediaHelper { _log = log };
            log.LogInformation("AssessReadings- Successfully initialised MediaHelper");
        }
        catch(Exception ex)
        {
            log.LogError("ERROR: Trying to spin up MediaHelper." + ex.Message);
            _mh = null;
        }

        _log= log;

        var _uploadsSasToken = Environment.GetEnvironmentVariable("BlobUploadsSasToken");
        var _usersStorageUrl = Environment.GetEnvironmentVariable("BlobUsersUrl");
        var _usersSasToken = Environment.GetEnvironmentVariable("BlobUsersSasToken");
        var _storageAccount = Environment.GetEnvironmentVariable("IotStorageAccountName");
        var _storageKey = Environment.GetEnvironmentVariable("IotStorageAccountKey");

        _blobHelper = new BlobHelper(_storageAccount, _storageKey);

    }
    public bool HasAlerts(MonitoringAlertNotifications man)
    {
        return (man.IsAlert==true || man.IsWarning==true);
        
    }
    public MonitoringAlertNotifications AssessTelehealthMonitorData(TelehealthMonitorData data, 
        string userBlobUri,
        MonitoringDataProcessorContext ctx
        )
    {
        data.AlertNotifications= new MonitoringAlertNotifications
        {
            TaskName = data.Thresholds.TaskName,
            CarerName = data.Thresholds.CarerName,
            DoctorName = data.Thresholds.DoctorName,
            ResidentName = data.Thresholds.ResidentName,
            FacilityName = data.Thresholds.FacilityName,
            ExTaskId = data.ExTaskId,
            Results = new List<MonitoringAlertResultLine>()
        };

        _log.LogInformation($"HT.AssessReadings - Starting assessment for {data.AlertNotifications.ResidentName} TaskId-{data.AlertNotifications.ExTaskId}");


        foreach (var r in data.Readings)
        { 
            var rItemType = r.ItemType.ToString();
            if (!r.ReadDateUtc.HasValue)
                r.ReadDateUtc = DateTime.UtcNow;

            //processfiles
            if (r.Files != null && ctx.ProcessFiles==true)
            {
                var transId = updateFiles(data.ExTaskId, 
                    data.UserExId, 
                    data.ExTransactionId, 
                    r, 
                    userBlobUri, 
                    ctx.UploadUri);
                if (string.IsNullOrEmpty(data.ExTransactionId) || transId!=data.ExTransactionId)
                    data.ExTransactionId = transId?.ToUpper();
            }

         
            //processreadings

            //set read value and icon url for UTI as it is not having any read value from front end. This is only for UTI.
            if (rItemType == "UA")

            {
                r.ReadValue = "1";
                r.IconUrl= $"{ReadIconBaseUrl}{r.ItemType}.png";
            }

            if (float.TryParse(r.ReadValue, out var value))
            {
                var th = data.Thresholds.GetByMonitorType(r.ItemType,r.ReadStatus?.ToLower());
                if (string.IsNullOrEmpty(r.ReadUnits))
                    r.ReadUnits = th?.UOM;
                r.IconUrl = $"{ReadIconBaseUrl}{r.ItemType}.png";
                //add the values
                try
                {
                    r.LowCriticalValue = th.LowCriticalValue;
                    r.LowWarningValue = th.LowWarningValue;
                    r.HighCriticalValue = th.HighCriticalValue;
                    r.HighWarningValue = th.HighWarningValue;
                }
                catch { };

                //determine the colour
                
                switch (value)
                {
                    case float f when f < th?.LowCriticalValue:
                        {
                            data.AlertNotifications.Results.Add(
                                new MonitoringAlertResultLine
                                {
                                    ReadType = rItemType,
                                    AssessColour = _red,
                                    ActualValue = $"{value}",
                                    ExpectedValue = $"{th.Expected}",
                                    LowValue = $"{th.LowCriticalValue}",
                                    HighValue = $"{th.HighWarningValue}",
                                    Message = $"Vital:{rItemType}-Value is less than the minimum critical threshold of {th.LowCriticalValue}\r\nActual value:{value}\r\nExpected Value:{th.Expected}"
                                }
                            ) ;
                            data.AlertNotifications.IsWarning = true;
                            r.AssessColour = _red;
                            r.IsAbnormal = true;
                            r.IsAlert = true;
                            break;
                        }
                    case float f when f >= th?.LowCriticalValue && f <= th?.LowWarningValue:
                        {
                            data.AlertNotifications.Results.Add(
                                new MonitoringAlertResultLine
                                {
                                    ReadType = rItemType,
                                    AssessColour = _amber,
                                    ActualValue = $"{value}",
                                    ExpectedValue = $"{th.Expected}",
                                    LowValue = $"{th.LowCriticalValue}",
                                    HighValue = $"{th.LowWarningValue}",
                                    Message = $"Vital:{rItemType}-Value is less than the minimum warning threshold of {th.LowWarningValue}\r\nActual value:{value}\r\nExpected Value:{th.Expected}"
                                }
                            );
                            data.AlertNotifications.IsWarning = true;
                            r.IsAbnormal = true;
                            r.AssessColour = _amber;
                            break;
                        }
                    case float f when f >= th?.LowWarningValue && f <= th?.HighWarningValue:
                        {
                            data.AlertNotifications.Results.Add(
                                new MonitoringAlertResultLine
                                {
                                    ReadType = rItemType,
                                    AssessColour = _green,
                                    ActualValue = $"{value}",
                                    ExpectedValue = $"{th.Expected}",
                                    LowValue = $"{th.LowWarningValue}",
                                    HighValue = $"{th.HighWarningValue}",
                                    Message = $"Vital:{rItemType}-Value {value} is within the expected range {th.LowWarningValue} and {th.HighWarningValue}"
                                }
                            ) ;
                            r.AssessColour = _green;
                            r.IsAbnormal = false;
                            r.IsAlert = false;
                            break;
                        }
                    case float f when f >= th?.HighWarningValue && f <= th?.HighCriticalValue:
                        {
                            data.AlertNotifications.Results.Add(
                                new MonitoringAlertResultLine
                                {
                                    ReadType = rItemType,
                                    AssessColour = _amber,
                                    ActualValue = $"{value}",
                                    ExpectedValue = $"{th.Expected}",
                                    LowValue = $"{th.LowWarningValue}",
                                    HighValue = $"{th.HighWarningValue}",
                                    Message = $"Vital:{rItemType}-Value {value} is greater than the expected range {th.LowWarningValue} and {th.HighWarningValue}"
                                }
                            );
                            data.AlertNotifications.IsWarning = true;
                            r.IsAbnormal = true;
                            r.AssessColour = _amber;
                            break;
                        }
                    case float f when f > th?.HighCriticalValue:
                        {
                            data.AlertNotifications.Results.Add(
                                new MonitoringAlertResultLine
                                {
                                    ReadType = rItemType,
                                    AssessColour = _red,
                                    ActualValue = $"{value}",
                                    ExpectedValue = $"{th.Expected}",
                                    LowValue = $"{th.LowWarningValue}",
                                    HighValue = $"{th.HighWarningValue}",
                                    Message = $"Vital:{rItemType}-Value {value} is not within the expected range {th.LowWarningValue} and {th.HighWarningValue}"
                                }
                            );
                            data.AlertNotifications.IsWarning = true;
                            data.AlertNotifications.IsAlert = true;
                            r.IsAbnormal = true;
                            r.IsAlert = true;
                            r.AssessColour = _red;
                            break;
                        }

                    case float f when (f > th?.LowWarningValue) && (!th.HighWarningValue.HasValue):
                        {
                            data.AlertNotifications.Results.Add(
                                new MonitoringAlertResultLine
                                {
                                    ReadType = rItemType,
                                    AssessColour = _green,
                                    ActualValue = $"{value}",
                                    ExpectedValue = $"{th.Expected}",
                                    LowValue = $"{th.LowWarningValue}",
                                    HighValue = $"{th.HighWarningValue}",
                                    Message = $"Vital:{rItemType}-Value {value} is within the expected range {th.LowWarningValue}"
                                }
                            );
                            data.AlertNotifications.IsWarning = true;
                            data.AlertNotifications.IsAlert = true;
                            r.IsAbnormal = false;
                            r.IsAlert = false;
                            r.AssessColour = _green;
                            break;
                        }

                }
            }
        }
        return data.AlertNotifications;
    }


    
    private string updateFiles(string exTaskId,string userExId,string transId, TelehealthDataItem r, string userUrlBase, string uploadUrlBase="")
    {
        var resTransId = "";
        var fldType = r.ItemType.ToString(); //readtype.
        //adjusting files
        foreach (var f in r.Files)
        {
            _log.LogInformation($"updateFiles now processing {f.Comment} {f.FileName}");
            foreach (var url in f.FileUrls)
            {
                _log.LogInformation($"updateFiles working on URL={url.FileUrl}");
                var origUrl = url.FileUrl;
                var uploadUrl = $"{uploadUrlBase}/{origUrl}";

                if (string.IsNullOrEmpty(transId)) //we need to fetch the TransId from the uploaded file.
                {
                    _log.LogInformation($"updateFiles - TransactionId is null, attempting to read from the uploaded file");
                    var props = _blobHelper.GetBlobMetadata(uploadUrl).GetAwaiter().GetResult();
                    if (props.TryGetValue("transid", out string uTransId) && (!string.IsNullOrEmpty(uTransId)))
                        transId = uTransId;
                    _log.LogInformation($"Successfully retrieved TransId:{transId} ");
                }

                //We need to adjust File URLs to include the NULL TaskIDs and reseat the files elsewhere.
                var fname = Path.GetFileName(origUrl);

                //catering for translation from 3GP to MP3
                var ext = Path.GetExtension(fname)?.ToLower();
                if (ext == ".3gp")
                    fname = fname.Replace(".3gp", ".mp3");


                var fileOpts = new ht.data.common.Utils.MonitorDataFileUrlOptions
                {
                    ExTaskId = exTaskId,
                    FldType = fldType,
                    FName = fname,
                    TransId = transId,
                    UserExId = userExId,
                };
                _log.LogInformation($"updateFiles - Fname {fname} FileType {fldType} Users {userExId} ExTaskId {exTaskId} TransId {transId} ");
                url.FileUrl = $"{userUrlBase}/{fileOpts}";
                if (string.IsNullOrEmpty(resTransId))
                    resTransId = transId;

                if (_mh != null)
                {
                    if (_mh.IsMediaFile(url.FileUrl))
                        url.FileThumbUrl = _mh.GetThumbFullName(url.FileUrl);
                    try
                    {
                        if (_mh.IsMediaFile(url.FileUrl) && !string.IsNullOrEmpty(uploadUrlBase))
                        {

                            _log.LogInformation($"Fetching file info from {uploadUrl}");
                            url.FileInfo = _mh.DiscoverFileInfo(uploadUrl).GetAwaiter().GetResult();
                            if (url?.FileInfo != null)
                            {
                                var s = HTJsonSerialiser.Serialise(url.FileInfo);
                                _log.LogInformation($"Fetching file info from {uploadUrl} FileInfo:{s} - done");
                            }
                            else
                                _log.LogInformation($"Fetching file info from {uploadUrl} ** NONE FOUND **- done.");
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.LogError(ex, "ERROR processing files");
                    }
                }

            }
        }
        return (resTransId);
    }
}
