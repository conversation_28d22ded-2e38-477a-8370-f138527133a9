﻿using FFmpeg.NET;
using ht.be.fn.Utils;
using ht.common.backend.shared.helpers;
using ht.data.common.Telehealth;
using Microsoft.Extensions.Logging;
using NAudio.Wave;
using NAudio.WaveFormRenderer;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.fn.Helpers
{
    public class MediaHelper
    {
        public static string ffMpegZipPath;
        public string _ffMpegPath = string.Empty;
        public Engine _engine = null;

        public string _accountName;
        public string _accountKey;
        public string _usersUrl;
        public string _userSasToken;
        public string _uploadsSasToken;

        public BlobHelper _bHelper;
        public ILogger _log;

        private MaxPeakProvider _maxPeakProvider = null;
        private StandardWaveFormRendererSettings _renderSettings = null;

        public static string AppPath = null;
        public static string TmpPath = Path.GetTempPath();
        public static string ConversionArgs = "-filter:a \"volume=4.0\"";

        public MediaHelper()
        {
            
            _ffMpegPath = Utilities.os_env == "linux" ? "/tmp/ffmpeg.exe":$@"{AppPath}\ffmpeg\{Utilities.CPU}\ffmpeg.exe";
            _engine = new Engine(_ffMpegPath);

            _accountName = Environment.GetEnvironmentVariable("IotStorageAccountName");
            _accountKey = Environment.GetEnvironmentVariable("IotStorageAccountKey");
            _usersUrl = Environment.GetEnvironmentVariable("BlobUsersUrl");

            //*** MADHAV *** - these ones I can't find in Keyvault.
            _userSasToken = Environment.GetEnvironmentVariable("BlobUsersSasToken");
            _uploadsSasToken = Environment.GetEnvironmentVariable("BlobUploadsSasToken") ?? "sv=2020-10-02&si=uploads&sr=c&sig=20c0htvmMscWZ9ak8UDGiG1744Pe6o8y4pGTlO8AGfQ%3D";


            _bHelper = new BlobHelper(_accountName,_accountKey);

            _maxPeakProvider = new MaxPeakProvider();
            //var rmsPeakProvider = new RmsPeakProvider(400); // e.g. 200
            //var samplingPeakProvider = new SamplingPeakProvider(200); // e.g. 200
            //var averagePeakProvider = new AveragePeakProvider(2); // e.g. 4

            _renderSettings = new StandardWaveFormRendererSettings
            {
                Width = 640,
                TopHeight = 64,
                BottomHeight = 2,
                //DecibelScale =  true
                BackgroundColor = System.Drawing.Color.Transparent,
                TopPeakPen = new System.Drawing.Pen(System.Drawing.Color.Coral),
                
            };
        }

        public async Task<HealthTeamsFileInfo> DiscoverFileInfo(string fileUrl)
        {
            HealthTeamsFileInfo fi = new();
            try
            {
                _log.LogInformation($"DiscoverFileInfo - Getting info for {fileUrl}");
                var bytes = await _bHelper.GetBlob(fileUrl);
                using (var tmp = TempFile.CopyFromUrl(fileUrl, bytes))
                {
                    fi = await this.GetFileInfo(tmp);

                }
                _log.LogInformation($"DiscoverFileInfo - {fi?.ToString()}");
            }
            catch(Exception ex)
            {
                _log?.LogError(ex, "ERROR: DiscoverFileInfo had and error");
            }
            return (fi);
        }

        public async Task<HealthTeamsFile> GetFileDetails(string mainUrl, bool bRefresh = false)
        {
            HealthTeamsFile htFile = new() { FileUrl = mainUrl };
            if (!bRefresh)
            {
                var props = await _bHelper.GetBlobMetadata(mainUrl);
                if (props.ContainsKey("thumburl"))
                    htFile.FileThumbUrl = props["thumburl"];
                if (props.ContainsKey("fileinfo"))
                    htFile.FileInfo = HTJsonSerialiser.Deserialise<HealthTeamsFileInfo>(props["fileinfo"]);

            }
            else 
            {
                var bytes = await _bHelper.GetBlob(mainUrl);
                using (var tmp = TempFile.CopyFromUrl(mainUrl,bytes))
                {
                    htFile.FileInfo = await this.GetFileInfo(tmp);
                    htFile.FileThumbUrl = GetThumbFullName(mainUrl);
                    await SaveHTMetadata(htFile);
                }

            }
            return (htFile);
        }

        public async Task<HealthTeamsFile> CreateAndStoreThumbnail(string vidUrl, bool bAddToMetadata = true)
        {
            HealthTeamsFile htFile = new() {  FileUrl = vidUrl};

            var result = string.Empty;
            Dictionary<string, string> metadata = null;

            if (!vidUrl.Contains('?')) //generate SAS token
            {
                metadata = (Dictionary<string,string>)await _bHelper.GetBlobMetadata(vidUrl);
            }

            //*** translate file if needed **//
            if (GetUrlExtension(vidUrl)?.ToLower()=="3gp") //we need to convert this to mp3.
            {
                var newFileResult = await GetTranslatedFile(vidUrl, "mp3"); //translate to MP3. NewFileResult is a TempFile - so we need to save to the cloud.
                if (newFileResult?.File !=null) //we have a translated file.
                {
                    htFile.FileUrl = await SaveTempFileToBlob(vidUrl, newFileResult, metadata);
                    htFile.FileInfo = newFileResult.FileInfo;
                    newFileResult?.File?.Dispose(); //clear out the temp file.
                }
            }

            //generate a thumb if required.
            var fileResult = await GetThumb(htFile.FileUrl);
            if (htFile.FileInfo==null)
                htFile.FileInfo = fileResult?.FileInfo;

            if (fileResult?.File !=null) //we have the thumbnail now to copy it.
            {
                var thumbUrl = await SaveThumbnail(vidUrl, fileResult, new Dictionary<string, string>());
                htFile.FileThumbUrl = thumbUrl;
                fileResult.File?.Dispose();
            }

            if (bAddToMetadata) //we need to add/update the new thumb + the media info to this file.
            {
                SaveHTMetadata(htFile).ConfigureAwait(false).GetAwaiter().GetResult();
            }

            return (htFile);
        }

        public bool IsMediaFile(string url)
        {
            var ext = GetUrlExtension(url);
            return (ext == "mp4" || ext == "mp3" || ext == "wav" || ext == "3gp");
        }

        //This routine saves Metadata to the File in Blob
        private async Task SaveHTMetadata(HealthTeamsFile htFile)
        {
            var json = HTJsonSerialiser.Serialise(htFile?.FileInfo);
            _log?.LogInformation($"MediaHelper Saving File Metadata {htFile?.FileUrl} FileInfo={json}");
            var props = new Dictionary<string, string> {
                   { "fileinfo", json  },
                   { "thumburl", htFile?.FileThumbUrl }
               };
            await _bHelper.SetBlobMetadata(htFile.FileUrl, props);
            _log?.LogInformation($"MediaHelper Saved File Metadata");
        }
       

        private async Task<string> SaveTempFileToBlob(string originalUrl, FileResult fr,Dictionary<string,string> metadata)
        {
            var newExt = fr.File.UrlExt ?? Path.GetExtension(fr.File.FileName);
            var baseFname = GetNewFileFullName(originalUrl, newExt);
            var bytes = fr.File.GetBytes();

            var tok = (baseFname.Contains("/uploads") ? _uploadsSasToken : _userSasToken);

            await _bHelper.SaveBlob(baseFname, tok, bytes, metadata);
            return baseFname;
        }

        private async Task<string> SaveThumbnail(string originalUrl, FileResult fr, Dictionary<string, string> metadata)
        {
            try
            {
                var baseFname = GetNewFileFullName(originalUrl, "thumb.png");
                var bytes = fr.File.GetBytes();
                var tok = (baseFname.Contains("/uploads") ? _uploadsSasToken : _userSasToken);
                await _bHelper.SaveBlob(baseFname, tok, bytes, metadata);
                return baseFname;
            }
            catch(Exception ex)
            {
                _log?.LogError(ex, $"ERROR: SaveThumb failed - {originalUrl} ");
                throw;
            }
        }

        public string GetNewFileFullName(string origurl,string newFname)
        {
            var bFname = Path.GetFileName(origurl);
            var baseVidUrl = origurl.Replace(bFname, ""); //strip the filename out.
            //store the thumb next to the vid blob.
            var baseFname = baseVidUrl + Path.GetFileNameWithoutExtension(bFname) + newFname;
            return (baseFname);
        }

        public string GetThumbFullName(string mainurl)
        {
            return GetNewFileFullName(mainurl, "thumb.png");
        }

        public string GetUrlExtension(string mainurl)
        {
            var bFname = Path.GetFileName(mainurl);
            return Path.GetExtension(bFname)?.Replace(".", "");
        }

        private async Task<HealthTeamsFileInfo> GetFileInfo(TempFile f)
        {
            HealthTeamsFileInfo res = null;
            var inputFile = new FFmpeg.NET.InputFile(new FileInfo(f.FileName));
            var metaData = await _engine.GetMetaDataAsync(inputFile, default);
            if (metaData != null)
            {
                res = new()
                {
                    Duration = metaData.Duration,
                    Audio = new()
                    {
                        ChannelOutput = metaData.AudioData?.ChannelOutput,
                        BitRateKbs = metaData.AudioData?.BitRateKbs,
                        Format = metaData.AudioData?.Format,
                        SampleRate = metaData.AudioData?.SampleRate

                    },
                    Video = new()
                    {
                        BitRateKbs = metaData.VideoData?.BitRateKbs,
                        Format = metaData.VideoData?.Format,
                        Fps = metaData.VideoData?.Fps ?? 0.0,
                        FrameSize = metaData.VideoData?.FrameSize,
                        ColourModel = metaData.VideoData?.ColorModel

                    }
                };
            }
            return (res);
        }

        private async Task<FileResult> GetTranslatedFile(string fileUrlIn,string newExt)
        {
            var originalUrl = fileUrlIn;
            byte[] bytes = null;
            if (!fileUrlIn.Contains('?'))
            {
                bytes = await _bHelper.GetBlob(fileUrlIn);
            }

            var fr = new FileResult();
            using (var fName = TempFile.CopyFromUrl(fileUrlIn, bytes))
            {
                fr.File =await TranslateFile(fName, newExt);
                fr.FileInfo = await GetFileInfo(fr.File);
            }
            return (fr);
        }

        private async Task<FileResult> GetThumb(string fileUrlIn)
        {
            var originalUrl = fileUrlIn;
            byte[] bytes = null;
            if (!fileUrlIn.Contains('?'))
            {
                bytes = await _bHelper.GetBlob(fileUrlIn);
            }

            var fr = new FileResult();
            using (var fName = TempFile.CopyFromUrl(fileUrlIn,bytes))
            {

                fr.FileInfo = await GetFileInfo(fName);

                if (fName.UrlExt?.ToLower() == "mp3" || fName.UrlExt?.ToLower() == "wav")
                {
                    fr.File = await GetAudioGraph(fName);
                }
                else if (fName.UrlExt?.ToLower() == "mp4")
                {
                    fr.File = await GetThumbFromVideo(fName);
                }

            }


            return (fr);
        }

        private async Task<TempFile> GetThumbFromVideo(TempFile fileIn)
        {
            var tmpFileOut = new TempFile("png");
            var outputFile = new FFmpeg.NET.OutputFile(tmpFileOut.FileName);

            // Saves the frame located on the 15th second of the video.
            var options = new FFmpeg.NET.ConversionOptions { Seek = TimeSpan.FromSeconds(2), VideoFps = 1 };
            var inputFile = new InputFile(fileIn.FileName);
            await _engine.GetThumbnailAsync(inputFile, outputFile, options, default).ConfigureAwait(false);
            return tmpFileOut;
        }

        public async Task<TempFile> GetAudioGraph(TempFile fileIn)
        {
            WaveStream stm = null;
            TempFile tmp = null;
            if (fileIn.UrlExt.ToLower() == "wav")
                stm = new AudioFileReader(fileIn.FileName);
            else if (fileIn.UrlExt.ToLower()=="mp3")
            {
                tmp = new TempFile(fileIn.UrlExt); //second mp3.
                var output = await _engine.ConvertAsync(
                    new InputFile(fileIn.FileName),
                    new OutputFile(tmp.FileName), 
                    default
                    );
                stm = new Mp3FileReader(tmp.FileName);
            }            

            var renderer = new WaveFormRenderer();
            var image = renderer.Render(stm, _maxPeakProvider, _renderSettings);
            var fileOut = new TempFile("png");
            image?.Save(fileOut.FileName);
            tmp?.Dispose();
            
            return fileOut;
        }

        public async Task<TempFile> TranslateFile(TempFile fileIn,string trgExt)
        {
            var opts = new ConversionOptions { ExtraArguments = MediaHelper.ConversionArgs };
            
            TempFile tmp = null;
            var srcExt = fileIn.UrlExt.ToLower();
            trgExt = trgExt?.ToLower();
            tmp = new TempFile(trgExt); // mp3.
            var output = await _engine.ConvertAsync(new InputFile(fileIn.FileName),
                new OutputFile(tmp.FileName),
                opts,
                default);
            return tmp;
        }



    }
}
