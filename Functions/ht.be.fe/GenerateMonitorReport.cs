using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.data.common.Telehealth;
using ht.be.fn.Utils;
using ht.be.fn.Helpers;
using System.Linq;

namespace ht.be.fn
{
    public static class GenerateMonitorReport
    {
        [FunctionName("GenerateMonitorReport")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            try
            {
                TelehealthMonitorData md = null;
                var svcUser = Utils.Utilities.svc_acct_UserExId;
                var usrSecurity = Utilities.GetJsonSecurity();

                string exUserId =  (req.Query.ContainsKey("userExId") ?  req.Query["userExId"] : "209986dc-1616-4d52-b8fb-cefd4d39415f");
                string exTaskId = (req.Query.ContainsKey("exTaskId") ? req.Query["userExId"]: "7a91ec1f-2c40-4dd4-b197-661f8e94b4c1"); //req.Query["exTaskId"]; // "82975910-af6a-4a77-9c28-73caf052a20a";
                string exTransactionId = (req.Query.ContainsKey("exTransactionId") ? req.Query["exTransactionId"] : "");

                var url = await GenerateMonitorDataReport(log, exUserId, exTaskId, exTransactionId);

                return new JsonResult(new { url=url});
            }
            catch(Exception ex)
            {
                return new NotFoundObjectResult(ex);
            }

        }

        public static async Task<TelehealthMonitorData> FetchReportData(ILogger log, string exUserId, string exTaskId, string exTransactionId)
        {
            log.LogInformation($"Executing GenerateMonitorReport with for User: {exUserId} Task: {exTaskId} Transaction: {exTransactionId}");
            var json = "{ \"userExId\":\"" + exUserId + "\",\"exTaskId\":\"" + exTaskId + "\",\"exTransactionId\":\"" + exTransactionId + "\"}";
            var usrSecurity = Utilities.GetJsonSecurity();

            var res = await DBHelper.ExecSprocWithJsonAsync("sp_MonitorData_Get_ForReport", json, usrSecurity);
            return HTJsonSerialiser.Deserialise<TelehealthMonitorData>(res);
        }

        public static async Task<string> GenerateMonitorDataReport(ILogger log, string userExId, string exTaskId, string exTransactionId)
        {

            log.LogInformation($"GenerateMonitorDataReport running for {userExId}");
            var md = await FetchReportData(log, userExId, exTaskId, exTransactionId);


            //Report Generation
            var pdf = PDFManager.GetInstance(log);
            var url = await pdf.GenerateMonitoringReport(md);

            var usrSecurity = Utilities.GetJsonSecurity();

            var json = "{\"url\":\"" + url + "\",\"userExId\":\"" + userExId + "\",\"exTaskId\":\"" + exTaskId + "\",\"exTransactionId\":\"" + exTransactionId + "\"}";
            var res = await DBHelper.ExecSprocWithJsonAsync("sp_MonitoringReport_Upsert", json, usrSecurity);

            return url;
        }
    }
}
