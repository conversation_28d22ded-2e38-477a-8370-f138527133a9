// Default URL for triggering event grid function in the local environment.
// http://localhost:7071/runtime/webhooks/EventGrid?functionName={functionname}
using System;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Host;
using Microsoft.Azure.WebJobs.Extensions.EventGrid;
using Microsoft.Extensions.Logging;
using Azure.Messaging.EventGrid;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Blobs;
using Microsoft.WindowsAzure.Storage.Blob;
using Azure;
using Azure.Storage;
using Microsoft.WindowsAzure.Storage.Auth;
using System.Threading.Tasks;
using System.Net.Http;
using System.Collections.Generic;
using System.Linq;
using ht.be.fn.Helpers;
using ht.be.fn.Utils;
using ht.common.backend.shared.helpers;

namespace ht.be.fn
{
    public static class IoTHubFileProcessor
    {
        private static string _uploadsSasToken;
        private static string _usersStorageUrl;
        private static string _usersSasToken;
        private static string _storageAccount;
        private static string _storageKey;
        static IoTHubFileProcessor()
        {
            _uploadsSasToken = Environment.GetEnvironmentVariable("BlobUploadsSasToken");
            _usersStorageUrl = Environment.GetEnvironmentVariable("BlobUsersUrl");
            _usersSasToken = Environment.GetEnvironmentVariable("BlobUsersSasToken");
            _storageAccount = Environment.GetEnvironmentVariable("IotStorageAccountName");
            _storageKey = Environment.GetEnvironmentVariable("IotStorageAccountKey");
        }

        [FunctionName("IoTHubFileProcessor")]
        public static void Run([EventGridTrigger] EventGridEvent fUploadEvent, ILogger log)
        {
            var bProfilePic = false;
            try
            {
                var bHelper = new BlobHelper(_storageAccount, _storageKey);

                var userUrl = string.Empty;
                var dstBlobUrl = string.Empty;
                log.LogInformation("Subject:" + fUploadEvent.Subject);

                var jObj = JObject.Parse(fUploadEvent.Data.ToString());
                var uploadUrl = jObj.GetValue("url").Value<string>(); //this is a device upload string.
                var uploadUri = new Uri(uploadUrl);
                var srcContentType = Utils.Utilities.GetMimeType(System.IO.Path.GetFileName(uploadUri.AbsolutePath));

                log.LogInformation($"IoTHubFileProcessor - Triggered by Upload Url: {uploadUrl}");

                var srcBlobUrl = $"{uploadUrl}?{_uploadsSasToken}";
                log.LogInformation($"We got srcCT={srcContentType} srcUrl={srcBlobUrl}");

                var props = bHelper.GetBlobMetadata(uploadUrl).GetAwaiter().GetResult();
                var bytes = bHelper.GetBlob(srcBlobUrl).GetAwaiter().GetResult();

                var urlFileName = System.IO.Path.GetFileName(uploadUrl);
                log.LogInformation($"IoTHubFileProcessor - FileName:{urlFileName} Upload Url: {uploadUrl}");


                if (!uploadUrl.Contains("/profilePic/") && !uploadUrl.Contains("/logo/")) //processing Vitals
                {
                    log.LogInformation($"IoTHubFileProcessor - fetched metadata props {props?.Count}");

                    _ = props.TryGetValue("extaskid", out string exTaskId);
                    _ = props.TryGetValue("userexid", out string userExId);
                    _ = props.TryGetValue("transid", out string transId);
                    _ = props.TryGetValue("monitortype", out string fldType);

                    if (string.IsNullOrEmpty(exTaskId))
                        exTaskId = Guid.Empty.ToString();


                    log.LogInformation($"IoTHubFileProcessor - prop values are task:{exTaskId} userid:{userExId} transid:{transId} monitortype:{fldType}");

                    var fileOpts = new ht.data.common.Utils.MonitorDataFileUrlOptions
                    {
                        ExTaskId = Convert.ToString(exTaskId),
                        FldType = Convert.ToString(fldType),
                        FName = Convert.ToString(urlFileName),
                        TransId = transId?.ToUpper(),
                        UserExId = userExId?.ToUpper(),
                    };

                    var partialUrl = Convert.ToString(fileOpts);

                    //create a user container.
                    log.LogInformation($"Url={_usersStorageUrl} an Container={partialUrl}");
                    //removing the device name out of here.
                    userUrl = $"{_usersStorageUrl}/{partialUrl}"; // (!_usersStorageUrl.EndsWith("/") ? "/":"") +partialUrl;
                    props.Add("deviceName", uploadUrl.Split("/")?[4]);

                }
                else //handle a profilePic
                {
                    bProfilePic = true;
                    userUrl = uploadUrl.Replace("/uploads/", "/users/");
                }

                log.LogInformation($"IoTHubFileProcessor - Upload Url: {uploadUrl}\r\n User Url:{userUrl}");


                //userUrl = userUrl.Replace("//", "/");
                dstBlobUrl = userUrl + $"?{_usersSasToken}";
                bHelper.SaveBlob(userUrl, _usersSasToken, bytes, (Dictionary<string, string>)props).GetAwaiter().GetResult();

                try
                {
                    //Check for file info
                    var mh = new MediaHelper { _log = log };
                    if (mh.IsMediaFile(userUrl))
                    {
                        log.LogInformation($"IoTHubFileProcessor - Got Media File Url {userUrl}");
                        mh.CreateAndStoreThumbnail(userUrl).GetAwaiter().GetResult();
                    }
                }
                catch(Exception ex)
                {
                    log.LogError("ERROR: IotHubFileProcessor tried to process files and failed on the MediaHelper section. " + ex.Message);
                }


                //perform async processing
                if (bProfilePic)
                {
                    var UserExId = string.Empty;
                    var ftype = string.Empty;
                    if (props.ContainsKey("UserExId"))
                    {
                        UserExId = props["UserExId"];

                    }
                    if (props.ContainsKey("ftype"))
                    {
                        ftype = props["ftype"];
                    }

                    Utilities.UpdateProfile(UserExId, userUrl, ftype);
                }

            }
            catch (Exception ex)
            {
                log.LogError(Convert.ToString(ex.InnerException) + '-' + Convert.ToString(ex.Message), "IoTHubFileProcessor");
            }
        }

    }
}
