using IoTHubTrigger = Microsoft.Azure.WebJobs.EventHubTriggerAttribute;

using Microsoft.Azure.WebJobs;
using System.Text;
using System.Net.Http;
using Microsoft.Extensions.Logging;
using Azure.Messaging.EventHubs;
using ht.be.fn.Helpers;
using ht.be.fn.Utils;
using ht.data.common.Telehealth;
using Microsoft.Azure.WebJobs.Extensions.EventGrid;
using Azure.Messaging.EventGrid;
using System.Text.Json.Nodes;
using ht.common.backend.shared.classes;
using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.IO;
using System.IO.Compression;
using Org.BouncyCastle.Asn1.Cms;

namespace ht.be.fn
{
    public class IotHubEventProcessor
    {
        public static string UserBlobUri { get; set; }
        public static string UploadBlobUri { get; set; }

        private static HttpClient client = new HttpClient();
        private readonly ILogger<IotHubEventProcessor> _logger;
        private static Dictionary<string, List<byte[]>> chunkStorage = new Dictionary<string, List<byte[]>>();

        public class ChunkData
        {
            public string MessageId { get; set; } // Unique identifier for the entire message
            public string ChunkId { get; set; }   // Unique identifier for the chunk
            public byte[] Chunk { get; set; }     // The actual chunk data
            public bool IsLastChunk { get; set; } // Indicates if this is the last chunk of the message
        }
        public IotHubEventProcessor(ILogger<IotHubEventProcessor> log)
        {
            _logger = log;
        }

        //This routine is called when the results are updated from the client.
        /*
        [FunctionName("IotHubEventProcessor")]
        [return: ServiceBus("sysmsgs", Connection = "HTSBSysMsgsConnectionString")]
        public string Run([IoTHubTrigger("",Connection = "htiothub")]EventData message,ILogger log)
        {

            var msg = Encoding.UTF8.GetString(message.Body.ToArray());
            var md = HTJsonSerialiser.Deserialise<TelehealthMonitorData>(msg);
            return (MonitoringdataProcessor(log, md));
            

            
        }
        */

        [FunctionName("IoTHubMessageProcessor")]
        //[return: ServiceBus("sysmsgs", Connection = "HTSBSysMsgsConnectionString")]
        public void RunMessageProcessor([EventGridTrigger] EventGridEvent gridEvent, ILogger log)
        {
            log.LogInformation($"RunMessageProcessor - received message {gridEvent?.EventType}");
            if (gridEvent.EventType.Contains("Telemetry"))
            {
                var res = gridEvent.Data.ToString(); //remove the first {
                log.LogInformation($"RunMessageProcessor - received message gridEvent.Data string 1: {gridEvent.Data.ToString()}");

                //This is for data decompression
                //res = res.Substring(0,res.Length - 1); //remove the last }
                var nd = JsonNode.Parse(res);
                var device = nd["systemProperties"]["iothub-connection-device-id"]?.ToString();
                var uploadUrl = $@"{UploadBlobUri}/{device}";

                //This is for data decompression
                byte[] decompressedData = Decompress(Convert.FromBase64String(Convert.ToString(nd["body"])));
                var body = (Encoding.UTF8.GetString(decompressedData));
                log.LogInformation($"RunMessageProcessor - this is decompressed data:  {body}");
                log.LogInformation($"RunMessageProcessor - upload url - {uploadUrl} body - {body}");

                var md = HTJsonSerialiser.Deserialise<TelehealthMonitorData>(body);

                var ctx = new MonitoringDataProcessorContext
                {
                    GenerateReport = true,
                    Persist = true,
                    ProcessFiles = true,
                    UploadUri = uploadUrl
                };

                var result = ProcessMonitoringAssessment.MonitoringdataProcessor(log, md, ctx);
                log.LogInformation($"RunMessageProcessor - Result {result}");
                //return (HTJsonSerialiser.Serialise(result));
            }

            else
                return;// null; 
        }

        public static byte[] Decompress(byte[] compressedData)
        {
            using (MemoryStream input = new MemoryStream(compressedData))
            using (MemoryStream output = new MemoryStream())
            using (DeflateStream dstream = new DeflateStream(input, CompressionMode.Decompress))
            {
                dstream.CopyTo(output);
                return output.ToArray();
            }
        }

        public string MonitoringdataProcessor(ILogger _logger, TelehealthMonitorData htMonitorData, string uploadUrl = "")
        {
            try
            {
                htMonitorData.ExTransactionId ??= System.Guid.NewGuid().ToString().ToUpper();
                MonitoringThresholds th = null;
                var svcUser = Utils.Utilities.svc_acct_UserExId;
                var usrSecurity = Utilities.GetJsonSecurity();

                if (string.IsNullOrEmpty(htMonitorData.ExTaskId))
                    htMonitorData.ExTaskId = System.Guid.Empty.ToString();


                var msg = HTJsonSerialiser.Serialise(htMonitorData);
                var res = DBHelper.ExecSprocByParams("sp_MonitorData_Get_Threshholds_ByTaskIdOrResidentId",
                        new System.Collections.Generic.Dictionary<string, string>
                        {
                            {"id",htMonitorData.ExTaskId},{"exResidentId",htMonitorData.UserExId},{"jsonSecurity",usrSecurity }
                        });

                if (string.IsNullOrEmpty(res))
                {
                    _logger.LogInformation($"MonitoringDataProcessor -  htMonitorData before serilization -{htMonitorData}");
                    _logger.LogInformation($"MonitoringDataProcessor -  htMonitorData after serialization -{msg}");

                    _logger.LogWarning($"MonitoringDataProcessor - no Thresholds found for Resident - {htMonitorData?.UserExId} and TaskId - {htMonitorData?.ExTaskId}");
                    DBHelper.ExecSproc("sp_MonitorData_Upsert", msg);
                    throw new HttpRequestException($"Unable to fetch Threshold values for data - {msg}");

                }
                _logger.LogInformation($"MonitoringDataProcessor - Threshold about to deserialise {res}");
                th = HTJsonSerialiser.Deserialise<MonitoringThresholds>(res);
                htMonitorData.Thresholds = th;
                _logger.LogInformation($"MonitoringDataProcessor - Thresholds obtained Resident-{htMonitorData.UserExId} Task - {htMonitorData.ExTaskId}");

                var rdr = new AssessReadings(_logger);
                var ctx = new MonitoringDataProcessorContext
                {
                    UploadUri = uploadUrl,
                    ProcessFiles = true,
                };
                _logger.LogInformation($"MonitoringDataProcessor - UserBlobUri --{UserBlobUri}");
                var man = rdr.AssessTelehealthMonitorData(htMonitorData, UserBlobUri, ctx);
                _logger.LogInformation($"MonitoringDataProcessor - Assessments done -{htMonitorData.UserExId} Task - {htMonitorData.ExTaskId}");
                msg = HTJsonSerialiser.Serialise(htMonitorData);
                _logger.LogInformation($"MonitoringDataProcessor -  htMonitorData before serilization -{htMonitorData}");
                _logger.LogInformation($"MonitoringDataProcessor -  htMonitorData after serialization -{msg}");

                var results = DBHelper.ExecSprocWithJsonAsync("sp_MonitorData_Upsert", msg, usrSecurity).GetAwaiter().GetResult();
                _logger.LogInformation($"MonitoringDataProcessor - Results Saved -{htMonitorData.UserExId} Task - {htMonitorData.ExTaskId}");

                if (rdr.HasAlerts(man))
                {
                    _logger.LogInformation($"Alerts Found -{man.ExTaskId}");
                    DBHelper.ExecSprocWithJsonAsync("sp_ResidentAlerts_Upsert", msg, usrSecurity).GetAwaiter().GetResult();
                }

                //Generate the report
                var url = GenerateMonitorReport.GenerateMonitorDataReport(_logger, htMonitorData.UserExId, htMonitorData.ExTaskId, htMonitorData.ExTransactionId).GetAwaiter().GetResult();

                //Raise an EventGrid Event
                return "{\"url\":\"" + url + "\"}";


            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "ERROR: " + Convert.ToString(ex.InnerException) + "--message" + Convert.ToString(ex.Message));
                throw;
            }



        }

    }
}