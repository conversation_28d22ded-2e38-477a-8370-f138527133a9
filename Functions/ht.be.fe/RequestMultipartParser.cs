using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.be.fn.Helpers;
using ht.common.backend.shared.helpers;
using ht.be.fn.Utils;
using ht.data.common.Telehealth;
using System.Net.Http;
using ht.common.backend.shared.classes;
using HttpMultipartParser;
using System.Text.Json;
using System.Linq;
using System.Text.Json.Nodes;
using Newtonsoft.Json.Linq;
using System.Text;

namespace ht.be.fn
{
    public static class RequestMultipartParser
    {
        [FunctionName("RequestMultipartParser")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = null)] HttpRequest req,
            ILogger log, ExecutionContext ctx)
        {

            log?.LogInformation($"RequestMultipartParser - Called");
            var bdy = await req.ReadAsStringAsync().ConfigureAwait(false);
            if (!string.IsNullOrEmpty(bdy))
            {
                log?.LogInformation($"{bdy}");


                //return (new OkObjectResult(str));
                if (req.HasFormContentType)
                {
                    //var str = Convert.FromBase64String(bdy);
                    //var frm = MultipartFormDataParser.Parse(new MemoryStream(str));
                    log?.LogInformation($"RequestMultipartParser - {req.HasFormContentType} Content Type : " + req.Headers["Content-Type"].First());
                    // log?.LogInformation("RequestMultipartParser - " + str);
                    var fld = req.GetQueryParameterDictionary()?["fld"];
                    // Parse the multipart form data and return it as a json object.
                    var frm = await req.ReadFormAsync().ConfigureAwait(false);
                    log?.LogInformation($"Form Count = {frm?.Count}");
                    log?.LogInformation($"Form Payload= {frm["payload"].First()}");
                    if (frm != null)
                    {
                        if (fld == null)
                            return new JsonResult(frm);
                        else
                        {
                            var v = frm[fld];
                            if (v.Count > 0)
                            {
                                var tmp = v.First();
                                var obj = getJson(tmp);
                                if (obj != null)
                                {
                                    return new JsonResult(obj);
                                }
                                else
                                    return new OkObjectResult(tmp);
                            }
                            return new OkObjectResult(null);
                        }
                    }

                }
                if (req.HasJsonContentType())
                {
                    var obj = await req.ReadFromJsonAsync<JsonElement>().ConfigureAwait(false);
                    log?.LogInformation($"resp {obj}");

                    return new JsonResult(obj);

                }
            }

            return new OkObjectResult(null);

        }

        private static JObject getJson(string str)
        {
            return JObject.Parse(str);
        }
    }
}
