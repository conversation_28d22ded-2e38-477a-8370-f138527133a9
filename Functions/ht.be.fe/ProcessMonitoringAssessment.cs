using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.data.common.Telehealth;
using ht.be.fn.Utils;
using ht.be.fn.Helpers;
using System.Net.Http;
using ht.common.backend.shared.models;
using ht.common.backend.shared.classes;
using System.Runtime.CompilerServices;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Net;
using System.Text;

namespace ht.be.fn
{
    public static class ProcessMonitoringAssessment
    {
        public static string UserBlobUri { get; set; }
        public static string UploadBlobUri { get; set; }

        [FunctionName("ProcessMonitoringAssessment")]
        public static async Task<HttpResponseMessage> Run(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                string data = await new StreamReader(req.Body).ReadToEndAsync();
                var md = HTJsonSerialiser.Deserialise<TelehealthMonitorData>(data);
                var res = MonitoringdataProcessor(log, md, new MonitoringDataProcessorContext
                {
                    UploadUri = UploadBlobUri
                });

                var result = HTJsonSerialiser.Serialise(res);

                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent(result, Encoding.UTF8, "application/json")
                };

            }
            catch (Exception ex)
            {
                log.LogError(ex, "ProcessMonitoringAssessment");
                throw;
            }
        }

        public static TelehealthMonitorData MonitoringdataProcessor(ILogger _logger, TelehealthMonitorData htMonitorData, MonitoringDataProcessorContext? ctx)
        {
            try
            {
                string vitalsComment = htMonitorData.VitalComments ?? "null";
                _logger.LogInformation($"MonitoringDataProcessor begin. Comments = {vitalsComment}");
                SystemLog("Information", "ProcessMonitoringAssessment", vitalsComment);

                ctx ??= new MonitoringDataProcessorContext
                {
                    UploadUri = ProcessMonitoringAssessment.UploadBlobUri
                };
                if (string.IsNullOrEmpty(htMonitorData.ExTransactionId))
                    htMonitorData.ExTransactionId = System.Guid.NewGuid().ToString().ToUpper();
                if (string.IsNullOrEmpty(htMonitorData.ExTaskId))
                    htMonitorData.ExTaskId = System.Guid.Empty.ToString();

                MonitoringThresholds th = null;
                string msg = "";
                var svcUser = Utils.Utilities.svc_acct_UserExId;
                var usrSecurity = Utilities.GetJsonSecurity();




                var res = DBHelper.ExecSprocByParams("sp_MonitorData_Get_Threshholds_ByIds",
                        new System.Collections.Generic.Dictionary<string, string>
                        {
                            {"exFacilityId",htMonitorData.ExFacilityId},
                            {"exTaskId",htMonitorData.ExTaskId},
                            {"exResidentId",htMonitorData.UserExId},
                            {"jsonSecurity",usrSecurity }
                        });

                if (string.IsNullOrEmpty(res) && ctx.Persist == true)
                {

                    _logger.LogInformation($"MonitoringDataProcessor -  htMonitorData before serilization -{htMonitorData}");

                    msg = HTJsonSerialiser.Serialise(htMonitorData);
                    _logger.LogInformation($"MonitoringDataProcessor -  htMonitorData after serialization -{msg}");

                    _logger.LogWarning($"MonitoringDataProcessor - no Thresholds found for Resident - {htMonitorData?.UserExId} and TaskId - {htMonitorData?.ExTaskId}");
                    DBHelper.ExecSproc("sp_MonitorData_Upsert", msg);
                    throw new HttpRequestException($"Unable to fetch Threshold values for data - {msg}");
                }

                _logger.LogInformation($"MonitoringDataProcessor - Threshold about to deserialise {res}");
                th = HTJsonSerialiser.Deserialise<MonitoringThresholds>(res);
                htMonitorData.Thresholds = th;
                _logger.LogInformation($"MonitoringDataProcessor - Thresholds obtained Facility-{htMonitorData.ExFacilityId} Resident-{htMonitorData.UserExId} Task - {htMonitorData.ExTaskId}");

                var rdr = new AssessReadings(_logger);
                var man = rdr.AssessTelehealthMonitorData(htMonitorData, UserBlobUri, ctx);
                _logger.LogInformation($"MonitoringDataProcessor - Assessments done -{htMonitorData.UserExId} Task - {htMonitorData.ExTaskId}");

                if (ctx.Persist == true)
                {
                    _logger.LogInformation($"MonitoringDataProcessor -  htMonitorData before serilization -{htMonitorData}");
                    msg = HTJsonSerialiser.Serialise(htMonitorData);
                    _logger.LogInformation($"MonitoringDataProcessor -  htMonitorData after serialization -{msg}");

                    var results = DBHelper.ExecSprocWithJsonAsync("sp_MonitorData_Upsert", msg, usrSecurity).GetAwaiter().GetResult();
                    _logger.LogInformation($"MonitoringDataProcessor - Results Saved -{htMonitorData.UserExId} Task - {htMonitorData.ExTaskId}");
                }

                if (rdr.HasAlerts(man) && ctx.Persist == true)
                {
                    _logger.LogInformation($"Alerts Found -{man.ExTaskId}");
                    DBHelper.ExecSprocWithJsonAsync("sp_ResidentAlerts_Upsert", msg, usrSecurity).GetAwaiter().GetResult();
                    var helper = new NotificationHelper(_logger);
                    helper.ProcessNotifications().GetAwaiter().GetResult();

                }

                /* MICK: URGENT FIX
                if (ctx.GenerateReport==true)
                {
                    //Generate the report
                    var url = GenerateMonitorReport.GenerateMonitorDataReport(_logger, htMonitorData.UserExId, htMonitorData.ExTaskId, htMonitorData.ExTransactionId).GetAwaiter().GetResult();
                    htMonitorData.ReportUrl = url;
                }
                */
                var assessResults = HTJsonSerialiser.Deserialise<TelehealthMonitorData>(HTJsonSerialiser.Serialise(htMonitorData));
                assessResults.Thresholds = null;
                assessResults.AlertNotifications = null;


                #region Partner Integration
                if (!string.IsNullOrEmpty(htMonitorData.ExTransactionId))
                {
                    try
                    {
                        SystemLog("Information", "ProcessMonitoringAssessment:Partners", htMonitorData.ExTransactionId ?? "null");

                        var partnerMappingDetails = PartnerIntegrationHelper.GetFacilityPartnerMapping(th.ExFacilityId, htMonitorData.UserExId).GetAwaiter().GetResult();

                        //send task to TLW
                        if (PartnerIntegrationHelper.IsPartnerTheLookOut(partnerMappingDetails))
                        {
                            _logger.LogInformation(HTJsonSerialiser.Serialize(partnerMappingDetails));

                            PartnerIntegrationHelper.SendVitalToLookOut(htMonitorData.ExNurseId, htMonitorData.UserExId, htMonitorData.ExTransactionId, partnerMappingDetails, _logger).GetAwaiter().GetResult();
                        }
                        else
                        {
                            var medtech = new MedtechIntegrationHelper(_logger);
                            if (medtech.IsPartnerMedtech(partnerMappingDetails))
                            {
                                _logger.LogInformation("MonitoringdataProcessor : Medtech");
                                _logger.LogInformation(HTJsonSerialiser.Serialize(partnerMappingDetails));

                                medtech.SendVitalsToMedtech(htMonitorData.ExNurseId, htMonitorData.UserExId, htMonitorData.ExTransactionId, partnerMappingDetails).GetAwaiter().GetResult();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        SystemLog("Error", "ProcessMonitoringAssessment:Partners", ex.ToString());
                        _logger.LogError($"Error while sending vitals to a partner integration: {ex.ToString()}");
                    }

                }
                #endregion


                return assessResults;

            }
            catch (System.Exception ex)
            {
                SystemLog("Error", "ProcessMonitoringAssessment", ex.ToString());
                _logger.LogError($"ERROR in MonitoringdataProcessor: {ex.ToString()}");
                throw;
            }



        }

        private static void SystemLog(string loggingLevel, string whereOccurred, string message, string json = "")
        {
            string process = "Clinical Data";
            try
            {
                var res = DBHelper.ExecSprocByParams("sp_Insert_Syslog",
                    new System.Collections.Generic.Dictionary<string, string>
                    {
                        {"loggingLevel",loggingLevel},
                        {"process",process},
                        {"whereOccurred",whereOccurred},
                        {"message",message},
                        {"json",json }
                    });
            }
            catch (Exception)
            {
            }
        }
    }
}
