using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.common.backend.shared.helpers;
using ht.be.fn.Helpers;
using System.Collections.Generic;
using System.Text.Json;
using ht.be.fn.Models.TheLookOut;
using ht.data.common.partner;
using System.Linq;

namespace ht.be.fn
{
    public static class TLWMemberSync
    {
        [FunctionName("TLWMemberSync")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var eventData = JsonConvert.DeserializeObject<TLW_MemberEvent>(requestBody);

            if ( eventData==null && eventData?.data?.community?.id == null)
                return new BadRequestResult();

            var partnerSetting = await GetPartnerSettingsAsync(eventData.data.community.id.ToString(), log);
            if (partnerSetting == null)
                return new BadRequestResult();

            var (importFlagFieldName, importFlagFieldValue) = GetImportFlags(partnerSetting.SettingsJSON);
            if (string.IsNullOrWhiteSpace(importFlagFieldName) || string.IsNullOrWhiteSpace(importFlagFieldValue))
            {
                log.LogInformation("Import flag field or value is blank. Importing all members.");
                await UpsertMembersAsync(eventData.data, log);
                return new OkResult();
            }

            var importFlagField = eventData.data.custom_attributes
                .FirstOrDefault(attr => attr.key == importFlagFieldName)?.value?.ToString();

            if (importFlagField == importFlagFieldValue)
            {
                log.LogInformation("Member meets import criteria. Importing/updating member.");
                log.LogInformation($"{JsonConvert.SerializeObject(eventData.data)}");
                await UpsertMembersAsync(eventData.data,log);
            }
            else
            {
                log.LogInformation("Member does not meet import criteria. Skipping member.");
            }

            return new OkResult();
        }

        private static async Task<PartnerIntegrationMapping> GetPartnerSettingsAsync(string communityId, ILogger log)
        {
            try
            {
                var dbResponse =  DBHelper.ExecSprocById("sp_getTLWSettingsBy_CommunityId", communityId, null);
                return JsonConvert.DeserializeObject<PartnerIntegrationMapping>(dbResponse);
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error retrieving partner settings.");
                return null;
            }
        }

        private static (string importFlagFieldName, string importFlagFieldValue) GetImportFlags(string settingsJson)
        {
            dynamic partnerConfig = JsonConvert.DeserializeObject(settingsJson);
            return (partnerConfig.importFlagFieldName, partnerConfig.importFlagFieldValue);
        }

        private static async Task UpsertMembersAsync(object data,ILogger log)
        {
            try
            {
                await DBHelper.ExecSprocWithJsonAsync("sp_Partner_TLW_Members_Upsert", JsonConvert.SerializeObject(data));
            }
            catch (Exception ex)
            {
               log.LogError("Error while Member upsert from TLW", ex.Message);
                throw new InvalidOperationException("Error during member upsert.", ex);
            }
        }
    }



}

