using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.common.backend.shared.helpers;
using ht.be.fn.Helpers;
using System.Collections.Generic;
using System.Text.Json;
using ht.be.fn.Models.Medtech;
using System.Net.Http;
using System.Net;
using ht.data.common.partner;
using System.Linq;
using ht.data.common.Users;
using ht.be.fn.Utils;



namespace ht.be.fn
{
    /// <summary>
    /// This endpoint is called by Medtech Evolution to get a "pane of glass" of patient details, clinical data and wounds etc.
    /// It passes a context string and a signature which are used to decode the patient and provider details.
    /// </summary>
    public static class MedtechPatientDetails
    {
        [FunctionName("MedtechPatientDetails")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            string context = "", signature = "";
            if (req.Query.ContainsKey("Context"))
                context = req.Query["Context"];
            if (req.Query.ContainsKey("Signature"))
                signature = req.Query["Signature"];

            // log the request
            log.LogInformation(" ");
            log.LogInformation($"*** MedtechPatientDetails request received. Context: {context}, Signature: {signature}");

            MedtechIntegrationHelper medtech = new MedtechIntegrationHelper(log);

            // if the request does not contain required values then return 403 Forbidden
            if (context == "" || signature == "")
            {
                log.LogInformation("MedtechAlex request does not have context or signature. Returning 403 Forbidden.");
                return new StatusCodeResult((int)HttpStatusCode.Forbidden);
            }

            // At this point we do not know the facility, it's inside the context, so we get some default mapping details instead
            var defaultMappingDetails = await PartnerIntegrationHelper.GetFacilityPartnerMappingBySourceId("DEFAULT", "Medtech");

            if (defaultMappingDetails?.Count == 0)
            {
                log.LogError("No Medtech mapping details found on HTC partner.");
                return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
            }

            string token = medtech.GetMedtechAccessToken(defaultMappingDetails.FirstOrDefault()).GetAwaiter().GetResult();
            if (!String.IsNullOrEmpty(token))
            {
                log.LogDebug("Medtech access token retrieved successfully.");
            }
            else
            {
                log.LogError("Failed to retrieve Medtech access token.");
                return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
            }

            // Call the alex api to decode the context string
            try
            {
                var client = new HttpClient();

                // TODO: get this endpoint address from tblPartnerIntegration
                var request = new HttpRequestMessage(HttpMethod.Get, $"{defaultMappingDetails[0].IntegrationURL}vendorforms/api/getlaunchcontextstring/{context}/{signature}");

                request.Headers.Add("Authorization", $"Bearer {token}");
                var content = new StringContent("", null, "text/plain");
                request.Content = content;
                var response = await client.SendAsync(request);

                if (!response.IsSuccessStatusCode)
                {
                    log.LogError("Error when decoding the context string from Medtech Alex" + response.ReasonPhrase);
                    return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
                }

                // example: {"patientId":"14E52E16EDB7A435BFA05E307AFD008B","facilityCode":"F99597-D","providerId":"A2CE992E9DBBA1F39BAD3B6CEA6D32FE","createdTime":"2024-08-13T10:15:44.3954859Z"}
                string contextjson = await response.Content.ReadAsStringAsync();
                var contextData = JsonConvert.DeserializeObject<MedtechPatientContext>(contextjson);

                if (contextData.patientId is null)
                    return new StatusCodeResult((int)HttpStatusCode.NotFound);

                // log the context data
                log.LogInformation($"MedtechAlex context data: {contextData.patientId}, {contextData.facilityCode}, {contextData.providerId}, {contextData.createdTime}");

                // map the context facility to our facility
                //## There might not be patient on partner table on initial call, so did some left join on procedure
                var partnerMappingDetails = PartnerIntegrationHelper.GetFacilityPartnerMappingBySourceId(contextData.facilityCode, "Medtech").GetAwaiter().GetResult();
                if (medtech.IsPartnerMedtech(partnerMappingDetails))
                {
                    log.LogInformation(JsonConvert.SerializeObject(partnerMappingDetails));
                }
                else
                {
                    log.LogInformation("Not a Medtech partner or no mapping has been configured.");
                    return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
                }

                // For Medtech, we are only importing patients and doctors when they are needed, so we can upsert them here because the doctor
                // has requested a teleconference call between them and the patient

                // upsert the patient from the context as a resident to our system and return the mapped userExId
                var patient_userExId = await medtech.UpsertEntityFromMedtechToHT(partnerMappingDetails, contextData.patientId, "patient");

                // upsert the doctor from the context to our system and return the mapped userExId
                var doctor_userExId = await medtech.UpsertEntityFromMedtechToHT(partnerMappingDetails, contextData.providerId, "doctor");

                // Get a link to the read-ony "Resident Details" as we have called it elsewhere
                string detailsLink = "";
                var facility_userExId = partnerMappingDetails[0].ExFacilityId;

                try
                {
                    // Get doctor mapping
                    var doctorMap = PartnerIntegrationHelper.GetStaffMap(partnerMappingDetails, doctor_userExId);
                    if (doctorMap == null)
                        log.LogError($"Doctor mapping not found for sourceId {doctor_userExId}");

                    // Get patient mapping
                    var patientMap = PartnerIntegrationHelper.GetResidentMap(partnerMappingDetails, patient_userExId);
                    if (patientMap == null)
                        log.LogError($"Patient mapping not found for sourceId {patient_userExId}");

                    // create the resident details link
                    // TODO: maybe get existing details link from the database if still valid
                    var meetingResponse = await medtech.CreateResidentDetailsAsync(patientMap, doctorMap, doctor_userExId);
                    log.LogInformation($"CreateResidentDetailsAsync response link : {meetingResponse}");

                    if (!String.IsNullOrEmpty(meetingResponse))
                    {
                        detailsLink = meetingResponse;
                    }
                }
                catch (Exception ex)
                {
                    log.LogError("Error when creating patient details link for Medtech in MedtechPatientDetails: " + ex.ToString());
                    return new ContentResult { Content = "An exception error was encountered and no details for the patient could be retrieved at this time. Please close and retry. If the issue persists then please contact support.", StatusCode = 200 };
                }

                if (string.IsNullOrEmpty(detailsLink))
                {
                    log.LogInformation("No details link could be generated. Returning a message to user.");
                    return new ContentResult { Content = "No details for the patient could be retrieved from Medtech Connect.", StatusCode = 200 };
                }
                else
                {
                    log.LogDebug($"Redirecting to patient details link : {detailsLink}");
                    return new RedirectResult(detailsLink, true);
                }
            }
            catch (Exception ex)
            {
                log.LogError("Error when decoding the context string from Medtech for MedtechPatientDetails: " + ex.Message);
                return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
            }
        }
    }
}

