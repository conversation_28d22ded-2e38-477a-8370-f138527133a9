using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.common.backend.shared.helpers;
using ht.be.fn.Helpers;
using System.Collections.Generic;
using System.Text.Json;
using ht.be.fn.Models.Medtech;
using System.Net.Http;
using System.Net;
using ht.data.common.partner;
using System.Linq;

namespace ht.be.fn
{
    /// <summary>
    /// This endpoint is called by Medtech Evolution to launch a teleconference call.
    /// It passes a context string and a signature which are used to decode the patient and provider details.
    /// </summary>
    public static class MedtechTeleConf
    {
        [FunctionName("MedtechTeleConf")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            string context = "", signature = "";
            if (req.Query.ContainsKey("Context"))
                context = req.Query["Context"];
            if (req.Query.ContainsKey("Signature"))
                signature = req.Query["Signature"];

            // log the request
            log.LogInformation($"MedtechTeleConf request received. Context: {context}, Signature: {signature}");

            MedtechIntegrationHelper medtech = new MedtechIntegrationHelper(log);


            #region test ip
            // Test IP address using https://api.ipify.org/
            /*
            try
            {
                log.LogInformation("Testing outbound IP address (TEST)");

                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Get, "https://api.ipify.org/");
                var content = new StringContent("", null, "text/plain");
                request.Content = content;
                var response = await client.SendAsync(request);
                if (!response.IsSuccessStatusCode)
                {
                    log.LogInformation($"Error returned from get ip address (TEST): {response}");
                    //return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
                }
                else
                {
                    log.LogInformation($"success returned from get ip address (TEST): {response}");
                    string contextStr = await response.Content.ReadAsStringAsync();
                    log.LogInformation($"IP Address : {contextStr}");
                }
            }
            catch (Exception ex)
            {
                log.LogInformation("Error attempting get ip address (TEST) : " + ex.Message);
            }
            */
            #endregion
            // if the request does not contain required values then return 403 Forbidden
            if (context == "" || signature == "")
            {
                log.LogInformation("MedtechAlex request does not have context or signature. Returning 403 Forbidden.");
                return new StatusCodeResult((int)HttpStatusCode.Forbidden);
            }

            // At this point we do not know the facility, it's inside the context, so we get some default mapping details instead
            var defaultMappingDetails = await PartnerIntegrationHelper.GetFacilityPartnerMappingBySourceId("DEFAULT", "Medtech");

            if (defaultMappingDetails?.Count == 0)
            {
                log.LogInformation("No Medtech mapping details found on HTC partner.");
                return new StatusCodeResult((int)HttpStatusCode.InternalServerError);

            }
            string token = medtech.GetMedtechAccessToken(defaultMappingDetails.FirstOrDefault()).GetAwaiter().GetResult();
            if (!String.IsNullOrEmpty(token))
            {
                log.LogInformation("Medtech access token retrieved successfully.");
            }
            else
            {
                log.LogInformation("Failed to retrieve Medtech access token.");
                return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
            }

            // Call the Alex API to decode the context string
            try
            {
                var client = new HttpClient();

                // TODO: get this endpoint address from tblPartnerIntegration
                var request = new HttpRequestMessage(HttpMethod.Get, $"{defaultMappingDetails[0].IntegrationURL}vendorforms/api/getlaunchcontextstring/{context}/{signature}");

                request.Headers.Add("Authorization", $"Bearer {token}");
                var content = new StringContent("", null, "text/plain");
                request.Content = content;
                var response = await client.SendAsync(request);

                if (!response.IsSuccessStatusCode)
                {
                    log.LogError("Error when decoding the context string from Medtech Alex" + response.ReasonPhrase);
                    return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
                }

                // example: {"patientId":"14E52E16EDB7A435BFA05E307AFD008B","facilityCode":"F99597-D","providerId":"A2CE992E9DBBA1F39BAD3B6CEA6D32FE","createdTime":"2024-08-13T10:15:44.3954859Z"}
                string contextjson = await response.Content.ReadAsStringAsync();
                var contextData = JsonConvert.DeserializeObject<MedtechPatientContext>(contextjson);

                if (contextData.patientId is null)
                    return new StatusCodeResult((int)HttpStatusCode.NotFound);

                // log the context data
                log.LogInformation($"MedtechAlex context data: {contextData.patientId}, {contextData.facilityCode}, {contextData.providerId}, {contextData.createdTime}");

                // map the context facility to our facility
                //## There might not be patient on partner table on initial call, so did some left join on procedure
                var partnerMappingDetails = PartnerIntegrationHelper.GetFacilityPartnerMappingBySourceId(contextData.facilityCode, "Medtech").GetAwaiter().GetResult();
                if (medtech.IsPartnerMedtech(partnerMappingDetails))
                {
                    log.LogInformation(JsonConvert.SerializeObject(partnerMappingDetails));
                }
                else
                {
                    log.LogInformation("Not a Medtech partner or no mapping has been configured.");
                    return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
                }

                // For Medtech, we are only importing patients and doctors when they are needed, so we can upsert them here because the doctor
                // has requested a teleconference call between them and the patient

                // upsert the patient from the context as a resident to our system and return the mapped userExId
                var patient_userExId = await medtech.UpsertEntityFromMedtechToHT(partnerMappingDetails, contextData.patientId, "patient");

                // upsert the doctor from the context to our system and return the mapped userExId
                var doctor_userExId = await medtech.UpsertEntityFromMedtechToHT(partnerMappingDetails, contextData.providerId, "doctor");

                // Get a scheduled consult for the doctor and patient for today - if it exists then use the one that is scheduled, otherwise create a new one
                var (meetingLink, incompatibleHosting) = await medtech.GetOrCreateTeleConfMeeting(patient_userExId, doctor_userExId, partnerMappingDetails, log);

                log.LogInformation($"Meeting link returned : {meetingLink}. Incompatible host : {incompatibleHosting}");

                // TODO: maybe authenticate the user to the web portal and redirect to the conference call instead of using a meeting link

                if (string.IsNullOrEmpty(meetingLink))
                {
                    log.LogInformation("No meeting code found. Returning a message to user.");
                    return new ContentResult { Content = "An error occurred and a meeting was not able to be initiated. Please close and retry. If the issue persists then please contact support.", StatusCode = 200 };
                }
                else
                {
                    if (incompatibleHosting)
                    {
                        log.LogInformation("Incompatible hosting flag set for the facility. Returning meeting link so user can copy and paste to a local browser.");

                        var htmlContent = @"
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <title>Custom HTML Response</title>
                                <style>
                                    body {
                                        font-family: Arial, sans-serif;
                                        margin: 40px;
                                        background-color: #f4f4f9;
                                        color: #333;
                                    }
                                    .container {
                                        max-width: 600px;
                                        margin: auto;
                                        padding: 20px;
                                        background-color: #fff;
                                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                                        border-radius: 8px;
                                    }
                                    h1 {
                                        color: #0056b3;
                                    }
                                    p {
                                        line-height: 1.6;
                                    }
                                    a {
                                        color: #0056b3;
                                        text-decoration: none;
                                        font-weight: bold;
                                    }
                                    a:hover {
                                        text-decoration: underline;
                                    }
                                </style>
                            </head>
                            <body>
                                <div class='container'>
                                    <h1>Medtech Connect Telehealth Meeting</h1>
                                    <p>Please click on the following link to copy it to the clipboard and paste it into a browser on your <b>local computer</b> to join the call:</p>
                                    <p><a href='{url}' onclick='copyToClipboard(); return false;'>{url}</a></p>
                                </div>
                                <script>
                                    function copyToClipboard() {
                                        var copyText = '{url}';
                                        navigator.clipboard.writeText(copyText).then(function() {
                                            alert('Telehealth meeting link copied to clipboard!');
                                        }, function(err) {
                                            console.error('Could not copy text: ', err);
                                        });
                                    }
                                </script>
                            </body>
                            </html>
                        ";
                        htmlContent = htmlContent.Replace("{url}", meetingLink);
                        return new ContentResult { Content = htmlContent, ContentType = "text/html", StatusCode = 200 };
                    }

                    log.LogInformation("Redirecting to the telehealth call.");
                    return new RedirectResult(meetingLink, true);
                }
            }
            catch (Exception ex)
            {
                log.LogError("Error when decoding the context string from Medtech Alex" + ex.Message);
                return new StatusCodeResult((int)HttpStatusCode.InternalServerError);
            }
        }
    }
}

