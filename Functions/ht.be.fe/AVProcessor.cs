using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using FFmpeg.NET;
using ht.be.fn.Utils;
using System.Text.Json.Nodes;

namespace ht.be.fn
{
    public static class AVProcessor
    {
        [FunctionName("GetProcessorDetails")]
        public static async Task<IActionResult> GetProcessorDetails(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req,
            ILogger log, ExecutionContext context)
        {
            try
            {
                log.LogInformation("C# Get Processor Details");
                var ffmpeg = new Engine(context.FunctionAppDirectory + @"\ffmpeg.exe");

                var inputFile = new InputFile(@"C:\Users\<USER>\Downloads\sample.wav");

                var md = await ffmpeg.GetMetaDataAsync(inputFile, System.Threading.CancellationToken.None);
                var obj = new
                {
                    Duration = md.Duration,
                    AudioData = md.AudioData,
                    VideoData = md.VideoData
                };

                return new OkObjectResult(obj);
            }
            catch(Exception ex)
            {
                return new JsonResult(ex);
            }
        }
    }
}
