﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace ht.be.fn.Utils;

public class HTJsonSerialiser
{
    private static object obj = new object();
    private static JsonSerializerOptions opt = null;
    public static JsonSerializerOptions opts
    {
        get
        {
            if (opt == null)
            {
                lock (obj)
                {
                    if (opt == null)
                    {
                        var o = new JsonSerializerOptions
                        {
                            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                            PropertyNameCaseInsensitive = true,
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                        };
                        o.Converters.Add(new JsonStringEnumConverter());
                        opt = o;
                    }
                }
            }
            return (opt);
        }
    } 

    public static string Serialise(object o)
    {
        return JsonSerializer.Serialize(o, opts);
    }

    public static string Serialize(object o)
        => HTJsonSerialiser.Serialise(o);

    public static T Deserialise<T>(string o)
        => JsonSerializer.Deserialize<T>(o,opts);

    public static T Deserialize<T>(string o)
        => JsonSerializer.Deserialize<T>(o);

}
