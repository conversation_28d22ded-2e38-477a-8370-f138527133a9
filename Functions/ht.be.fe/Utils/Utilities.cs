﻿using Microsoft.AspNetCore.StaticFiles;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.fn.Utils
{
    public class Utilities
    {
        public static string svc_acct_UserExId = null;
        public static string os_env { get; set; } = "x86";

        public static string CPU
        {
            get
            {
                return os_env;
            }
        }
        public static string GetJsonSecurity()
        {
            return "{\"userExId\":\"" + svc_acct_UserExId + "\"}";
        }

        public static string GetMimeType(string filename)
        {
            var prov = new FileExtensionContentTypeProvider();
            string ct = string.Empty;
            if (!prov.TryGetContentType(filename, out ct))
                ct = "application/octet-stream";
            return (ct);
        }

        public static string UpdateProfile(string UserExId, string url,string ftype,string jsonSecurity="")
        {
            //we need to update the DB with the new profile URL
            var json = "{\"profilePic\":\"" + url + "\",\"UserExId\":\"" + UserExId + "\",\"ftype\":\"" + ftype + "\"}";
            return Helpers.DBHelper.ExecSprocWithJsonAsync("sp_User_ProfilePic_Upsert", json,GetJsonSecurity()).GetAwaiter().GetResult();  
        }

        /// <summary>
        /// TRUE if the process is running under WOW64. That is if it is a 32 bit process running on 64 bit Windows.
        /// If the process is running under 32-bit Windows, the value is set to FALSE. 
        /// If the process is a 64-bit application running under 64-bit Windows, the value is also set to FALSE.
        /// </summary>
        [DllImport("kernel32.dll", SetLastError = true)]
        static extern bool IsWow64Process(System.IntPtr aProcessHandle, out bool isWow64Process);

        /// <summary>
        /// Indicates if the process is 32 or 64 bit.
        /// </summary>
        /// <param name="aProcessHandle">process to query</param>
        /// <returns>true: process is 64 bit; false: process is 32 bit</returns>
        public static bool Is64BitProcess(System.IntPtr aProcessHandle)
        {
            if (!System.Environment.Is64BitOperatingSystem)
                return false;

            bool isWow64Process;
            if (!IsWow64Process(aProcessHandle, out isWow64Process))
                throw new Win32Exception(Marshal.GetLastWin32Error());

            return !isWow64Process;
        }

        public static bool Is64BitProcess()
        {
            var r =  Is64BitProcess(System.Diagnostics.Process.GetCurrentProcess().Handle);
            if (os_env != "linux")
            {
                os_env = (r == true) ? "x64" : "x86";
            }
            return r;
        }
    }
}
