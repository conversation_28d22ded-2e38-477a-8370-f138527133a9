using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Text.Json;
using System.Linq;
using ht.data.common.Users;
using ht.data.common.Telehealth;
using ht.be.fn.Utils;
using ht.common.backend.shared.helpers;

namespace ht.be.fn
{
    public static class UserFileGetSasToken
    {
        private static string storageAccount = Environment.GetEnvironmentVariable("IotStorageAccountName");
        private static string storageKey = Environment.GetEnvironmentVariable("IotStorageAccountKey");

        [FunctionName("UserFileGetSasToken")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function,"post", Route = null)] HttpRequest req,
            ILogger log)
        {
            try
            {
                var ask = new AskForAccessRequest { Files = new List<HealthTeamsFile>() };
                var bHelper = new BlobHelper(storageAccount, storageKey);
                
                var iTime = 60; //60 mins.
                if (req.Query.ContainsKey("itime"))
                    int.TryParse(req.Query["itime"],out iTime);

                if (iTime==0)
                    iTime = 60;

                var data = await req.ReadAsStringAsync();
                if (IsJson(data))
                {
                    ask = HTJsonSerialiser.Deserialise<AskForAccessRequest>(data);
                }
                else //single URL
                {
                    ask.Files.Add(new HealthTeamsFile {  FileUrl=data});
                    log.LogInformation($"UserFileGetSasToken userUrl-{data}");
                }

                if (ask?.Files?.Count > 0)
                    ask.Files = ask.Files.Select(u =>
                    {
                        if (u.FileUrl?.StartsWith("https://") == true)
                            u.FileUrl += "?" + bHelper.GetSasToken(u.FileUrl, iTime).GetAwaiter().GetResult();
                        if (u.FileThumbUrl?.StartsWith("https://") == true)
                            u.FileThumbUrl += "?" + bHelper.GetSasToken(u.FileThumbUrl, iTime).GetAwaiter().GetResult();
                        return u;
                    }).ToList();
                return new JsonResult(ask);
                
            }
            catch (Exception ex)
            {
                log.LogError(ex, "UserFileGetSasToken");
                return new BadRequestObjectResult($"ERROR:{ex.Message}");
            }
        }

        public static bool IsJson(string req)
        {
            try
            {
                ReadOnlySpan<byte> bytes = System.Text.Encoding.UTF8.GetBytes(req);
                Utf8JsonReader rdr = new Utf8JsonReader(bytes);
                return rdr.TrySkip();
            }
            catch
            {
                return false;
            }
                
        }
    }
}
