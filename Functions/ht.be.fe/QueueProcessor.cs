using System;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Host;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using Azure.Messaging.ServiceBus;
using System.Linq;
using System.Text;
using System.Text.Unicode;
using System.Text.Json;
using System.Text.Json.Nodes;
using ht.data.common.Extensions;

namespace ht.be.fn
{
    public static class QueueProcessor
    {
        /*
        [FunctionName("QueueProcessor")]
        public static void Run([ServiceBusTrigger("webhkq", Connection = "HTMsgBusReceiver")] ServiceBusReceivedMessage[] msgs, ILogger log)
        {
            var lst = new List<string>();

            foreach (var msg in msgs)
            {
                if (msg!=null && !string.IsNullOrEmpty(msg.Subject) && msg.Subject.StartsWith("Acs Event"))
                {
                    
                    var bdy = Encoding.UTF8.GetString(System.Convert.FromBase64String(msg.Body?.ToString()));
                    var subject = msg.Subject?.Replace("Acs Event-", "");
                    log?.LogInformation($"AcsRecording- About to process Subject - {subject} and {bdy}");
                    if (!string.IsNullOrEmpty(bdy))
                        Helpers.DBHelper.ExecSprocByParams("dbo.sp_MeetingAuditLogs_Insert_AcsEvents", new Dictionary<string, string> { { "json", bdy }, { "subject", subject } });


                    //let's process the call recording file.
                    if (!string.IsNullOrEmpty(subject) && subject.Contains("/recording"))
                    {
                        processRecording(log, bdy, subject);

                    }


                }
            }
            
        }
        */

        public static void processRecording(ILogger log, string bdy, string subject)
        {
            try
            {
                var serverCallId = Helpers.AcsManager.ExtractServerCallId(subject);
                log?.LogInformation($"AcsRecording- Extracted Server CallId - {serverCallId}");
                //Let's see what we have,
                var obj = JsonNode.Parse(bdy);

                if (!string.IsNullOrEmpty(obj["sessionEndReason"].GetValue<string>()))
                {
                    var chunk = obj["recordingStorageInfo"]["recordingChunks"]?.AsArray()?.FirstOrDefault();
                    var docId = chunk["documentId"].GetValue<string>();

                    if (!string.IsNullOrEmpty(docId))
                    {
                        var acsMgr = new Helpers.AcsManager(null);
                        var url = acsMgr.SaveRecording(docId).GetAwaiter().GetResult();
                        var json = HTJsonSerialiser.Serialise(new
                        {
                            serverCallId = serverCallId,
                            url = url
                        });
                        log?.LogInformation($"AcsRecording-Calling Meeting AcsRecording Upser with {json}");
                        Helpers.DBHelper.ExecSproc("dbo.sp_Meeting_AcsRecording_Upsert", json);

                    }

                }
            }
            catch(Exception ex)
            {
                log.LogError(ex, "ERROR - Processing AcsRecording");
            }
        }
    }
}
