﻿using Azure.Core;
using Azure.Identity;
using ht.integration.mhr.fn.Helpers;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mime;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;


[assembly: FunctionsStartup(typeof(ht.integration.mhr.fn.Startup))]
namespace ht.integration.mhr.fn;


public class Startup : FunctionsStartup
{
    private static IConfiguration _configuration = null;

    private static string TenantId = null;
    private static string ClientId = null;
    private static string ClientSecret = null;

    public static string AzKvUri = null;

    

    public override void Configure(IFunctionsHostBuilder builder)
    {
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
        ServicePointManager.DefaultConnectionLimit = 4000;

        TokenCredential creds = null;

        //var connectionString = Environment.GetEnvironmentVariable("ConnectionStrings:DBConnection");

        var serviceProvider = builder.Services.BuildServiceProvider();
        _configuration = serviceProvider.GetRequiredService<IConfiguration>();

        //need to extract the tenantId, client Id, client secret
        var tenantId = Utils.GetVariable("HT_MHR_TenantId") ?? "e6a1adfb-abaf-4fb0-b0b1-a5dcd7c60baf";//"6b127268-dbaf-4ffc-a262-23fcb73619f9";
        var clientId = Utils.GetVariable("HT_MHR_ClientId") ?? "cd69c2bf-1df6-488c-b2ed-fe7a74b0cebf"; //"66e5a124-4cf6-49ac-9565-bba8b9087859"; //- ht integration services.
        var clientsecret = Utils.GetVariable("HT_MHR_SecretId") ?? "****************************************"; //"****************************************";

        if (Utils.IsInAzure)
        {
            var azOpts = new DefaultAzureCredentialOptions();
            azOpts.AdditionallyAllowedTenants.Add("*");
            creds = new DefaultAzureCredential(azOpts);
        }
        else
        {
            var csOpts = new ClientSecretCredentialOptions();
            csOpts.AdditionallyAllowedTenants.Add("*");
            creds = new ClientSecretCredential(tenantId, clientId, clientsecret, csOpts);
        }


        MHRUtils.AzKv = new AzureKeyvaultHelper(Startup.AzKvUri, creds); //instanciate helper.
        
        //MHRUtils.PopulateBothCert("mhr-hpio-79-cert");
        MHRUtils.PopulateTLsCert("mhr-csp-cert");
        MHRUtils.PopulateSignCert("mhr-csp-cert");

    }

    public override void ConfigureAppConfiguration(IFunctionsConfigurationBuilder builder)
    {
        //var tenantId = "e6a1adfb-abaf-4fb0-b0b1-a5dcd7c60baf";//"6b127268-dbaf-4ffc-a262-23fcb73619f9";
        var clientId = "cd69c2bf-1df6-488c-b2ed-fe7a74b0cebf"; //"66e5a124-4cf6-49ac-9565-bba8b9087859"; //- ht integration services.
        var clientsecret = "****************************************"; //"****************************************";

        Startup.AzKvUri = Utils.GetVariable("HT_KV_URI") ?? "https://kv-ht-uat.vault.azure.net/";

        FunctionsHostBuilderContext context = builder.GetContext();

        var config = builder.ConfigurationBuilder
            .AddJsonFile(Path.Combine(context.ApplicationRootPath, "local.settings.json"), optional: true, reloadOnChange: false)
            .AddJsonFile(Path.Combine(context.ApplicationRootPath, $"local.settings.{context.EnvironmentName}.json"), optional: true, reloadOnChange: false)
            .AddEnvironmentVariables()
            .AddEnvironmentVariables(prefix: "HTBEINTMHR:");

        var defOpts = new DefaultAzureCredentialOptions();
        defOpts.AdditionallyAllowedTenants.Add("*");
        var creds = new DefaultAzureCredential(defOpts);

        if (Utils.IsInAzure)
            config.AddAzureKeyVault(new Uri(Startup.AzKvUri), creds, new PrefixKeyVaultSecretManager("htbeintmhr"));
        else
            config.AddAzureKeyVault(Startup.AzKvUri,clientId,clientsecret, new PrefixKeyVaultSecretManager("htbeintmhr"));

    }
}
