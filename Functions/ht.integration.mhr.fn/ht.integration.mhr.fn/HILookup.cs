using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ht.integration.mhr.fn.Models;

namespace ht.integration.mhr.fn
{
    public static class HILookup
    {
        [FunctionName("HILookup")]
        public static async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            //This is our test case
            var srch = new HIRequest
            {
                FirstName = "Kirrily",
                LastName = "Arnold",
                IHI = "8003608666672753",
                Dob = "1955-06-26",
                Sex = "F"
            };

            var obj = MHRUtils.DoHILookup(srch);

            return new OkObjectResult(obj);
        }
    }
}
