﻿using Azure.Core;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.WindowsAzure.Storage.Blob.Protocol;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace ht.integration.mhr.fn;

public class AzureKeyvaultHelper
{
    public string KvUri = null;
    private TokenCredential _creds = null;
    private SecretClient _client = null;

    public AzureKeyvaultHelper(string kv_uri, TokenCredential creds)
    {
        this.KvUri = kv_uri;
        this._creds = creds;

        _client = new SecretClient(new Uri(kv_uri), creds);
    }

    public X509Certificate2 GetCertificate(string certName)
    {
       KeyVaultSecret secret = _client.GetSecret(certName);
       byte[] certificate = Convert.FromBase64String(secret.Value);
       X509Certificate2 x509 = new X509Certificate2(certificate);
       return x509;
    }
}
