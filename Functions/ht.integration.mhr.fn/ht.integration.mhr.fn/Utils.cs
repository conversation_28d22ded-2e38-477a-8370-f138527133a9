﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.integration.mhr.fn
{
    public class Utils
    {
        public static bool IsInAzure
        {
            get
            {
                return (System.Environment.GetEnvironmentVariable("WEBSITE_INSTANCE_ID") != null);
            }
        }

        public static string GetVariable(string name)
        {
            return System.Environment.GetEnvironmentVariable(name);
        }
    }
}
