﻿using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.AzureKeyVault;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.integration.mhr.fn.Helpers
{
    public class PrefixKeyVaultSecretManager : KeyVaultSecretManager, IKeyVaultSecretManager
    {
        private readonly string _prefix;

        public PrefixKeyVaultSecretManager(string prefix)
        {
            _prefix = $"{prefix}-";
        }

        public override bool Load(SecretProperties secret)
        {
            return secret.Name.StartsWith(_prefix);
        }

        public override string Get<PERSON>ey(KeyVaultSecret secret)
        {
            return secret.Name
                .Substring(_prefix.Length)
                .Replace("--", ConfigurationPath.KeyDelimiter);
        }

        public bool Load(Microsoft.Azure.KeyVault.Models.SecretItem secret)
        {
            return secret.Identifier.Name.StartsWith(_prefix);
        }

        public string <PERSON><PERSON><PERSON>(Microsoft.Azure.KeyVault.Models.SecretBundle secret)
        {
            return secret.SecretIdentifier.Name
                .Substring(_prefix.Length)
                .Replace("--", ConfigurationPath.KeyDelimiter);
        }
    }
}
