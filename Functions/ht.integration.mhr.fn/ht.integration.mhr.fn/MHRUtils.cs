﻿using nehta.mcaR3.ConsumerSearchIHI;
using Nehta.VendorLibrary.Common;
using Nehta.VendorLibrary.HI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.ServiceModel.Channels;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;
using ht.integration.mhr.fn.Models;
using System.Runtime.CompilerServices;

namespace ht.integration.mhr.fn
{
    public class MHRUtils
    {
        public static AzureKeyvaultHelper AzKv = null;

        public static X509Certificate2 TlsCert;
        public static X509Certificate2 SignCert;

        public static MHRContext CallContext = null;

        public static string MHREndpoint = "https://www5.medicareaustralia.gov.au/cert/soap/services/";


        public static X509Certificate2 GetX509Certificate(string subName)
        {
            // Obtain the certificate by serial number
            X509Certificate2 cert = X509CertificateUtil.GetCertificate(
                subName,
                X509FindType.FindBySubjectName,
                StoreName.My,
                StoreLocation.CurrentUser,
                true
                );

            return cert;
        }

        public static object DoHILookup(HIRequest req)
        {
            var soapResponse = string.Empty;
            #region Setup PCIN
            // Set up client product information (PCIN)
            // Values below should be provided by Medicare
            ProductType product = new ProductType()
            {
                platform = "HealthTeams",     // Can be any value
                productName = "HealthTeams Lookup",                               // Provided by Medicare
                productVersion = "1.0",                         // Provided by Medicare
                vendor = new QualifiedId()
                {
                    id = "HTP00000",                                       // Provided by Medicare               
                    qualifier = "http://ns.electronichealth.net.au/id/hi/vendorid/1.0 "                          // Provided by Medicare
                }
            };

            // Set up user identifier details
            QualifiedId user = new QualifiedId()
            {
                id = "Mick123",                                             // User ID internal to your system
                qualifier = "http://healtheams/id/123/userid/1.0"    // Eg: http://ns.yourcompany.com.au/id/yoursoftware/userid/1.0
            };

            // Set up user identifier details
            QualifiedId hpio = new QualifiedId()
            {
                id = "8003628233368479", //"8003638233369062", // "8003628233368479",                                              // HPIO internal to your system
                qualifier = "http://ns.electronichealth.net.au/id/hi/hpio/1.0"
            };
            #endregion
            // ------------------------------------------------------------------------------
            // Client instantiation and invocation
            // ------------------------------------------------------------------------------

            // Instantiate the client
            ConsumerSearchIHIClient client = new ConsumerSearchIHIClient(
                new Uri(MHREndpoint),
                product,
                user,
                hpio,
                SignCert,
                TlsCert);

            // Set up the request
            searchIHI request = new searchIHI();
            if (!string.IsNullOrEmpty(req.IHI))
                request.ihiNumber = $"http://ns.electronichealth.net.au/id/hi/ihi/1.0/{req.IHI}";
            request.dateOfBirth = DateTime.Parse(req.Dob); //DateTime.Parse("12 Dec 2002");
            request.givenName = req.FirstName;
            request.familyName = req.LastName;
            if (req.Sex == "F")
                request.sex = SexType.F;
            else if (req.Sex == "M")
                request.sex = SexType.M;
            else
                request.sex = SexType.N;


            try
            {
                // Invokes a basic search
                searchIHIResponse ihiResponse = client.BasicSearch(request);
                return ihiResponse;
            }
            catch (FaultException fex)
            {
                string returnError = "";
                MessageFault fault = fex.CreateMessageFault();
                if (fault.HasDetail)
                {
                    ServiceMessagesType error = fault.GetDetail<ServiceMessagesType>();
                    // Look at error details in here
                    if (error.serviceMessage.Length > 0)
                        returnError = error.serviceMessage[0].code + ": " + error.serviceMessage[0].reason;
                }

                // If an error is encountered, client.LastSoapResponse often provides a more
                // detailed description of the error.
                soapResponse = client.SoapMessages.SoapResponse;
            }
            catch (Exception ex)
            {
                // If an error is encountered, client.LastSoapResponse often provides a more
                // detailed description of the error.
                soapResponse = client.SoapMessages.SoapResponse;
            }

            return soapResponse;
        }

        #region Certificate Utilities
        public static X509Certificate2 GetCertFromStore(string certName)
        {
            return AzKv.GetCertificate(certName);
        }
        public static void PopulateTLsCert(string kvCertName)
        {
            TlsCert = GetCertFromStore(kvCertName);
        }
        public static void PopulateSignCert(string kvCertName)
        {
            SignCert = GetCertFromStore(kvCertName);
        }

        public static void PopulateBothCert(string kvCertName)
        {
            SignCert = GetCertFromStore(kvCertName);
            TlsCert = SignCert;
        }
        #endregion
    }
}
