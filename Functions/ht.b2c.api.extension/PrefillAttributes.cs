using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using ht.data.common.Extensions;
using ht.b2c.api.extension.Service;
using ht.b2c.api.extension.models;

namespace ht.b2c.api.extension;

public static class PrefillAttributes
{

    [FunctionName("prefillAttributes")]
    public static async Task<IActionResult> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = null)] HttpRequest req, ILogger log)
    {
        try
        {
            //log.LogInformation("C# HTTP trigger function processed a request.");
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            //LogAllHeaders(req, log);

            log.LogInformation($"PrefillAttributes Request- {requestBody}");
            var r = JsonConvert.DeserializeObject<B2CPrefillRequest>(requestBody);



            /*
            #region Token Work
            var ac = data?.data?.authenticationContext;
            
            var ireq = new
            {
                ExternalId = (string)ac?.user?.id,
                Idtype = "AAD",
                UserEmail = (string)ac?.user?.mail,
                FirstName = (string)ac?.user?.giveName,
                LastName = (string)ac?.user?.surname,
                //UserMobile = req.Mobile,
                //UserMobileCC = req.MobileCC,
                UserType = ac.user?.userType
            };

            if (string.IsNullOrEmpty(ireq?.ExternalId))
                throw new KeyNotFoundException("User Id not found in incoming request");
            #endregion
            */
            B2CResponse rr = new B2CResponse();
            rr.data.odatatype = "microsoft.graph.onAttributeCollectionStartResponseData";
            rr.data.actions[0].odatatype = "microsoft.graph.attributeCollectionStart.setPrefillValues";
            rr.data.actions[0].claims = null;
            rr.data.actions[0].inputs = new Dictionary<string, dynamic>();

            rr.data.actions[0].inputs.Add("firstName", "MickTest");
            rr.data.actions[0].inputs.Add("inviteCode", "CodeTest");
            /*
            if (!string.IsNullOrEmpty(ireq?.ExternalId))
            {
                var json = HTJsonSerialiser.Serialise(ireq);
                log.LogInformation($"DB SQL Json Request - {json}");
                var res = await DBHelper.ExecSprocWithJsonAsync("sp_User_B2CAuthenticate", json);

                r.data.actions[0].claims.CustomRoles.Add(res);
            }
            else
            {
                r.data.actions[0].claims.CustomRoles.Add("Authentication failed - please supply login details");
            }
            */
            return (new OkObjectResult(rr));
        }
        catch(Exception ex)
        {
            throw;
        }

    }


}
