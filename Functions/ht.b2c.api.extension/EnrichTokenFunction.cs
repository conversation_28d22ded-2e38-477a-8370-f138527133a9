using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using ht.data.common.Extensions;
using ht.b2c.api.extension.Service;
using ht.b2c.api.extension.models;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ht.b2c.api.extension;

public static class EnrichTokenFunction
{

    [FunctionName("enrichTokenFunction")]
    public static async Task<IActionResult> Run(
        //[HttpTrigger(AuthorizationLevel.Function, "post", Route = null)] HttpRequest req, ILogger log)
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = null)] JObject req, ILogger log)
    {
        try
        {

            //log.LogInformation("C# HTTP trigger function processed a request.");
            //string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            string requestBody = req.ToString();
            //LogAllHeaders(req, log);

            log.LogInformation($"Request- {requestBody}");
            dynamic data = JsonConvert.DeserializeObject(requestBody);

            // Read the correlation ID from the Azure AD  request    
            string correlationId = data?.data?.authenticationContext?.correlationId ?? "";


            #region Token Work
            var ac = data?.data?.authenticationContext;

            var ireq = new
            {
                ExternalId = (string)ac?.user?.id,
                Idtype = "AAD",
                UserEmail = (string)ac?.user?.mail,
                FirstName = (string)ac?.user?.giveName,
                LastName = (string)ac?.user?.surname,
                //UserMobile = req.Mobile,
                //UserMobileCC = req.MobileCC,
                UserType = ac.user?.userType
            };

            if (string.IsNullOrEmpty(ireq?.ExternalId))
                throw new KeyNotFoundException("User Id not found in incoming request");
            #endregion

            B2CResponse rr = new B2CResponse();
            rr.data.actions[0].claims.CorrelationId = correlationId;
            rr.data.actions[0].claims.ApiVersion = "1.0.0";
            rr.data.actions[0].claims.DateOfBirth = "01/01/2000";

            if (!string.IsNullOrEmpty(ireq?.ExternalId))
            {
                var json = HTJsonSerialiser.Serialise(ireq);
                log.LogInformation($"DB SQL Json Request - {json}");
                var res = await DBHelper.ExecSprocWithJsonAsync("sp_User_B2CAuthenticate", json);

                dynamic results = JsonConvert.DeserializeObject<dynamic>(res);
           
                string windowsTz = Convert.ToString(results?.userLocalZone);
                if (!string.IsNullOrEmpty(windowsTz))
                {
                    if (TimeZoneConverter.TZConvert.TryWindowsToIana(windowsTz, out var tz))
                    {
                        results.userLocalZone = tz;
                    }
                }
                if (!string.IsNullOrEmpty(res))
                {
                    var res64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(Convert.ToString(results)));
                    rr.data.actions[0].claims.CustomRoles.Add(res64);
                }
            }
            else
            {
                rr.data.actions[0].claims.CustomRoles.Add("Authentication failed - please supply login details");
            }
            return (new OkObjectResult(rr));
        }
        catch (Exception ex)
        {
            throw;
        }

    }

    public static void LogAllHeaders(HttpRequest req, ILogger log)
    {
        foreach (var hdr in req.Headers)
        {
            log.LogInformation($"{hdr.Key}={string.Join(",", hdr.Value.ToArray())}");
        }
    }

}
