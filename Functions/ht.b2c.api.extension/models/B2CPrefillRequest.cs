﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.b2c.api.extension.models
{
    public class B2CPrefillRequest
    {
        public string type { get; set; }
        public string source { get; set; }
        public Data data { get; set; }
    }

    public class Data
    {
        [JsonProperty("@odata.type")]
        public string odatatype { get; set; }
        public string tenantId { get; set; }
        public string authenticationEventListenerId { get; set; }
        public string customAuthenticationExtensionId { get; set; }
        public Authenticationcontext? authenticationContext { get; set; }
        public Usersignupinfo userSignUpInfo { get; set; }
    }

    public class Authenticationcontext
    {
    }

    public class Usersignupinfo
    {
        public Dictionary<string,PrefillAttributeValue> attributes { get; set; }
    }

    public class PrefillAttributeValue
    {
        [JsonProperty("@odata.type")]
        public string odatatype { get; set; }
        public string value { get; set; }
        public string attributeType { get; set; }
    }


}
