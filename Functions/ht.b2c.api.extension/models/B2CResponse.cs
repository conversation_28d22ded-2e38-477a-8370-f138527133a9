﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ht.b2c.api.extension.models
{
    public class B2CResponse
    {
        [JsonProperty("data")]
        public ExtData data { get; set; }
        public B2CResponse()
        {
            data = new ExtData();
        }
    }

    public class ExtData
    {
        [JsonProperty("@odata.type")]
        public string odatatype { get; set; }
        public List<Action> actions { get; set; }
        public ExtData()
        {
            odatatype = "microsoft.graph.onTokenIssuanceStartResponseData";
            actions = new List<Action>();
            actions.Add(new Action());
        }
    }

    public class Action
    {
        [JsonProperty("@odata.type")]
        public string odatatype { get; set; }
        public string? message { get; set; }
        public Claims? claims { get; set; }
        public Dictionary<string,dynamic>? inputs { get; set; }
        public Action()
        {
            odatatype = "microsoft.graph.provideClaimsForToken";
            claims = new Claims();
        }
    }

    

    public class Claims
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CorrelationId { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DateOfBirth { get; set; }
        public string ApiVersion { get; set; }
        public List<string> CustomRoles { get; set; }
        public Claims()
        {
            CustomRoles = new List<string>();
        }
    }
}
