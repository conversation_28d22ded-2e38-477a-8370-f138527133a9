{"dependencies": {"appInsights1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/microsoft.insights/components/ht-app-ai-shared", "type": "appInsights.azure", "connectionId": "APPLICATIONINSIGHTS_CONNECTION_STRING"}, "storage1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/Microsoft.Storage/storageAccounts/rghtauedemo2b5a6", "type": "storage.azure", "connectionId": "AzureWebJobsStorage"}}}