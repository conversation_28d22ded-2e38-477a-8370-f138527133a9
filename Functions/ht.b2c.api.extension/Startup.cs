﻿
using ht.b2c.api.extension.Service;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

[assembly: FunctionsStartup(typeof(ht.b2c.api.extension.Startup))]
namespace ht.b2c.api.extension;


public class Startup : FunctionsStartup
{
    public override void Configure(IFunctionsHostBuilder builder)
    {
        var context = builder.GetContext();
        var services = builder.Services;

        services.Configure<KestrelServerOptions>(opts =>
        {
            opts.Limits.MinRequestBodyDataRate = null;
            opts.Limits.MinResponseDataRate = null;
        });
        DBHelper.dbConnStr = System.Environment.GetEnvironmentVariable("htdb_connStr");

        

    }
}
