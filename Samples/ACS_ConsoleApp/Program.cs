﻿
using Azure.Communication.Identity;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ACS_ConsoleApp
{
    class Program
    {
        static string conStr = "endpoint=https://ht-cs-uat.communication.azure.com/;accesskey=57t6SUj+ENH7IaL3ZzdL8ww09XyoMzyw1y7ADBIbYgydGNr+9oniP3SQ+Xdu6YAIbfKh5a6wdrAkX92hi/8WkQ==";
        static string newTopicId = Guid.NewGuid().ToString();
        static void Main(string[] args)
        {
            CallingServer().GetAwaiter().GetResult();
            MainAsync(args).GetAwaiter().GetResult();
            Console.ReadLine();
        }
        static async Task MainAsync(string[] args)
        {
            
            Console.WriteLine("Hello World!");
            

            
            var conStr = "endpoint=https://ht-cs-uat.communication.azure.com/;accesskey=57t6SUj+ENH7IaL3ZzdL8ww09XyoMzyw1y7ADBIbYgydGNr+9oniP3SQ+Xdu6YAIbfKh5a6wdrAkX92hi/8WkQ==";
            var client = new CommunicationIdentityClient(conStr);
            var uIdent = await client.CreateUserAsync();

            Console.WriteLine($"Generated user with:{uIdent.Value.Id}");

            var tok = await client.GetTokenAsync(uIdent, new List<CommunicationTokenScope> { CommunicationTokenScope.Chat });
            Console.WriteLine($"Generated tok:{tok.Value.Token}");

        }

        static async Task CallingServer()
        {
            var srvClient = new Azure.Communication.CallingServer.CallingServerClient(conStr);
            var srvCall = srvClient.InitializeServerCall(newTopicId);
            
            Console.WriteLine($"Calling Server Setup {newTopicId}");


        }
    }
}
