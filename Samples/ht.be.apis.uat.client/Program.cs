﻿using Microsoft.Identity.Client;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ht.be.apis.uat.client
{
    class Program
    {
        static AuthConfig config = null;
        static void Main(string[] args)
        {
            Console.WriteLine("Hello World!");
            config = AuthConfig.ReadFromJsonFile("appsettings.json");
            var tok = GetTokenAsync().GetAwaiter().GetResult();

            var resp = MakeSecureCall(tok).GetAwaiter().GetResult();    

            //var resp = GetACSUserToken(tok).GetAwaiter().GetResult();

            Console.ReadLine();
        }

        private static async Task<string> MakeSecureCall(string tok)
        {
            var client = new RestClient(config.BaseAddress);
            var rr = new RestRequest("/api/client/securecall",Method.Get)
                .AddHeader("Authorization", "Bearer " + tok);

            var resp = await client.GetAsync<string>(rr, CancellationToken.None);
            Console.WriteLine("****");
            Console.WriteLine("We got :" + resp);
            return (resp);
        }
        private static async Task<string> GetACSUserToken(string tok)
        {
            var client = new RestClient(config.BaseAddress);
            var rr = new RestRequest("/api/acs/getusertoken",Method.Get)
                .AddHeader("Authorization", "Bearer " + tok);

            var resp = await client.GetAsync<string>(rr, CancellationToken.None);
            Console.WriteLine("****");
            Console.WriteLine("We got :" + resp);
            return (resp);
        }

        private static async Task<string> GetTokenAsync()
        {   
            IConfidentialClientApplication app;

            app = ConfidentialClientApplicationBuilder.Create(config.ClientId)
                .WithClientSecret(config.ClientSecret)
                .WithAuthority(new Uri(config.Authority))
                .Build();

            string[] ResourceIds = new string[] { config.ResourceID };

            AuthenticationResult result = null;
            try
            {
                result = await app.AcquireTokenForClient(ResourceIds).ExecuteAsync();
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("Token acquired \n");
                Console.WriteLine($"Expires: " + result.ExpiresOn);
                Console.WriteLine($"Type: " + result.TokenType);
                Console.WriteLine($"Scopes:  {string.Join(",",result.Scopes)}");
                Console.WriteLine($"Metadata:  {JsonConvert.SerializeObject(result.AuthenticationResultMetadata)}");

                Console.WriteLine(result.AccessToken.Substring(0,20));

                Console.ResetColor();
                return (result.AccessToken);
            }
            catch (MsalClientException ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine(ex.Message);
                Console.ResetColor();
                return null;
            }
        }

    }
}
