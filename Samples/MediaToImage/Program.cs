﻿// See https://aka.ms/new-console-template for more information
using FFmpeg.NET;
using NAudio;
using NAudio.Wave;
using NAudio.WaveFormRenderer;

await Xabe.FFmpeg.Downloader.FFmpegDownloader.GetLatestVersion(Xabe.FFmpeg.Downloader.FFmpegVersion.Shared);

Console.WriteLine("Hello, World!");

Console.WriteLine("Converting a 3gp file to WAV");
var baseDir = AppContext.BaseDirectory;
var ffmpegPath = @$"{baseDir}\ffmpeg.exe";
var fileWav = @$"{baseDir}heart.mp3";

await generateGraph(fileWav);



//var file1 = "https://htappuat.blob.core.windows.net/pub/test/vid1.mp4";
var file1 = "https://htappuat.blob.core.windows.net/uploads/testDevice2/209986DC-1616-4D52-B8FB-CEFD4D39415F/72D3A83A-647C-4503-8A4F-39ECAD038C3E/Throat/1654577658232.mp4";
var file1Thumb = $@"{baseDir}\vid1Thumb2.png";

var file2 = @$"{baseDir}\heart.wav";

var res = await GetThumb(file1);

//Console.WriteLine(res?.FileInfo?.FullName);
Console.ReadLine();
return;

async Task<FileResult> GetThumb(string fileUrlIn)
{
    var inStm = (new HttpClient().GetStreamAsync(fileUrlIn)).GetAwaiter().GetResult();
    var inputStream = inStm;
    //var inputFile = new FFmpeg.NET.InputFile(new FileInfo(filein));
    var inputFile = new FFmpeg.NET.StreamInput(inputStream, true);
    var tmpFileOut = System.IO.Path.GetTempFileName() + ".png";
    var outputFile = new FFmpeg.NET.OutputFile(tmpFileOut);
    
    var ffmpeg = new FFmpeg.NET.Engine(ffmpegPath);

    ffmpeg.Error += Ffmpeg_Error;
    // Saves the frame located on the 15th second of the video.
    var options = new FFmpeg.NET.ConversionOptions { Seek = TimeSpan.FromSeconds(2), VideoFps=1 };
    //await ffmpeg.ExecuteAsync($"-i {inputFile.FileInfo.FullName} -ss 00:00:02 -vframes 1 {outputFile.FileInfo.FullName}",default);
    await ffmpeg.GetThumbnailAsync(inputFile, outputFile, options,default).ConfigureAwait(false);
    var info = await ffmpeg.GetMetaDataAsync(inputFile, default);

    return new FileResult { File = outputFile, FileInfo = info };
    
}

async Task<FFmpeg.NET.MediaFile> makeThumb(string filein, string fileout)
{
    var inputStream = File.OpenRead(filein);
    //var inputFile = new FFmpeg.NET.InputFile(new FileInfo(filein));
    var inputFile = new FFmpeg.NET.StreamInput(inputStream, true);
    var outputFile = new FFmpeg.NET.OutputFile(fileout);

    var ffmpeg = new FFmpeg.NET.Engine(ffmpegPath);
    FFmpeg.NET.MetaData md = null;
    ffmpeg.Error += Ffmpeg_Error;
    // Saves the frame located on the 15th second of the video.
    var options = new FFmpeg.NET.ConversionOptions { Seek = TimeSpan.FromSeconds(2), VideoFps = 1 };
    //await ffmpeg.ExecuteAsync($"-i {inputFile.FileInfo.FullName} -ss 00:00:02 -vframes 1 {outputFile.FileInfo.FullName}",default);
    await ffmpeg.GetThumbnailAsync(inputFile, outputFile, options, default).ConfigureAwait(false);

    return outputFile;

}

void Ffmpeg_Error(object? sender, FFmpeg.NET.Events.ConversionErrorEventArgs e)
{
    Console.WriteLine("ERROR: " + e.Exception.Message);
}

async Task<string> generateGraph(string fileWav)
{
    var maxPeakProvider = new MaxPeakProvider();
    var rmsPeakProvider = new RmsPeakProvider(400); // e.g. 200
    var samplingPeakProvider = new SamplingPeakProvider(200); // e.g. 200
    var averagePeakProvider = new AveragePeakProvider(2); // e.g. 4


    var settings = new StandardWaveFormRendererSettings
    {
        Width = 640,
        TopHeight = 64,
        BottomHeight = 2,
        Name = "Position 1 Heart",
        //DecibelScale =  true
        BackgroundColor = System.Drawing.Color.Transparent,
        TopPeakPen = new System.Drawing.Pen(System.Drawing.Color.Coral),
        //PixelsPerPeak=2
        
    };

    var ext = Path.GetExtension(fileWav);
    var tmp = new OutputFile(Path.GetFileNameWithoutExtension(fileWav) + "2" + ext);
    var inFile = new InputFile(fileWav);
    var ffmpeg = new FFmpeg.NET.Engine(ffmpegPath);
    var info = await ffmpeg.GetMetaDataAsync(inFile, default);

    var output = await ffmpeg.ConvertAsync(inFile, tmp,default);


    var stmWav = new Mp3FileReader(output.FileInfo.FullName);
    
    var renderer = new WaveFormRenderer();
    var image = renderer.Render(stmWav, maxPeakProvider, settings);

    var fname = @$"{AppContext.BaseDirectory}\imageWav2.png";
    image?.Save(fname);

    
    return fname;
}


public class FileResult
{
    public  OutputFile File { get; set; }
    public MetaData FileInfo { get; set; }
}
