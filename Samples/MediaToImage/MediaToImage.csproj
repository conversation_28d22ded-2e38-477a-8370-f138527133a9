<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="NAudio" Version="2.2.1" />
    <PackageReference Include="NAudio.WaveFormRenderer" Version="2.0.0" />
    <PackageReference Include="Xabe.FFmpeg.Downloader" Version="5.2.6" />
    <PackageReference Include="xFFmpeg.NET" Version="7.1.3" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Heart.mp3">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="vid1.mp4">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
