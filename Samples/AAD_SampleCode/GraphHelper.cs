﻿using Azure.Identity;

using Microsoft.Graph;
using Microsoft.Graph.Identity.ApiConnectors.Item;
using Microsoft.Graph.Models;
using Microsoft.Graph.Models.ExternalConnectors;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Identity = Microsoft.Graph.Models.Identity;

namespace AAD_SampleCode
{
    public class GraphHelper
    {
        public static string TenantId { get; set; }
        public static string ClientId { get; set; }

        public static string ClientSecret { get; set; }

        public static GraphServiceClient GetGraphServiceClient(string env)
        {
            // var scopes = new[] { "Directory.AccessAsUser.All" };
            var scopes = new[] { $"https://graph.microsoft.com/.default" };
            var options = new TokenCredentialOptions
            {
                AuthorityHost = AzureAuthorityHosts.AzurePublicCloud
            };

            // Granted to the application through here
            // https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/CallAnAPI/appId/0ca16d25-5a36-47a7-a8ca-f2e0a7bb3bdf/defaultBlade/CallAnAPI
            if (env.ToLower().Contains("uat"))
            {
                ClientId = "0ca16d25-5a36-47a7-a8ca-f2e0a7bb3bdf"; //AppId from 
                ClientSecret = "****************************************";
                TenantId = "234832e0-e532-492f-828d-c41642fd98b3";
            }
            else if (env.ToLower().Contains("demo"))
            {
                ClientId = "af8e0ba1-2620-44fa-8bf9-1b70c783b80d"; //AppId from 
                ClientSecret = "****************************************";
                TenantId = "084aafd0-ae39-4683-a0ff-5706e0e3665a";
            }

            /*
            IConfidentialClientApplication confClientApp = ConfidentialClientApplicationBuilder
                .Create(ClientId)
                .WithTenantId(TenantId)
                .WithClientSecret(ClientSecret)
                .Build();

            var authProv = new Microsoft.Graph.Auth.ClientCredentialProvider(confClientApp);
            */
            var authProv = new ClientSecretCredential(TenantId, ClientId, ClientSecret, options);

            //var authCodeCred = new AuthorizationCodeCredential(TenantId, AppId, ClientSecret, "1", options);

            return new GraphServiceClient(authProv);
            //_gsc = new GraphServiceClient(authCodeCred, scopes);
            /*
            _gsc = new GraphServiceClient(
                new DelegateAuthenticationProvider(
                    async (req) =>
                    {
                        //get an access token if required.
                        var authResult = await confClientApp.AcquireTokenForClient(scopes).ExecuteAsync();
                        req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer",authResult.AccessToken);
                    }
                ));
            */
        }



        //NOTE: the UPN format for an AD User within this tenant is:
        // e.g. if we want to sign in with '<EMAIL>'
        // we register as 'UserPrincipalName=mick_gmail.com#EXT#@htuat3.onmicrosoft.com'
        // this is REALLY important.
        public static Task<string> CreateUATUser()
        {
            Console.WriteLine("Creating user in demo...");
            var uname = "";
            var gc = GetGraphServiceClient("demo");
            //User user1 = gc.Users["TestGUID-***********"].GetAsync().Result;
            var user = new User
            {
                AccountEnabled = true,
                DisplayName = "sam remo",
                MailNickname = "samremo",
                Mail = "<EMAIL>",
                UserPrincipalName = "res.sam.remo_healthteams.com.au#EXT#@htdemo.onmicrosoft.com",
                Identities = new List<ObjectIdentity>
    {
                new ObjectIdentity
                {
                    SignInType = "emailAddress",
                    Issuer = "htdemo.onmicrosoft.com",
                    IssuerAssignedId = "<EMAIL>",
                }
                },
                PasswordProfile = new()
                {
                    Password = "Hello.123",
                    ForceChangePasswordNextSignIn = false, //<--- THIS COULD BE BIG
                }


            };
            var result = gc.Users.PostAsync(user).GetAwaiter().GetResult();
            Console.WriteLine("Created.");
            uname = "sam remo";
            return Task.FromResult(uname);
        }

        public static Task<string> InviteUATUser()
        {
            Console.WriteLine("Inviting user in UAT...");
            var uname = "";
            var gc = GetGraphServiceClient("uat");

            var invite = new Invitation
            {
                InvitedUserDisplayName = "Harish INVITED Chann",
                InvitedUserMessageInfo = new()
                {
                    CustomizedMessageBody = "Health Teams really wants you! Come on down.",
                    MessageLanguage = "en-US",
                    CcRecipients = new List<Recipient> {
                        new Recipient { EmailAddress = new() { Address="<EMAIL>", Name = "Madhav"} } //only 1 supported
                     }
                },



                //= "htdemo.onmicrosoft.com",




                InvitedUserType = "Guest",
                InvitedUserEmailAddress = "<EMAIL>",
                SendInvitationMessage = true,
                InviteRedirectUrl = "http://localhost:4200/auth"    //<--- MADHAV for you to set with the man. Maybe a page - Welcome to Health Teams....
            };
            var result = gc.Invitations.PostAsync(invite).GetAwaiter().GetResult();
            Console.WriteLine("Created.");
            uname = "Harish INVITED Chann";
            return Task.FromResult(uname);
        }

        public static async Task<string> ChangePassword()
        {
            string error = string.Empty;


            string[] scopes = { "Directory.AccessAsUser.All", "User.ReadWrite.All", "User.ManageIdentities.All" };

            // Start interactive login session
            //var scopes = new[] {  };

            // Multi-tenant apps can use "common",
            // single-tenant apps must use the tenant ID from the Azure portal
            var tenantId = "common";

            // Value from app registration
            var clientId = "af8e0ba1-2620-44fa-8bf9-1b70c783b80d";

            // using Azure.Identity;
            var options = new TokenCredentialOptions
            {
                AuthorityHost = AzureAuthorityHosts.AzurePublicCloud
            };

            var userName = "<EMAIL>";
            var password = "Nb89GTLf123";

            // https://learn.microsoft.com/dotnet/api/azure.identity.usernamepasswordcredential
            var userNamePasswordCredential = new UsernamePasswordCredential(
                userName, password, tenantId, clientId, options);

            var graphClient = new GraphServiceClient(userNamePasswordCredential, scopes);

            // Set variable (in real solution you should rely on UI to collect passwords in a secure way)
            var currentPassword = "Nb89GTLf123";
            var newPassword = "123$Nb89GTLf$123";
            try
            {
                var user = new Microsoft.Graph.Models.User
                {
                    PasswordPolicies = "DisablePasswordExpiration,DisableStrongPassword",
                    PasswordProfile = new Microsoft.Graph.Models.PasswordProfile
                    {
                        ForceChangePasswordNextSignIn = false,
                        Password = newPassword
                    }
                };


                var result = await graphClient.Users["3ab794c3-1a5d-4786-acf2-2bfc8ca42d4e"].PatchAsync(user);


            }
            catch (Exception ex)
            {
                error = ex.InnerException.ToString();
            }
            return await Task.FromResult("");
        }

    }
}
