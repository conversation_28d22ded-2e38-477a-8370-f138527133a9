﻿using AAD_SampleCode;

public class Program
{
    public static void Main(string[] args)
    {
        Console.WriteLine("Welcome to the User Creation Sample");
        Console.WriteLine("Written just for you Madhav + Akshay");
        Console.WriteLine("\r\n\r\n");

        Console.WriteLine("1 - Create a user in (new) UAT");
        Console.WriteLine("2 - Invite a user in (new) UAT");
        Console.WriteLine("3 - Create a user in DEMO");
        Console.WriteLine("4 - Invite a user in DEMO");
        Console.Write("Select:");
        var res = Console.ReadLine();
        if (res.Contains("1"))
        {
            Console.WriteLine($"User Create - {GraphHelper.CreateUATUser().GetAwaiter().GetResult()}");
        }
        if (res.Contains("2"))
        {
            Console.WriteLine($"User Invite - {GraphHelper.InviteUATUser().GetAwaiter().GetResult()}");
        }
        if (res.Contains("3"))
        {
            Console.WriteLine($"User Create - {GraphHelper.CreateUATUser().GetAwaiter().GetResult()}");
        }
        if(res.Contains("4"))
        {
            Console.WriteLine($"User Create - {GraphHelper.ChangePassword().GetAwaiter().GetResult()}");

            
        }
        Console.WriteLine("\r\n\r\nFinished. Press any key");
        Console.ReadKey();

    }
}