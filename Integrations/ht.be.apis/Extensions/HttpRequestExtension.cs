﻿using ht.data.common.Extensions;
using Microsoft.AspNetCore.Http;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System;
using ht.data.common;
using Microsoft.IdentityModel.Tokens;
using System.Text.RegularExpressions;
using System.Text;

namespace ht.be.apis.Extensions;

public static class HttpRequestExtension
{
    private static readonly string HDR_ExFacilityId = "x-ExFacilityId";

    public static string GetClientToken(this HttpRequest req, string hdrKey = "x-client-tok")
    {
        var hdrVal = req.Headers[hdrKey].FirstOrDefault();
        return (hdrVal);
    }

    public static JwtSecurityToken ExtractClientToken(this HttpRequest req, string hdrKey = "x-client-tok")
    {
        var tok = GetClientToken(req, hdrKey);
        if (string.IsNullOrEmpty(tok))
            throw new NullReferenceException("Client Token not found");

        var jwt = new MyJwtSecurityTokenHandler(); //new JwtSecurityTokenHandler();
        
        if (jwt.CanReadToken(tok))
        {
            var token = jwt.ReadJwtToken(tok);
            return (token);
        }
        else
            throw new SecurityTokenInvalidTypeException("JWT error - cannot read inbound token. Possibly malformed.");

    }

    public static string ExtractClientUserExId(this HttpRequest req, string hdrKey = "x-client-tok")
    {
        var tok = ExtractClientToken(req, hdrKey);
        return tok.Subject;

    }

    public static models.JwtUserAndRoles ExtractClientUserRoles(this HttpRequest req, string hdrKey = "x-client-tok")
    {
        var br = new models.JwtUserAndRoles();
        var tok = ExtractClientToken(req, hdrKey);
        var id = tok.Subject;


        foreach (var c in tok.Claims)
        {
            if (c.Type == "userRoles" || c.Type=="ht_roles")
            {
                string val = c.Value;
                //check if it's base64
                if (Regex.IsMatch(val, @"^[a-zA-Z0-9\+/]*={0,2}$"))
                {
                    val = Encoding.UTF8.GetString(Convert.FromBase64String(val));
                }
                br = HTJsonSerialiser.Deserialise<models.JwtUserAndRoles>(val);
                break;
            }
        }
        return (br);

    }

    public static string GetUserSecurityJson(this HttpRequest req, baseRequest request, bool bThrowOnMissing = false)
    {
        var roles = req.ExtractClientUserRoles();
        if (bThrowOnMissing && (roles == null || String.IsNullOrEmpty(roles.UserExId)))
            throw new MissingFieldException("ERROR: Please supply a valid client token");

        var facId = req.Headers[HDR_ExFacilityId].FirstOrDefault() ?? request?.GetExFacilityId();

        if (bThrowOnMissing && String.IsNullOrEmpty(facId))
            throw new MissingFieldException("ERROR: Please supply a valid ExFacilityId");

        var json = "{\"exFacilityId\":\"" + facId + "\",\"userExId\":\"" + roles.UserExId + "\"}";
        return (json);


    }
}
