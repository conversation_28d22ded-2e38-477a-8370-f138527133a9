﻿using ht.common.backend.shared.models;
using Microsoft.AspNetCore.Components;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace ht.be.apis.Utils
{
    public class ReportManager
    {
        /*
        public async static Task<byte[]> GenerateSSRSReportAsync(ReportOutputTypes repType, string repName, Dictionary<string, string> parms = null)
        {
            try
            {
                ParameterValue[] parameters = null;
                var client = GetClient(parms["rsAddress"], parms["rsUser"], parms["rsPass"]);

                //load report
                var repPath = $"/Mick Badran/ht/{repName}";
                await client.LoadReportAsync(new TrustedUserHeader(), repPath, null);

                if (parms != null)
                {
                    parameters = (from kv in parms.Where(kv => kv.Key.StartsWith("p"))
                                  select new ParameterValue { Name = kv.Key.Substring(1), Value = kv.Value }
                                     ).ToArray();
                    if (parameters != null)
                        await client.SetExecutionParametersAsync(null, null, parameters, "en-us");
                }

                //deviceInfo
                const string deviceInfo = @"<DeviceInfo><Toolbar>False</Toolbar></DeviceInfo>";
                var resp = await client.RenderAsync(new RenderRequest(null, null, repType.ToString(), deviceInfo));

                //spit out the result
                var byteResults = resp.Result;

                return byteResults;
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        */

    }
}
