﻿using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Logging;
using Microsoft.WindowsAzure.Storage.Blob;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.apis.Utils
{
    public class BlobHelper2
    {
        public static string acctName;
        public static string acctKey;
        private string _key;
        private string _acctName;
        private ILogger _log;
        private CloudBlobClient _client;
        public BlobHelper2(string acctName, string key, ILogger log = null)
        {
            this._key = key;
            this._acctName = acctName;
            this._log = log;
        }            

        public bool CheckIfContainerExists(string rootUrl,string parentContainer, string cName)
        {
            var client = new CloudBlobClient(new Uri(rootUrl), new Microsoft.WindowsAzure.Storage.Auth.StorageCredentials(_acctName, _key));
            var root = client.GetRootContainerReference();

            _log.LogInformation(client.GetRootContainerReference().ToString());
            return client.GetContainerReference(cName).CreateIfNotExistsAsync().GetAwaiter().GetResult();
        }

        public async Task<bool> SaveBlob(string url, string sasToken, byte[] bytes, Dictionary<string, string> metadata)
        {
            using (var dstClient = new HttpClient())
            {
                var uri = new Uri(url);
                var dstBlobUrl = url + $"?{sasToken}";
                var srcContentType = GetMimeType(System.IO.Path.GetFileName(uri.AbsolutePath));
                using (var dstReq = new HttpRequestMessage(HttpMethod.Put, dstBlobUrl))
                {
                    dstReq.Headers.Add("x-ms-blob-container-type", srcContentType);
                    dstReq.Headers.Add("x-ms-blob-type", "BlockBlob");
                    dstReq.Headers.Add("x-ms-version", "2020-02-10");

                    //Add parameters as meta data
                    if (metadata?.Count > 0)
                    {
                        foreach (var p in metadata)
                            dstReq.Headers.Add($"x-ms-meta-{p.Key}", p.Value);
                    }
                    var sc = new ByteArrayContent(bytes, 0, bytes.Length);

                    sc.Headers.Remove("Content-Type");
                    sc.Headers.Add("Content-Type", "application/octet-stream");
                    dstReq.Content = sc;

                    var dstResp = await dstClient.SendAsync(dstReq);
                    dstResp.EnsureSuccessStatusCode();
                }
            }
            return true;
        }

        public async Task<byte[]> GetBlob(string url,string sasToken, Dictionary<string,string> metadata)
        {
            using (var client = new HttpClient())
            {
                var req = new HttpRequestMessage(HttpMethod.Get, url);
                req.Headers.Add("x-ms-blob-type", "BlockBlob");
                req.Headers.Add("x-ms-version", "2020-02-10");
                var srcResp = await client.SendAsync(req);
                srcResp.EnsureSuccessStatusCode(); //error is there's a failure.
                var props = new Dictionary<string, string>();
                foreach (var h in srcResp.Headers)
                {
                    if (h.Key.StartsWith("x-ms-meta-"))
                    {
                        var k = h.Key.Replace("x-ms-meta-", "");
                        props[k] = srcResp.Headers.GetValues(h.Key).FirstOrDefault();
                    }
                }
                //work with the content.
                var bytes = await srcResp.Content.ReadAsByteArrayAsync();

                return (bytes);
            }
        }

        public async Task<string> GetSasToken(string url, int minsValidFor)
        {
            var uri = new Uri(url);
            var client = new CloudBlobClient(uri, new Microsoft.WindowsAzure.Storage.Auth.StorageCredentials(_acctName, _key));
            var bRef = await client.GetBlobReferenceFromServerAsync(uri);
            var res =  bRef.GetSharedAccessSignature(new SharedAccessBlobPolicy
            {
                SharedAccessExpiryTime = DateTime.UtcNow.AddMinutes(minsValidFor),
                Permissions = SharedAccessBlobPermissions.Read
            });

            return (res?.Replace("?","" ));
        }

        public static string GetMimeType(string filename)
        {
            var prov = new FileExtensionContentTypeProvider();
            string ct = string.Empty;
            if (!prov.TryGetContentType(filename, out ct))
                ct = "application/octet-stream";
            return (ct);
        }
    }
}
