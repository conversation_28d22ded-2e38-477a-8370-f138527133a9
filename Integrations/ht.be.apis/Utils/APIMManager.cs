﻿using System;
using System.Net.Http;

namespace ht.be.apis.Utils
{
    public class APIMManager
    {
        public static string baseUrl;
        public static string apikey;
        public static HttpClient GetClient()
        {
            var client = new HttpClient { BaseAddress = new Uri(baseUrl) };
            client.DefaultRequestHeaders.Add("x-api-key", apikey);
            return(client);

        }

        
    }
}
