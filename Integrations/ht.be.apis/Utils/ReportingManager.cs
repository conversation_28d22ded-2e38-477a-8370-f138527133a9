﻿using ht.common.backend.shared.models;
using ht.data.common.Billing;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ht.be.apis.Utils
{
    public class ReportingManager
    {
     /*
      * public async Task<byte[]> RunReportAsBytes(ReportOutputTypes repType, string repName, Dictionary<string, string> parms = null)
        {
            parms = AddRsServerDetails(parms);
            var resp = await ReportManager.GenerateSSRSReportAsync(repType, repName, parms);
            return (resp);
        }

        public async Task<byte[]> RunReportAsHtml(string repName, Dictionary<string, string> parms = null)
        {
            var resp = await RunReportAsBytes(ReportOutputTypes.HTML, repName, parms);

            return (resp);
        }

        public async Task<byte[]> RunReportAsPDF(string repName, Dictionary<string, string> parms = null)
        {
            var resp = await RunReportAsBytes(ReportOutputTypes.PDF, repName, parms);
            return (resp);
        }

        public async Task<byte[]> RunReportAsExcel(string repName, Dictionary<string, string> parms = null)
        {
            var resp = await RunReportAsBytes(ReportOutputTypes.EXCEL, repName, parms);
            return (resp);
        }

        public async Task<GetReportResponse> RunReport(GetReportRequest req, Dictionary<string, string> parms = null)
        {
            var resp = new GetReportResponse { ReportType = req.ExportFormat };

            ClearedInternalReport cr = null;
            if (req.ReportId.HasValue)
                cr = JsonConvert.DeserializeObject<ClearedInternalReport>(DBHelper.ExecSprocById("sp_REPORTS_Get_By_Id", req.ReportId.Value));
            else if (!string.IsNullOrEmpty(req.FriendlyName))
            {
                var json = DBHelper.ExecSprocByParams("sp_REPORTS_Get_By_FriendlyName", new Dictionary<string, string> { { "friendlyName", $"{req.FriendlyName}" } });
                json = json.Replace("\"", "'");
                cr = JsonConvert.DeserializeObject<ClearedInternalReport>(json);
            }

            //Work out Parameters
            var newParms = new Dictionary<string, string>();
            foreach (var kv in parms)
                newParms["p" + kv.Key] = kv.Value;

            if (req.ExportFormat == ReportOutputTypes.PDF)
                resp.Data = await RunReportAsPDF(cr.ReportName, newParms);
            else if (req.ExportFormat == ReportOutputTypes.EXCEL)
            {
                resp.Data = await RunReportAsExcel(cr.ReportName, newParms);
                resp.DataAsString = Convert.ToBase64String(resp.Data);
            }
            else
            {
                resp.Data = await RunReportAsHtml(cr.ReportName, newParms);
                resp.DataAsString = Convert.ToBase64String(resp.Data);
            }

            return (resp);
        }

        private Dictionary<string, string> AddRsServerDetails(Dictionary<string, string> parms)
        {
            parms = parms ?? new Dictionary<string, string>();
            parms["rsAddress"] = Config.RsAddress;
            parms["rsUser"] = Config.RsUser;
            parms["rsPass"] = Config.RsPass;
            return (parms);
        }
     */

    }
}
