﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ht.be.apis.Utils
{
    public class Environment
    {
        public static bool IsInAzure
        {
            get
            {
                return ((System.Environment.GetEnvironmentVariable("WEBSITE_HOSTNAME") != null) || 
                        (System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER")=="true"));
            }
        }
    }
}
