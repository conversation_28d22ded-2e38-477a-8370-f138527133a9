﻿using System;
using System.Text.RegularExpressions;

namespace ht.be.apis.Utils.Mhr
{
    public enum IdentifierType
    {
        IHI = 0,
        HPII,
        HPIO,
        CSP
    }

    public static class HealthIdentifierHelper
    {
        public static string StripSpaces(string value)
        {
            return string.IsNullOrEmpty(value) ? "" : value.Replace(" ", "");
        }

        public static string FormatForDisplay(string value)
        {
            return Regex.Replace(StripSpaces(value) ?? "", ".{4}", "$0 ");
        }

        public static bool IsValid(string value, IdentifierType type)
        {
            string identifierPrefix = "";
            switch (type)
            {   
                case IdentifierType.IHI:
                    identifierPrefix = "800360";
                    break;
                case IdentifierType.HPII:
                    identifierPrefix = "800361";
                    break;
                case IdentifierType.HPIO:
                    identifierPrefix = "800362";
                    break;
                case IdentifierType.CSP:
                    identifierPrefix = "800363";
                    break;
                default:
                    break;
            }
            string strippedValue = StripSpaces(value);
            return strippedValue.Length == 16 &&
                   strippedValue.StartsWith(identifierPrefix) &&
                   <PERSON>hnCheck(strippedValue);
        }

        private static bool LuhnCheck(string value)
        {
            int sum = 0;
            bool alternate = false;
            for (int i = value.Length - 1; i >= 0; i--)
            {
                int n = int.Parse(value[i].ToString());
                if (alternate)
                {
                    n *= 2;
                    if (n > 9)
                    {
                        n -= 9;
                    }
                }
                sum += n;
                alternate = !alternate;
            }
            return (sum % 10 == 0);
        }
    }
}
