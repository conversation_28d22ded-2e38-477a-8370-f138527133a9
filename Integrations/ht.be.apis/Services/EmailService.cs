﻿using Microsoft.Graph.Core;
using Microsoft.Identity.Client;
using System.Threading.Tasks;
using System.Linq;
using ht.data.common.Users;
using System.Collections.Generic;
using Azure.Identity;
using Microsoft.Kiota.Abstractions;
using Microsoft.Graph.Models;
using System;
using Microsoft.Graph;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http;
using System.Text.Json;
using System.Text;
using ht.data.common;
using Microsoft.AspNetCore.Http;
using ht.be.apis.Utils;
using ht.data.common.Shared;

namespace ht.be.apis.Services
{
    public class EmailService
    {
        public static string AppId { get; set; }
        public static string TenantId { get; set; }
        public static string ClientSecret { get; set; }
        public static string Issuer { get; set; }
        public string _portalUrl = null;
        public string _apimKey = null;
        public string _apimsendemailurl = null;
        DBService _dbSvc;
        public class Emailtemplate
        {
            public string templateName { get; set; }
            public string HtmlTemplate { get; set; }
            public string Subject { get; set; }
            public string Status { get; set; }
        }
        public EmailService()
        {
            _portalUrl = common.backend.shared.Globals.Properties["PortalUrl"];
            _apimKey = common.backend.shared.Globals.Properties["APIMKey"];
            _apimsendemailurl = common.backend.shared.Globals.Properties["APIMSendEmailURL"];
        }



        public async Task<string> RandomString(int length)
        {
            var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var stringChars = new char[length];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            var finalString = new String(stringChars);
            return finalString;
        }

        public async Task<SharedListItem> GetEmailTemplate(string templateName)
        {
            SharedListItem br = new();
            try
            {
                _dbSvc = new DBService();
                var jsonSecurity = string.Empty;
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_email_template_by_Name", templateName, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<SharedListItem>(res);
            }
            catch (Exception ex)
            {

            }
            return br;
        }

        public async Task<HttpResponseMessage> SendEmail(string htmlTemplate, string email, string subject, string link)
        {
            string Body = string.Empty;

            var body = new
            {
                Content = htmlTemplate,
                Subject = subject,
                To = email,
                Link = link
            };
            var resp = new HttpResponseMessage();
            using (var clnt = new HttpClient())
            {
                clnt.DefaultRequestHeaders.Add("x-api-key", _apimKey);

                resp = await clnt.PostAsync(_apimsendemailurl,
                   new StringContent(JsonSerializer.Serialize(body), Encoding.UTF8, "application/json")
                   );
                resp.EnsureSuccessStatusCode();
            };
            return resp;
        }

    }
}
