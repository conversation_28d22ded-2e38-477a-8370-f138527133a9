﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;

using ht.be.apis.Utils;
using Microsoft.Extensions.Logging;
using Microsoft.ApplicationInsights;
using ht.be.apis.Extensions;
using static ht.be.apis.Services.EmailService;
using ht.data.common.Shared;
using ht.data.common.Users;
using static ht.be.apis.models.LoginResponse;
using ht.data.common.partner;
using ht.data.common.Partner.TheLookOut;
using System.Text.Json.Nodes;
using Newtonsoft.Json;
using System.Text;
using System.Net.Http;
using System.Net.Http.Json;
using Microsoft.Graph.Models;
using System.Net.Http.Headers;
using Microsoft.Extensions.Primitives;
using ht.data.common.Wounds;
using ht.be.apis.models.TLW;

namespace ht.be.apis.Services
{
    public class PartnerService
    {

        DBService _dbSvc;
        ILogger<PartnerService> logger;
        public PartnerService(DBService dbSvc, ILogger<PartnerService> logger)
        {
            _dbSvc = dbSvc;
            this.logger = logger;
        }

        public async Task<List<PartnerIntegrationMapping>> GetFacilityPartnerMapping(string exFacilityId, string residentExId)
        {
            try
            {
                var mapReq = new GetPartnerIntegrationMappingDetailsRequest
                {
                    ExFacilityId = exFacilityId,
                    UserExId = residentExId


                };
                var json = HTJsonSerialiser.Serialise(mapReq);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Facility_Partner_Mapping_Details", json, "");
                var partnerIntegrationMappingDetails = HTJsonSerialiser.Deserialise<List<PartnerIntegrationMapping>>(res);


                return partnerIntegrationMappingDetails;

            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return null;
            }
        }

        public async Task<PartnerIntegrationMapping> GetPartnerByNameFacility(string exFacilityId, string name)
        {
            try
            {

                var parms = new Dictionary<string, string>()
                {
                    { "name",name },
                    {"exFacilityID",exFacilityId }
                };

                // var json = HTJsonSerialiser.Serialise(mapReq);
                var res = await _dbSvc.ExecSprocAsync("dbo.sp_Get_Facility_Partner_Mapping_By_Name", parms);
                var partnerIntegrationMappingDetails = HTJsonSerialiser.Deserialise<List<PartnerIntegrationMapping>>(res);


                return partnerIntegrationMappingDetails.FirstOrDefault();

            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);

                return null;
            }
        }

        public async Task InserStaffToHTC(long? srcId,long ?entityId ,int? ParnterId)
        {
            try
            {

                var parms = new Dictionary<string, string>()
                {
                    { "entityId",entityId.Value.ToString() },
                    {"srcId",srcId.Value.ToString() },
                    {"partnerId",ParnterId.Value.ToString() }
                };
                 await _dbSvc.ExecSprocAsync("dbo.sp_Partner_TLW_Staff_Insert", parms);



            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);

            }
        }
        public async Task<Staffers> SearchTlwStafferByEmail(string email, PartnerIntegrationMapping partner)
        {
            dynamic partnerConfig = JsonConvert.DeserializeObject(partner.SettingsJSON);

            using HttpClient client = new HttpClient { BaseAddress = new Uri($"{partner.IntegrationURL}") };
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", (string)partnerConfig.authorization);

            var data= await client.GetFromJsonAsync<TlwStaffers>($"{partner.PartnerId}/staffers");


            //check email exist
            var tlwStaff = data.data.FirstOrDefault(p => p.profile.email == email);
            return tlwStaff;
        }
        public async Task SendTaskToLookOut(string jsonSecurity, string residentExId, string assigneeId, string exTaskID, List<PartnerIntegrationMapping> partner)
        {

            dynamic user = JsonConvert.DeserializeObject(jsonSecurity);

            var res = await _dbSvc.ExecSprocWithIdAsync("sp_get_task_for_partnerIntegration", exTaskID, jsonSecurity);
            var tasks = HTJsonSerialiser.Deserialise<List<HealthTeamsTask>>(res);

            foreach (var task in tasks)
            {
                var tlwReq = CreateLookOutVitalTaskRequest(task, (string)user.userExId, assigneeId, residentExId, partner);
                if (tlwReq != null)
                {
                    await SendTaskToLookOut((string)user.userExId, tlwReq, partner);
                }
            }
        }


        private string BuildVitalTaskNote(HealthTeamsTask task)
        {
            var vitalTaskNote = new StringBuilder(@$"<div>Vitals to be taken at {task.TaskDateTimeLocal.Value.ToString("dd/MM/yyyy hh:mmtt")}<br/>");
            vitalTaskNote.Append("<br/>");
            if (!string.IsNullOrEmpty(task.TaskDescription))
            {
                vitalTaskNote.Append($"{task.TaskDescription}<br/>");
                vitalTaskNote.Append("<br/>");

            }
            vitalTaskNote.Append($"Please take the following vitals:<br/>");
            task.VitalsToCapture.ForEach(vital => vitalTaskNote.Append($"* {vital}<br/>"));
            vitalTaskNote.Append("<br/>");

            string url = task.AssignedToUser.Role.ToLower() == "nurse"
                        ? "https://portal-uat.healthteams.com.au/nurse/tasklist"
                        : task.AssignedToUser.Role.ToLower() == "doctor"
                        ? "https://portal-uat.healthteams.com.au/doctor/tasklist"
                        : "https://portal-uat.healthteams.com.au/facility/tasklist";

            vitalTaskNote.Append($"<a href='{url}'>{url}</a>");
            vitalTaskNote.Append("<br/>");
            vitalTaskNote.Append("<br/>");
            return vitalTaskNote.ToString();
        }

        private TheLookOutVitalTaskRequest CreateLookOutVitalTaskRequest(HealthTeamsTask task, string userExId, string assigneeId, string residentExId, List<PartnerIntegrationMapping> partner)
        {
            var residentMap = partner.FirstOrDefault(p => p.UserExId == residentExId);
            var staffMap = partner.FirstOrDefault(p => p.UserExId == userExId);
            var assignee = partner.FirstOrDefault(p => p.UserExId == assigneeId);
            if (staffMap is null && assignee is null)
                return null;

            dynamic partnerConfig = JsonConvert.DeserializeObject(staffMap.SettingsJSON);

            return new TheLookOutVitalTaskRequest()
            {
                Data = new VitalTask()
                {
                    Subject_id = residentMap.SourceId,
                    Author_id = staffMap?.SourceId,
                    Due_at = task.TaskDateTimeUtc.Value,
                    Ticket_template_id = (int)partnerConfig.ticketTemplateId,
                    Priority = true,
                    Assignee_ids = new int[] { Convert.ToInt32(assignee.SourceId) },
                    Title = task.TaskName,
                    Subject_type = "client",
                    Note = BuildVitalTaskNote(task)
                }
            };
        }

        private async Task SendTaskToLookOut(string userExId, TheLookOutVitalTaskRequest tlwReq, List<PartnerIntegrationMapping> partner)
        {
            var staffMap = partner.FirstOrDefault(p => p.UserExId == userExId);
            dynamic partnerConfig = JsonConvert.DeserializeObject(staffMap.SettingsJSON);

            using HttpClient client = new HttpClient { BaseAddress = new Uri($"{staffMap.IntegrationURL}") };
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", (string)partnerConfig.authorization);

            await client.PostAsJsonAsync<object>($"{staffMap.PartnerId}/tickets", tlwReq);
        }

        public bool IsPartnerTheLookOut(List<PartnerIntegrationMapping> partnerMappingDetails)
        {
            return partnerMappingDetails.Any(p => p.PartnerName == "TheLookOut");
        }

        public bool IsPartnerMedtech(List<PartnerIntegrationMapping> partnerMappingDetails)
        {
            return partnerMappingDetails.Any(p => p.PartnerName == "Medtech");
        }

        public async Task SendProgressNote(string note, string userExId, string residentExId, List<PartnerIntegrationMapping> partner)
        {

            var progressNote = new StringBuilder();
            progressNote.Append("Progress Notes from Health Teams Connect:<br>");

            progressNote.Append(note);

            var staffMap = partner.FirstOrDefault(p => p.UserExId == userExId);
            var residentMap = GetResidentMap(partner, residentExId);

            if (staffMap is null)
                return;

            dynamic partnerConfig = JsonConvert.DeserializeObject(staffMap.SettingsJSON);

            var req = new TheLookOutProgressNoteRequest()
            {
                Data = new ProgressNoteRequest()
                {
                    Note_type = "progress",
                    Title = $"Progress Notes for {residentMap.EntityFullName}",
                    Creator_id = Convert.ToInt32(staffMap.SourceId),
                    Content = progressNote.ToString()


                }
            };


            using HttpClient client = new HttpClient { BaseAddress = new Uri($"{residentMap.IntegrationURL}") };
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", (string)partnerConfig.authorization);

            var json = new StringContent(HTJsonSerialiser.Serialise(req), Encoding.UTF8, "application/json");
            var res = await client.PostAsync($"{staffMap.PartnerId}/clients/{residentMap.SourceId}/notes", json);
        }

        public async Task<List<TLWProgressNote>> ReadProgressNoteFromTLW(string residentExId, List<PartnerIntegrationMapping> partner)
        {
            try
            {
                var residentMap = GetResidentMap(partner, residentExId);
                dynamic partnerConfig = JsonConvert.DeserializeObject(residentMap.SettingsJSON);

                using HttpClient client = new HttpClient { BaseAddress = new Uri($"{residentMap.IntegrationURL}") };
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", (string)partnerConfig.authorization);

                var res = await client.GetFromJsonAsync<TLWProgressNoteResponse>($"{residentMap.PartnerId}/clients/{residentMap.SourceId}/notes");
                return res.Data.Where(p => p.Note_Type == "progress" && !p.Content.Contains("Health Teams Connect")).ToList();
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        public async Task SendWoundAssessment(Wound wound, string userExId, string residentExId, List<PartnerIntegrationMapping> partner)
        {
            var woundNote = new StringBuilder();
            woundNote.Append("The following wound details have been recorded in Health Teams Connect:<br>");

            if (!string.IsNullOrEmpty(wound.Name))
                woundNote.Append($"Wound Name : {wound.Name}<br>");

            if (!string.IsNullOrEmpty(wound.Type))
                woundNote.Append($"Wound Type : {wound.Type}<br>");

            if (!string.IsNullOrEmpty(wound.Vitalsigns))
                woundNote.Append($"Vital Signs : {wound.Vitalsigns}<br>");

            if (!string.IsNullOrEmpty(wound.WoundPosition))
                woundNote.Append($"Wound Position : {wound.WoundPosition}<br>");

            if (!string.IsNullOrEmpty(wound.Status))
                woundNote.Append($"Status : {wound.Status}<br>");

            if (!string.IsNullOrEmpty(wound.WoundSide))
                woundNote.Append($"Wound Side : {wound.WoundSide}<br>");

            if (wound.FirstNoticeDateUTC is not null)
                woundNote.Append($"First Noticed : {wound.FirstNoticeDateUTC.Value.ToString("dd/MM/yyyy")}<br>");

            woundNote.Append("<br>");

            if (wound.WoundDetails is not null)
            {
                var woundDetail = wound.WoundDetails.FirstOrDefault();
                if (woundDetail.InspectionDate is not null)
                    woundNote.Append($"Inspection Date : {woundDetail.InspectionDate.Value.ToString("dd/MM/yyyy")}<br>");
                if (woundDetail.Measurements is not null)
                    woundNote.Append($"Measurement : {woundDetail.Measurements}<br>");
                if (woundDetail.Depth is not null)
                    woundNote.Append($"Wound Depth : {woundDetail.Depth}<br>");
                if (woundDetail.BedSurface is not null)
                    woundNote.Append($"Bed Surface : {woundDetail.BedSurface}<br>");
                if (woundDetail.ExudateDrainageType is not null)
                    woundNote.Append($"Exudate Drainage Type : {woundDetail.ExudateDrainageType}<br>");
                if (woundDetail.Drainageamount is not null)
                    woundNote.Append($"Drainage Amount : {woundDetail.Drainageamount}<br>");
                if (woundDetail.WoundOdour is not null)
                    woundNote.Append($"Wound Odour : {woundDetail.WoundOdour}<br>");

                if (woundDetail.Woundedgecharacteristics is not null && woundDetail.Woundedgecharacteristics.Count > 0)
                    woundNote.Append($"Wound Edge Characteristics : {string.Join(", ", woundDetail.Woundedgecharacteristics)}<br>");
                if (woundDetail.PeriWoundandSurroundingSkin is not null && woundDetail.PeriWoundandSurroundingSkin.Count > 0)
                    woundNote.Append($"Peri Wound and Surrounding Skin : {string.Join(", ", woundDetail.PeriWoundandSurroundingSkin)}<br>");

                if (woundDetail.Pain is not null)
                    woundNote.Append($"Pain : {woundDetail.Pain}<br>");
                if (woundDetail.Comments is not null)
                    woundNote.Append($"Comments : {woundDetail.Comments}<br>");
                if (woundDetail.Progress is not null)
                    woundNote.Append($"Progress : {woundDetail.Progress}<br>");
                if (woundDetail.ProgressDetails is not null)
                    woundNote.Append($"Progress Details : {woundDetail.ProgressDetails}<br>");

                if (woundDetail.WoundImages.Count > 0)
                {
                    woundNote.Append(woundDetail.WoundImages.Count > 1 ? "<br>Images:<br>" : "<br>Image:<br>");
                }

                foreach (var image in woundDetail.WoundImages)
                {
                    woundNote.Append($"<a href='{image.FileUrl}'>{image.FileName}</a><br>");
                }
            }

            var staffMap = partner.FirstOrDefault(p => p.UserExId == userExId);
            var residentMap = GetResidentMap(partner, residentExId);

            dynamic partnerConfig = JsonConvert.DeserializeObject(staffMap.SettingsJSON);

            var req = new TheLookOutProgressNoteRequest()
            {
                Data = new ProgressNoteRequest()
                {
                    Note_type = "progress",
                    Title = $"Wound Assesment for {residentMap.EntityFullName}",
                    Creator_id = staffMap == null ? 0 : Convert.ToInt32(staffMap.SourceId),
                    Content = woundNote.ToString()
                }
            };

            using HttpClient client = new HttpClient { BaseAddress = new Uri($"{residentMap.IntegrationURL}") };
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", (string)partnerConfig.authorization);

            var json = new StringContent(HTJsonSerialiser.Serialise(req), Encoding.UTF8, "application/json");
            var res = await client.PostAsync($"{staffMap.PartnerId}/clients/{residentMap.SourceId}/notes", json);
        }


        private static PartnerIntegrationMapping GetStaffMap(List<PartnerIntegrationMapping> partner)
        {
            return partner.FirstOrDefault(p => p.EntityType.ToLower() == "nurse");
        }

        private static PartnerIntegrationMapping GetResidentMap(List<PartnerIntegrationMapping> partner, string residentExId)
        {
            return partner.FirstOrDefault(p => p.UserExId == residentExId);
        }
    }
}
