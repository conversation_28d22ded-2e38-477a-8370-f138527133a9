﻿using ht.be.apis.models.Mhr;
using ht.be.apis.Utils;
using ht.be.apis.Utils.Mhr;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models;
using ht.data.common.Users;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using System.Text.Json;
using System.Threading.Tasks;

namespace ht.be.apis.Services
{
    public class MhrService
    {
        // These values are expected by the MHR API - changing them will mean the API calls will not work
        public const string clientId = "HTP00000";
        public const string productName = "HealthTeams Connect";
        public const string productVersion = "V4.2";

        DBService _dbSvc;
        PartnerService _partnerService;
        ILogger _logger;

        public MhrService(DBService dBService, PartnerService partnerService, ILogger<MhrService> logger)
        {
            _dbSvc = dBService;
            _partnerService = partnerService;
            _logger = logger;
        }

        public async Task<MhrPortalResponse> GetMhrPortal(MhrPortalRequest portalRequest)
        {
            var portalResponse = new MhrPortalResponse();

            // Get HPIO and MHR endpoint for this facility.
            // We could read the HPIO out of the certificate: string hpio = (clientCert != null ? clientCert.Subject.Split('.')[1] : "");
            // But MHR rules need the HPIO to be stored in db, and editable by the user
            string hpio = "";
            string mhrEndpoint = "";
            var partner = await _partnerService.GetPartnerByNameFacility(portalRequest.ExFacilityId, Partner.MHR.ToString());
            if (partner != null)
            {
                hpio = partner.ExPartnerMapId;
                mhrEndpoint = partner.IntegrationURL;
            }

            // Verify we have an HPIO
            if (String.IsNullOrEmpty(hpio))
            {
                portalResponse.ErrorMessage = "This facility has not been setup for the My Health Record (HPI-O missing)";
                return portalResponse;
            }

            // Verify we have the URL for the CIStoNPP endpoint
            // test/svt endpoint = "https://services.svt.gw.myhealthrecord.gov.au/CIStoNPP";
            if (String.IsNullOrEmpty(mhrEndpoint))
            {
                portalResponse.ErrorMessage = "This facility has not been setup for the My Health Record (Endpoint not set)";
                return portalResponse;
            }

            // Get the HPIO certificate for the facility
            X509Certificate2 clientCert = GetHPIOCertificate(portalRequest.ExFacilityId);
            if (clientCert == null)
            {
                portalResponse.ErrorMessage = "This facility has not been setup for the My Health Record (Certificate not provided)";
                return portalResponse;
            }

            // Get the current user details
            var jsonSecurity = $"{{\"exFacilityId\":\"{portalRequest.ExFacilityId}\",\"userExId\":\"{portalRequest.UserExId}\"}}";
            HealthTeamsUser practitioner = null;
            try
            {
                var provider = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_User_Details_by_UserexId", portalRequest.UserExId, jsonSecurity);
                if (string.IsNullOrEmpty(provider))
                {
                    _logger.LogError($"GetMhrPortal - Unable to get details for current provider {portalRequest.ExResidentId}");
                    portalResponse.ErrorMessage = "Unable to get provider details";
                    return portalResponse;
                }
                practitioner = HTJsonSerialiser.Deserialise<HealthTeamsUser>(provider);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, "Error when getting provider details for GetMhrPortal.");
                portalResponse.ErrorMessage = "Unable to get provider details";
                return portalResponse;
            }

            // Validate the HPII of the current user
            string hpii = HealthIdentifierHelper.StripSpaces(practitioner.IHI); // "8003616566715309";
            if (!HealthIdentifierHelper.IsValid(hpii, IdentifierType.HPII))
            {
                portalResponse.ErrorMessage = "You have not provided your HPI-I number or it is invalid. This is required for access to the My Health Record";
                return portalResponse;
            }

            // Get the patient details
            HealthTeamsUser patient = null;
            try
            {
                var patientUser = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_User_Details_by_UserexId", portalRequest.ExResidentId, jsonSecurity);
                if (string.IsNullOrEmpty(patientUser))
                {
                    _logger.LogError($"GetMhrPortal - Unable to get details for patient {portalRequest.ExResidentId}");
                    portalResponse.ErrorMessage = "Unable to get patient details";
                    return portalResponse;
                }
                patient = HTJsonSerialiser.Deserialise<HealthTeamsUser>(patientUser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, "Error when getting patient details for GetMhrPortal.");
                portalResponse.ErrorMessage = "Unable to get patient details";
                return portalResponse;
            }

            string ihi = "", medicareNo = "", dva = "";
            // If patient has a valid IHI then we need to use it (a MHR requirement), but this wont usually be the case as we are not doing IHI lookups at this time
            ihi = HealthIdentifierHelper.StripSpaces(patient.IHI);
            if (!HealthIdentifierHelper.IsValid(ihi, IdentifierType.IHI))
            {
                // it is not a valid IHI, so we will move on to Medicare number
                ihi = "";
            }

            // Ensure only one of IHI or MCN or DVA is provided to MHR - this is a requirement
            if (ihi == "")
            {
                medicareNo = patient.MedicareNumber?.Replace(" ", "") ?? "";
                string medicarePosn = patient.MedicarePosNo?.Replace(" ", "") ?? "";
                // For MHR, the Medicare number must inclide the IRN (position) - so needs to be 11 digits long
                // Since we are not validating Medicare numbers, the IRN may already be included in the Medicare number, so we need to check that
                if (medicareNo.Length < 11)
                {
                    medicareNo += medicarePosn;
                }
                _logger.LogDebug($"Medicare Number: {patient.MedicareNumber ?? ""} - Position: {medicarePosn}");
            }
            //string dva = ""; // We dont appear to be storing DVA numbers in the db

            if (String.IsNullOrEmpty(ihi) && String.IsNullOrEmpty(medicareNo) && String.IsNullOrEmpty(dva))
            {
                portalResponse.ErrorMessage = "This person does not have a valid IHI or Medicare number. This is required for access to the My Health Record";
                return portalResponse;
            }

            // Map our free text gender values to specific ones required by MHR
            string mhrGender = "U";
            if (patient.Gender != null)
            {
                switch (patient.Gender.ToUpper())
                {
                    case "F":
                    case "FEMALE":
                        mhrGender = "F";
                        break;
                    case "M":
                    case "MALE":
                        mhrGender = "M";
                        break;
                    case "I":
                    case "INDETERMINATE":
                    case "NON-BINARY":
                    case "NONBINARY":
                        mhrGender = "I";
                        break;
                    default:
                        mhrGender = "U";
                        break;
                }
            }

            // Get DOB in dd-MM-yyyy format
            string dob = patient.DOB.GetValueOrDefault().ToString("dd-MM-yyyy");

            // Remember request details for auditing
            var requestDetails = new
            {
                hpio,
                hpii,
                dob,
                mhrGender,
                patient.LastName,
                ihi,
                medicareNo,
                dva
            };
            DateTime requestTimeStamp = DateTime.UtcNow;

            // Create client
            MhrRestClient client = new MhrRestClient(mhrEndpoint, clientId, clientCert, productName, productVersion);

            try
            {
                //string errorMessage = "";
                //int statusCode = 0; //HTTP Response

                portalResponse = client.GetAccessToNpp(hpio, hpii, dob, mhrGender, patient.LastName, ihi, medicareNo, dva);

                try
                {
                    if (portalResponse.StatusCode != 200)
                    {
                        // See if we have an error message in the portalHtml
                        if (!String.IsNullOrEmpty(portalResponse.PortalHtml) && portalResponse.PortalHtml.Contains("message"))
                        {
                            // Unescape any \u0022 from the HTML string
                            portalResponse.PortalHtml = portalResponse.PortalHtml.Replace("\\u0022", "\"");
                            // Now attempt to parse the string which should be in json format
                            var errorJson = JsonSerializer.Deserialize<Dictionary<string, string>>(portalResponse.PortalHtml);
                            if (errorJson != null && errorJson.ContainsKey("message"))
                            {
                                portalResponse.ErrorMessage = portalResponse.ErrorMessage + " - " + errorJson["message"];
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Error parsing unsuccessful response in MhrService.GetMhrPortal: " + ex.ToString());
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in MhrService.GetMhrPortal: " + ex.ToString());

                // If an error is encountered, look at client.restResponse for detailed description of the error
                var lookAtStatusCode = client.restResponse;
                portalResponse.ErrorMessage = "Error: " + lookAtStatusCode.StatusCode + ". Exception: " + ex.Message + ". ResponseErrorMessage: " + lookAtStatusCode.ErrorMessage;
            }
            finally
            {
                // Log the request and response details to the dedicated audit table
                LogMhrPortalAccess(portalRequest, requestTimeStamp, portalResponse, DateTime.UtcNow);
            }
            return portalResponse;
        }

        public async Task<baseResponse> UpsertHPIODetails(UpsertHPIOCertificateRequest hpioDetails)
        {
            var response = new ht.common.backend.shared.models.baseResponse();
            // Strip any spaces from the HPIO Identifier
            hpioDetails.HPIO = HealthIdentifierHelper.StripSpaces(hpioDetails.HPIO);

            #region Validate the request
            // A HPIO Identifier is required
            if (String.IsNullOrEmpty(hpioDetails.HPIO))
            {
                response.Status = "Error";
                response.Message = "HPIO Identifier must be provided";
                return response;
            }

            // Validate the HPIO Identifier
            if (!HealthIdentifierHelper.IsValid(hpioDetails.HPIO, IdentifierType.HPIO))
            {
                response.Status = "Error";
                response.Message = "The HPIO Identifier is not in a valid format";
                return response;
            }

            // A certificate file is optional, but if provided, a password is required
            if (!String.IsNullOrEmpty(hpioDetails.CertificateFile))
            {
                if (String.IsNullOrEmpty(hpioDetails.CertificatePassword))
                {
                    response.Status = "Error";
                    response.Message = "Certificate password must be provided when uploading a certificate";
                    return response;
                }
                // Attempt to load the certificate file using the password
                // This will throw an exception if the password is incorrect or the file is not in a valid format
                try
                {
                    byte[] certBytes = Convert.FromBase64String(hpioDetails.CertificateFile);
                    var certificate = new X509Certificate2(certBytes, hpioDetails.CertificatePassword);
                }
                catch (Exception)
                {
                    response.Status = "Error";
                    response.Message = "Certificate password is incorrect or the certificate file is not in a valid format";
                    return response;
                }
            }
            #endregion Validate the request

            // Save the HPIO Identifier to the database
            try
            {
                var parms = new Dictionary<string, string>()
                {
                    {"exFacilityId", hpioDetails.ExFacilityId},
                    {"hpio", hpioDetails.HPIO}
                };
                var res = await _dbSvc.ExecSprocAsync("sp_Partner_MHR_HPIO_Upsert", parms);
                response.Status = "Success";
                response.Message = "HPIO Identifier saved successfully";
            }
            catch (Exception ex)
            {
                _logger.LogError("Error saving HPIO in MhrService.UpsertHPIODetails: " + ex.ToString());
                response.Status = "Error";
                response.Message = ex.Message;
                return response;
            }

            // Save the certificate file and password to the key vault
            if (!String.IsNullOrEmpty(hpioDetails.CertificateFile))
            {
                try
                {
                    bool result1 = AzureKeyvaultHelper.SetSecret("mhr-hpio-cert-" + hpioDetails.ExFacilityId, hpioDetails.CertificateFile);
                    bool result2 = AzureKeyvaultHelper.SetSecret("mhr-hpio-pass-" + hpioDetails.ExFacilityId, hpioDetails.CertificatePassword);
                    if (result1 && result2)
                    {
                        response.Status = "Success";
                        response.Message = "HPIO Identifier and certificate saved successfully";
                    }
                    else
                    {
                        response.Status = "Error";
                        response.Message += "Error saving HPIO certificate and password.";
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("Error saving HPIO certificate in MhrService.UpsertHPIODetails: " + ex.ToString());
                    response.Status = "Error";
                    response.Message += "Error saving HPIO certificate.";
                }
            }
            return response;
        }

        public async Task<MhrSetupResponse> GetMhrSetup(string ExFacilityId)
        {
            var response = new MhrSetupResponse();
            try
            {
                // Get HPIO for this facility.
                response.HPIO = "";

                var partner = await _partnerService.GetPartnerByNameFacility(ExFacilityId, Partner.MHR.ToString());
                if (partner != null)
                {
                    response.HPIO = partner.ExPartnerMapId;
                }

                // See if we have a HPIO certificate for the facility
                response.CertificateProvided = false;
                X509Certificate2 clientCert = GetHPIOCertificate(ExFacilityId);
                if (clientCert != null)
                {
                    response.CertificateProvided = true;
                    response.CertificateExpiry = clientCert.NotAfter.ToString("dd/MM/yyyy");
                }

            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting details in MhrService.GetMhrSetup: " + ex.ToString());
                response.Status = "Error";
                response.Message += "Error getting MHR setup details.";
            }

            return response;
        }

        private void LogMhrPortalAccess(MhrPortalRequest portalRequest, DateTime requestTimeStamp, MhrPortalResponse portalResponse, DateTime responseTimeStamp)
        {
            try
            {
                var parms = new Dictionary<string, string>()
                    {
                        {"RequestTimestamp", requestTimeStamp.ToString("yyyy-MM-ddTHH:mm:ss")},
                        {"ResponseTimestamp", responseTimeStamp.ToString("yyyy-MM-ddTHH:mm:ss")},
                        {"Request", JsonSerializer.Serialize(portalRequest)},
                        {"Response", JsonSerializer.Serialize(portalResponse)},
                        {"StatusCode", portalResponse.StatusCode.ToString()},
                        {"ErrorMessage", portalResponse.ErrorMessage},
                        {"ExFacilityId", portalRequest.ExFacilityId},
                        {"ExResidentId", portalRequest.ExResidentId},
                        {"ExUserId", portalRequest.UserExId}
                    };
                var res = _dbSvc.ExecSprocAsync("sp_Insert_AuditMyHealthRecord", parms);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in MhrService.LogMhrPortalAccess: " + ex.ToString());
            }
        }

        private X509Certificate2 GetHPIOCertificate(string facilityId)
        {
            string certString = "";
            string certPassword = "";
            var secrets = new Dictionary<string, string>();
            try
            {
                secrets = AzureKeyvaultHelper.GetSecrets("mhr-");
                if (secrets?.Count > 0)
                {
                    secrets.TryGetValue("hpio-cert-" + facilityId, out certString);
                    secrets.TryGetValue("hpio-pass-" + facilityId, out certPassword);
                }
                if (String.IsNullOrEmpty(certString) || String.IsNullOrEmpty(certPassword))
                {
                    _logger.LogWarning("Not able to get HPIO Certificate and Password for facility: " + facilityId);
                    return null;
                }

                byte[] certBytes = Convert.FromBase64String(certString);
                var certificate = new X509Certificate2(certBytes, certPassword);
                return certificate;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in MhrService.GetHPIOCertificate: " + ex.ToString());
            }
            return null;
        }

    }
}
