﻿using ht.be.apis.Utils;
using ht.data.common.Weather;
using RestSharp;
using System;
using System.Threading.Tasks;

namespace ht.be.apis.Services
{
    public class WeatherService
    {
        public static string Host { get; set; }
        public static string Key { get; set; }
        public static string BaseUrl { get; set; }

        public WeatherService()
        {

        }

        //NOTE: I created this routine as we needed to call this routine from a few places.
        // Originally this code was in the WeatherController but also needed to be called from the TelehealthController
        // Cross Controller calls are doable but not preferred.
        // I abstracted this down into the 'Service' where both controllers can call easily

        public async Task<GetWeatherResponse> FetchAndUpdateWeather(GetWeatherRequest req)
        {
            var _dbSvc = new DBService();
            GetWeatherResponse br = new();
            try
            {
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_WeatherData_Get", json);
                if (!string.IsNullOrEmpty(res))
                    br.Weather = HTJsonSerialiser.Deserialise<HTWeatherInfo>(res);
                if (br.Weather == null)
                {
                    var resp = await FetchWeather(req);
                    if (resp != null)
                    {
                        json = HTJsonSerialiser.Serialise(resp);
                        res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_WeatherData_Upsert", json);
                        if (res != null)
                            br.Weather = HTJsonSerialiser.Deserialise<HTWeatherInfo>(res);

                    }
                }
                br.Status = "Success";
            }
            catch(Exception ex)
            {
                br.FromException(ex);
            }
            return br;
        }

        public async Task<OpenWeatherResponse> FetchWeather(GetWeatherRequest req)
        {
            using (var client = GetClient())
            {
                var rr = new RestRequest($"?appid={WeatherService.Key}&q={req.City},{req.CountryCode}&units=metric&mode=json", Method.Get);
                var resp = await client.GetAsync<OpenWeatherResponse>(rr);
                return(resp);
            }
        }

        public RestClient GetClient()
        {
            return new RestClient(WeatherService.BaseUrl);
                
        }


    }
}
