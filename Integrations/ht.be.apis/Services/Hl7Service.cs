﻿using ht.be.apis.Utils;
using ht.common.backend.shared;
using ht.common.backend.shared.models;
using ht.common.backend.shared.models.hl7;
using ht.data.common;
using ht.data.common.Shared;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using PeterPiper.Hl7.V2.Model;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using static ht.common.backend.shared.models.hl7.OBR;

namespace ht.be.apis.Services
{
    public class Hl7Service
    {

        ISegment prd2 = null;
        private readonly DBService _dbSvc;
        private readonly IConfiguration _conf;
        private HL7Message HL7;
        private string _beasyncUrl = null;
        public Hl7Service(DBService dbSvc, IConfiguration configuration) : base()
        {
            _beasyncUrl = common.backend.shared.Globals.Properties["HtBEAsyncUrl"];
            _dbSvc = dbSvc;
            this._conf = configuration;
        }

        public void Initilize()
        {
            HL7 = new("2.3.1", "REF", "I12", "MSH,RF1,PRD,PID,OBR,OBX");

            var rf1 = HL7.GetHL7Segment("RF1");
            var prd = HL7.GetHL7Segment("PRD");

            prd2 = HL7.AddHL7Segment("PRD");

            var pid = HL7.GetHL7Segment("PID");
            var obr = HL7.GetHL7Segment("OBR");
            var obx = HL7.GetHL7Segment("OBX");
            var msh = HL7.GetHL7Segment("MSH");
            var date = HL7.GetFieldValue("MSH", 7).Split('.')[0] + HL7.GetFieldValue("MSH", 7).Substring(HL7.GetFieldValue("MSH", 7).IndexOf('+'));
            HL7.SetField("MSH", 7, date);
        }

        public async Task<Dictionary<string, string>> GenerateHl7(HealthTeamsUser user, ResidentUser details, Hl7Request request)
        {
            Initilize();


            Dictionary<string,string> hl7Details = new Dictionary<string,string>();
            List<string> hl7Messages = new List<string>();
            request.ReferToProvider = new Hl7Provider()
            {
                FamilyNameLastNamePrefix = user.LastName ?? "Doctor",
                GivenName = user.FirstName ?? "Demo",
                Prefix = "Dr"
            };
            //encoded PDF-Azure func

            foreach (var noteId in request.ExNoteIds)
            {
                request.ReferToProvider = new Hl7Provider()
                {
                    FamilyNameLastNamePrefix = user.LastName ?? "Doctor",
                    GivenName = user.FirstName ?? "Demo",
                    Prefix = "Dr"
                };

              
                request.PdfEncode = await GetPdfEncode(new()
                {
                    ExFacilityId = request.ExFacilityId,
                    ExResidentId= request.ExResidentId,
                    ExNoteIds = new List<string>() { noteId},
                    ReferToProvider = request.ReferToProvider,
                    IsAmended = request.IsAmended
                });

                //set MSH
                GenerateMSH(new MSH()
                {
                    SendingApplication = "Health Teams Connect v1",
                    SendingFacility = Globals.Properties["HealthTeamsEDI"],
                    ReceivingApplication = "HealthLink",
                    ReceivingFacility = user.HealthLinkEDI,

                });
                //RF1
                GenerateRF1(new RF1()
                {
                    ReferralStatus = ReferralStatus.A.ToString(),
                    ReferralPriority = ReferralPriority.R.ToString(),
                    ReferralType = new()
                    {
                        ReferralTypeCode = "NOT",
                        ReferralTypeDescription = "Notification",
                        ReferralTypeCodingSystem = "HL70281"
                    },
                    ReferralDisposition = "UHR",
                    OriginatingReferralIdentifier = (identifier: HL7.GetFieldValue("MSH", 10), sendingSytem: "HealthTeams"),
                    EffectiveDate = HL7.GetFieldValue("MSH", 7)
                });
                //PRD RP
                GeneratePRD(new PRD()
                {
                    //@"Doctor^Demo^^^Dr",
                    ProviderRole = ProviderRole.RP,
                    ProviderName = new()
                    {
                        FamilyNameLastNamePrefix = user.LastName ,
                        GivenName = user.FirstName,
                        Prefix = "Dr"
                    },
                    ProviderIdentifiers = (ID: user.ProviderNumber, IdType: "PROV#")
                    // ProviderIdentifiers = (ID: string.IsNullOrEmpty(details.GP.ProviderNumber) ? "1234567A" : details.GP.ProviderNumber, IdType: "PROV#")
                });
                //PRD RT
                GeneratePRD2(new PRD()
                {
                    //@"Doctor^Demo^^^Dr",
                    ProviderRole = ProviderRole.RT,
                    ProviderName = new()
                    {
                        FamilyNameLastNamePrefix = user.LastName,
                        GivenName = user.FirstName,
                        Prefix = "Dr"
                    },
                    ProviderIdentifiers = (ID: user.ProviderNumber, IdType: "PROV#")

                    // ProviderIdentifiers = (ID: string.IsNullOrEmpty(details.GP.ProviderNumber) ? "0000000Y" : details.GP.ProviderNumber, IdType: "PROV#")
                });
                //PID
                GeneratePID(new PID()
                {
                    SetID = "1",
                    PatientID = details.ExResidentId,
                    DateTimeOfBirth = DateTime.Parse(details.DOB.Value.ToShortDateString()),
                    Sex = details.Gender.ToLower() == "male" ? "M" : details.Gender.ToLower() == "female" ? "F" : "O",
                    PatientIdentifierList = new()
                {
                   new PatientIdentifier()
                   {
                       ID=details.MedicareNumber, //?? "**********",
                       Authority="AUSHIC",
                       IdentifierTypeCode="MC"

                   }
                },
                    PatientNameList = new()
                {
                    new PatientName()
                    {
                        LastName= details.LastName,
                        FirstName=details.FirstName
                        //,MiddleName= "Middle"
                        //, Prefix="Sir"

                    }
                },
                    PatientAddressList = GetPatientAddress(details.Address),
                    PhoneNumberList = GetPatientPhoneNumber(details)
                }); ;
                //obr
                GenerateOBR(new OBR()
                {
                    SetID = "1",
                    FillerOrderNumber = new()
                    {
                        EntityIdentifier = HL7.GetFieldComponentValue("RF1", 6, 1),
                        NameSpaceId = HL7.GetFieldComponentValue("RF1", 6, 2),
                        UniversalId = HL7.GetFieldComponentValue("RF1", 6, 2)
                    },
                    UniversalServiceIDInfo = new()
                    {
                        Text = "Letter - Referral Report"
                    },
                    ObservationDateTime = HL7.GetFieldValue("MSH", 7),
                    OrderingProviders = new()
                {
                    new OBR.Provider()
                    {
                        ID=  user.ProviderNumber,// "1234567A",
                        LastName=user.LastName,
                        FirstName=user.FirstName,
                        Prefix="Dr",
                        Authority="AUSHICPR"
                    }
                },
                    DiagnosticServiceSectorID = "PHY",
                    ResultStatusID = "F",

                    FillerField1 = new List<string> { "DR=0000000Y" },
                    ResultCopiesTo = new List<OBR.Provider> { new OBR.Provider()
                {
                    ID="1234567X",
                    LastName =  "Provider",
                    FirstName = "Test",
                    Prefix="Dr",
                    Authority="AUSHICPR"
                } }

                }, false);
                //obx
                GenerateOBX(new OBX()
                {
                    SetID = "1",
                    ValueType = "ED",
                    ObservationIdentifier = new()
                    {
                        Identifier = "PDF",
                        Text = "Display format in PDF",
                        NameOfCodingSystem = "AUSPDI"
                    },
                    ObservationValue = request.PdfEncode,
                    ObservationResultStatus = "F"
                });
                hl7Messages.Add(HL7.ToString());
                hl7Details.Add(noteId, HL7.ToString());
            }

            return hl7Details;


        }
        public string GenerateHl7CopyTo(ResidentUser details, Hl7Request request)
        {
            Initilize();
            //set MSH
            GenerateMSH(new MSH()
            {
                SendingApplication = "Health Teams Connect v1",
                SendingFacility = "healthte",
                ReceivingApplication = "HealthLink",
                ReceivingFacility = "PMSBESTP",

            });
            //RF1
            GenerateRF1(new RF1()
            {
                ReferralStatus = ReferralStatus.A.ToString(),
                ReferralPriority = ReferralPriority.R.ToString(),
                ReferralType = new()
                {
                    ReferralTypeCode = "NOT",
                    ReferralTypeDescription = "Notification",
                    ReferralTypeCodingSystem = "HL70281"
                },
                ReferralDisposition = "UHR",
                OriginatingReferralIdentifier = (identifier: HL7.GetFieldValue("MSH", 10), sendingSytem: "HealthTeams"),
                EffectiveDate = HL7.GetFieldValue("MSH", 7)
            });
            //PRD RP
            GeneratePRD(new PRD()
            {
                //@"Doctor^Demo^^^Dr",
                ProviderRole = ProviderRole.RP,
                ProviderName = new()
                {
                    FamilyNameLastNamePrefix = details.GP.LastName ?? "Test",
                    GivenName = details.GP.FirstName ?? "Doctor",
                    Prefix = "Dr"
                },
                ProviderIdentifiers = (ID: string.IsNullOrEmpty(details.GP.ProviderNumber) ? "1234567A" : details.GP.ProviderNumber, IdType: "PROV#")
            });
            //PRD RT
            GeneratePRD2(new PRD()
            {
                //@"Doctor^Demo^^^Dr",
                ProviderRole = ProviderRole.RT,
                ProviderName = new()
                {
                    FamilyNameLastNamePrefix = "Provider",
                    GivenName = "Test",
                    Prefix = "Dr"
                },
                ProviderIdentifiers = (ID: "1234567X", IdType: "PROV#")
            });
            //PID
            GeneratePID(new PID()
            {
                SetID = "1",
                PatientID = details.ExResidentId,
                DateTimeOfBirth = DateTime.Parse(details.DOB.Value.ToShortDateString()),
                Sex = details.Gender.ToLower() == "male" ? "M" : details.Gender.ToLower() == "female" ? "F" : "O",
                PatientIdentifierList = new()
                {
                   new PatientIdentifier()
                   {
                       ID=details.MedicareNumber ?? "**********",
                       Authority="AUSHIC",
                       IdentifierTypeCode="MC"

                   }
                },
                PatientNameList = new()
                {
                    new PatientName()
                    {
                        LastName= details.LastName,
                        FirstName=details.FirstName,
                        MiddleName= "Middle",
                        Prefix="Sir"

                    }
                },
                PatientAddressList = GetPatientAddress(details.Address),
                PhoneNumberList = GetPatientPhoneNumber(details)
            }); ;
            //obr
            GenerateOBR(new OBR()
            {
                SetID = "1",
                FillerOrderNumber = new()
                {
                    EntityIdentifier = HL7.GetFieldComponentValue("RF1", 6, 1),
                    NameSpaceId = HL7.GetFieldComponentValue("RF1", 6, 2),
                    UniversalId = HL7.GetFieldComponentValue("RF1", 6, 2)
                },
                UniversalServiceIDInfo = new()
                {
                    Text = "Letter - Referral Report"
                },
                ObservationDateTime = HL7.GetFieldValue("MSH", 7),
                OrderingProviders = new()
                {
                    new OBR.Provider()
                    {
                        ID=  "1234567A",
                        LastName=details.GP.LastName ?? "Test",
                        FirstName=details.GP.FirstName ?? "Doctor",
                        Prefix="Dr",
                        Authority="AUSHICPR"
                    }
                },
                DiagnosticServiceSectorID = "PHY",
                ResultStatusID = "F",
                FillerField1 = new List<string> { "CP=Y", "DR=1234567X" },
                ResultCopiesTo = new List<OBR.Provider> { new OBR.Provider()
                {
                    ID="0000000Y",
                    LastName =  "Doctor",
                    FirstName = "Demo",
                    Prefix="Dr",
                    Authority="AUSHICPR"
                } }

            }, true);
            //obx
            GenerateOBX(new OBX()
            {
                SetID = "1",
                ValueType = "ED",
                ObservationIdentifier = new()
                {
                    Identifier = "PDF",
                    Text = "Display format in PDF",
                    NameOfCodingSystem = "AUSPDI"
                },
                ObservationValue = request.PdfEncode,
                ObservationResultStatus = "F"
            });
            return HL7.ToString();


        }
        public string GenerateHl7Amend(ResidentUser details, Hl7Request request)
        {
            Initilize();
            //set MSH
            GenerateMSH(new MSH()
            {
                SendingApplication = "Health Teams Connect v1",
                SendingFacility = "healthte",
                ReceivingApplication = "HealthLink",
                ReceivingFacility = "pms3medd",

            });
            //RF1
            GenerateRF1(new RF1()
            {
                ReferralStatus = ReferralStatus.A.ToString(),
                ReferralPriority = ReferralPriority.R.ToString(),
                ReferralType = new()
                {
                    ReferralTypeCode = "NOT",
                    ReferralTypeDescription = "Notification",
                    ReferralTypeCodingSystem = "HL70281"
                },
                ReferralDisposition = "UHR",
                OriginatingReferralIdentifier = (identifier: HL7.GetFieldValue("MSH", 10), sendingSytem: "HealthTeams"),
                EffectiveDate = HL7.GetFieldValue("MSH", 7)
            });
            //PRD RP
            GeneratePRD(new PRD()
            {
                //@"Doctor^Demo^^^Dr",
                ProviderRole = ProviderRole.RP,
                ProviderName = new()
                {
                    FamilyNameLastNamePrefix = details.GP.LastName ?? "Test",
                    GivenName = details.GP.FirstName ?? "Doctor",
                    Prefix = "Dr"
                },
                ProviderIdentifiers = (ID: string.IsNullOrEmpty(details.GP.ProviderNumber) ? "1234567A" : details.GP.ProviderNumber, IdType: "PROV#")
            });
            //PRD RT
            GeneratePRD2(new PRD()
            {
                //@"Doctor^Demo^^^Dr",
                ProviderRole = ProviderRole.RT,
                ProviderName = new()
                {
                    FamilyNameLastNamePrefix = "Doctor",
                    GivenName = "Demo",
                    Prefix = "Dr"
                },
                ProviderIdentifiers = (ID: string.IsNullOrEmpty(details.GP.ProviderNumber) ? "0000000Y" : details.GP.ProviderNumber, IdType: "PROV#")
            });
            //PID
            GeneratePID(new PID()
            {
                SetID = "1",
                PatientID = details.ExResidentId,
                DateTimeOfBirth = DateTime.Parse(details.DOB.Value.ToShortDateString()),
                Sex = details.Gender.ToLower() == "male" ? "M" : details.Gender.ToLower() == "female" ? "F" : "O",
                PatientIdentifierList = new()
                {
                   new PatientIdentifier()
                   {
                       ID=details.MedicareNumber ?? "**********",
                       Authority="AUSHIC",
                       IdentifierTypeCode="MC"

                   }
                },
                PatientNameList = new()
                {
                    new PatientName()
                    {
                        LastName= details.LastName,
                        FirstName=details.FirstName,
                        MiddleName= "Middle",
                        Prefix="Sir"

                    }
                },
                PatientAddressList = GetPatientAddress(details.Address),
                PhoneNumberList = GetPatientPhoneNumber(details)
            }); ;
            //obr
            GenerateOBR(new OBR()
            {
                SetID = "1",
                FillerOrderNumber = new()
                {
                    EntityIdentifier = HL7.GetFieldComponentValue("RF1", 6, 1),
                    NameSpaceId = HL7.GetFieldComponentValue("RF1", 6, 2),
                    UniversalId = HL7.GetFieldComponentValue("RF1", 6, 2)
                },
                UniversalServiceIDInfo = new()
                {
                    Text = "Letter - Referral Report"
                },
                ObservationDateTime = HL7.GetFieldValue("MSH", 7),
                OrderingProviders = new()
                {
                    new OBR.Provider()
                    {
                        ID=  "1234567A",
                        LastName=details.GP.LastName ?? "Test",
                        FirstName=details.GP.FirstName ?? "Doctor",
                        Prefix="Dr",
                        Authority="AUSHICPR"
                    }
                },
                DiagnosticServiceSectorID = "PHY",
                ResultStatusID = "C"

            }, false);
            //obx
            GenerateOBX(new OBX()
            {
                SetID = "1",
                ValueType = "ED",
                ObservationIdentifier = new()
                {
                    Identifier = "PDF",
                    Text = "Display format in PDF",
                    NameOfCodingSystem = "AUSPDI"
                },
                ObservationValue = request.PdfEncode,
                ObservationResultStatus = "C"
            });
            return HL7.ToString();


        }


        void GenerateMSH(MSH mSH)
        {
            mSH.SetMSHFields(mSH, HL7);
            mSH.AddMSHComponents(HL7);
            #region
            //HL7.SetField("MSH", 3, mSH.SendingApplication);
            //HL7.SetField("MSH", 4, mSH.SendingFacility);
            //HL7.SetField("MSH", 5, mSH.ReceivingApplication);
            //HL7.SetField("MSH", 6, mSH.ReceivingFacility);
            //HL7.SetField("MSH", 10, GenerateMessageControlId());

            //HL7.AddComponent("MSH", 12, 1, "2.3.1");
            //HL7.AddComponent("MSH", 12, 2, "AUS");
            //HL7.SetField("MSH", 15, "NE");
            //HL7.SetField("MSH", 16, "AL");
            #endregion
        }

        void GenerateRF1(RF1 rf1)
        {
            HL7.SetField("RF1", 1, rf1.ReferralStatus);
            HL7.SetField("RF1", 2, rf1.ReferralPriority);

            HL7.AddComponent("RF1", 3, 1, rf1.ReferralType.ReferralTypeCode);
            HL7.AddComponent("RF1", 3, 2, rf1.ReferralType.ReferralTypeDescription);
            HL7.AddComponent("RF1", 3, 3, rf1.ReferralType.ReferralTypeCodingSystem);

            HL7.SetField("RF1", 4, rf1.ReferralDisposition);

            HL7.AddComponent("RF1", 6, 1, rf1.OriginatingReferralIdentifier.identifier);
            HL7.AddComponent("RF1", 6, 2, rf1.OriginatingReferralIdentifier.sendingSytem);

            HL7.SetField("RF1", 9, rf1.EffectiveDate);


        }

        void GeneratePRD(PRD prd)
        {
            HL7.SetField("PRD", 1, prd.ProviderRole.ToString());

            HL7.AddComponent("PRD", 2, 1, prd?.ProviderName.FamilyNameLastNamePrefix);
            HL7.AddComponent("PRD", 2, 2, prd?.ProviderName.GivenName);
            HL7.AddComponent("PRD", 2, 5, prd?.ProviderName.Prefix);

            HL7.SetField("PRD", 3, prd?.ProviderAddress ?? "");
            HL7.SetField("PRD", 4, prd?.ProviderLocation ?? "");
            HL7.SetField("PRD", 5, prd?.ProviderCommunicationInformation ?? "");
            HL7.SetField("PRD", 6, prd?.PreferredMethodofContact ?? "");


            if (!string.IsNullOrEmpty(prd?.ProviderIdentifiers.ID))
            {

                HL7.AddComponent("PRD", 7, 1, prd?.ProviderIdentifiers.ID);
                HL7.AddComponent("PRD", 7, 2, prd?.ProviderIdentifiers.IdType);
            }


            HL7.SetField("PRD", 8, prd?.EffectiveStartDate ?? "");
            HL7.SetField("PRD", 9, prd?.EffectiveEndDate ?? "");




        }
        void GeneratePRD2(PRD prd)
        {


            prd2.Field(1).AsString = prd.ProviderRole.ToString();

            prd2.Field(2).Component(1).AsString = prd?.ProviderName.FamilyNameLastNamePrefix;
            prd2.Field(2).Component(2).AsString = prd?.ProviderName.GivenName;
            prd2.Field(2).Component(5).AsString = prd?.ProviderName.Prefix;

            HL7.SetField("PRD", 3, prd?.ProviderAddress ?? "");
            HL7.SetField("PRD", 4, prd?.ProviderLocation ?? "");
            HL7.SetField("PRD", 5, prd?.ProviderCommunicationInformation ?? "");
            HL7.SetField("PRD", 6, prd?.PreferredMethodofContact ?? "");

            if (!string.IsNullOrEmpty(prd?.ProviderIdentifiers.ID))
            {
                prd2.Field(7).Component(1).AsString = prd?.ProviderIdentifiers.ID;
                prd2.Field(7).Component(2).AsString = prd?.ProviderIdentifiers.IdType;
            }

            HL7.SetField("PRD", 8, prd?.EffectiveStartDate ?? "");
            HL7.SetField("PRD", 9, prd?.EffectiveEndDate ?? "");

        }

        void GeneratePID(PID pid)
        {
            HL7.SetField("PID", 1, pid.SetID);


            var isMedicare = pid.PatientIdentifierList.FirstOrDefault() != null && !string.IsNullOrEmpty(pid.PatientIdentifierList.FirstOrDefault().ID);
            if (!isMedicare)
            {
                HL7.SetField("PID", 3, @"""""");
            }
            else
            {

                pid.PatientIdentifierList.ForEach(identifier =>
                {
                    HL7.AddComponent("PID", field: 3, component: 1, identifier.ID);
                    HL7.AddComponent("PID", field: 3, component: 4, identifier.Authority);
                    HL7.AddComponent("PID", field: 3, component: 5, identifier.IdentifierTypeCode);
                });
            }
            pid.PatientNameList.ForEach(name =>
            {
                HL7.AddComponent("PID", field: 5, component: 1, name.LastName);
                HL7.AddComponent("PID", field: 5, component: 2, name.FirstName);
                //HL7.AddComponent("PID", field: 5, component: 3, name.MiddleName);
                //HL7.AddComponent("PID", field: 5, component: 5, name.Prefix);

            });


            if (pid.PatientAddressList.Count > 0)
            {
                pid.PatientAddressList.ForEach(address =>
                {
                    HL7.AddComponent("PID", field: 11, component: 1, address.StreetAddress);
                    HL7.AddComponent("PID", field: 11, component: 3, address.City);
                    HL7.AddComponent("PID", field: 11, component: 4, address.State);
                    HL7.AddComponent("PID", field: 11, component: 5, address.PostalCode);
                });
            }


            if (pid.PhoneNumberList.Count > 0)
            {
                pid.PhoneNumberList.ForEach(number =>
                {
                    HL7.AddComponent("PID", field: 13, component: 2, number.TelecommunicationUseCode);
                    HL7.AddComponent("PID", field: 13, component: 3, number.TelecommunicationEquipmentType);
                    if (!string.IsNullOrEmpty(number.CountryCode))
                    {
                        HL7.AddComponent("PID", field: 13, component: 5, RemoveSpecialCharacters(number.CountryCode));
                    }

                    HL7.AddComponent("PID", field: 13, component: 7, !string.IsNullOrEmpty(number.AreaCityCode) ? $"{number.AreaCityCode}{number.TelephoneNumber}" : number.TelephoneNumber);
                });
            }
            HL7.SetField("PID", 7, pid.DateTimeOfBirth.Value.ToString("yyyyMMdd"));
            HL7.SetField("PID", 8, pid.Sex);



        }

        void GenerateOBR(OBR obr, bool isCopyTo = false)
        {

            HL7.SetField("OBR", 1, obr.SetID);
            HL7.AddComponent("OBR", field: 3, component: 1, obr.FillerOrderNumber?.EntityIdentifier);
            HL7.AddComponent("OBR", field: 3, component: 2, obr.FillerOrderNumber?.NameSpaceId);
            HL7.AddComponent("OBR", field: 3, component: 3, obr.FillerOrderNumber?.UniversalId);
            HL7.AddComponent("OBR", field: 4, component: 2, obr.UniversalServiceIDInfo.Text);
            HL7.SetField("OBR", 7, $"{obr.ObservationDateTime}");
            HL7.SetField("OBR", 6, $"{obr.ObservationDateTime}");

            var isProviderN = obr.OrderingProviders.FirstOrDefault() != null && !string.IsNullOrEmpty(obr.OrderingProviders.FirstOrDefault().ID);
            if (!isProviderN)
            {
                HL7.AddComponent("OBR", field: 16, component: 2, obr.OrderingProviders.FirstOrDefault().LastName);
                HL7.AddComponent("OBR", field: 16, component: 3, obr.OrderingProviders.FirstOrDefault().FirstName);
                HL7.AddComponent("OBR", field: 16, component: 6, obr.OrderingProviders.FirstOrDefault().Prefix);
            }
            else
            {
                obr.OrderingProviders.ForEach(provider =>
                {
                    if (!string.IsNullOrEmpty(provider.ID))
                        HL7.AddComponent("OBR", field: 16, component: 1, provider.ID);

                    HL7.AddComponent("OBR", field: 16, component: 2, provider.LastName);
                    HL7.AddComponent("OBR", field: 16, component: 3, provider.FirstName);
                    // HL7.AddComponent("OBR", field: 16, component: 6, provider.Prefix);
                    HL7.AddComponent("OBR", field: 16, component: 9, provider.Authority);
                });
            }

            if (isCopyTo && obr.FillerField1.Count > 1)
            {
                HL7.SetField("OBR", 20, String.Join(",", obr.FillerField1.ToArray()));
                obr.ResultCopiesTo.ForEach(provider =>
                {
                    if (!string.IsNullOrEmpty(provider.ID))
                        HL7.AddComponent("OBR", field: 28, component: 1, provider.ID);

                    HL7.AddComponent("OBR", field: 28, component: 2, provider.LastName);
                    HL7.AddComponent("OBR", field: 28, component: 3, provider.FirstName);
                    HL7.AddComponent("OBR", field: 28, component: 6, provider.Prefix);
                    HL7.AddComponent("OBR", field: 28, component: 9, provider.Authority);
                });
            }


            HL7.SetField("OBR", 24, obr.DiagnosticServiceSectorID);
            HL7.SetField("OBR", 25, obr.ResultStatusID);

            HL7.AddComponent("OBR", 27, 4, $"{HL7.GetFieldValue("OBR", 6)}");
        }

        void GenerateOBX(OBX obx)
        {
            HL7.SetField("OBX", 1, obx.SetID);
            HL7.SetField("OBX", 2, obx.ValueType);
            HL7.AddComponent("OBX", 3, 1, obx.ObservationIdentifier.Identifier);
            HL7.AddComponent("OBX", 3, 2, obx.ObservationIdentifier.Text);
            HL7.AddComponent("OBX", 3, 3, obx.ObservationIdentifier.NameOfCodingSystem);

            HL7.AddComponent("OBX", 5, 2, "TX");
            HL7.AddComponent("OBX", 5, 3, "PDF");
            HL7.AddComponent("OBX", 5, 4, "Base64");
            HL7.AddComponent("OBX", 5, 5, obx.ObservationValue);
            HL7.SetField("OBX", 11, obx.ObservationResultStatus);

            HL7.SetField("OBX", 14, $"{HL7.GetFieldValue("OBR", 6)}");

        }

        async Task<string> GetPdfEncode(Hl7Request req)
        {
            var beUrl = $"{_beasyncUrl}/api/GenerateHl7Report";
            byte[] pdfResponse = null;
            try
            {

                if (req.ExNoteIds.Count == 0 && !req.IsAllProgressNotes)
                    throw new Exception("No Noteid found");

                using (var hc = new HttpClient())
                {
                    var json = HTJsonSerialiser.Serialise(req);

                    hc.DefaultRequestHeaders.Add("x-functions-key", _conf["function-key"]);
                    var resp = await hc.PostAsync(beUrl, new StringContent(JsonSerializer.Serialize(req), Encoding.UTF8, "application/json"));
                    resp.EnsureSuccessStatusCode();
                    pdfResponse = await resp.Content.ReadAsByteArrayAsync();
                    return Convert.ToBase64String(pdfResponse);
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        List<PatientAddress> GetPatientAddress(Address address)
        {


            if (address != null)
            {
                return new()
                {
                    new()
                    {
                        StreetAddress=address?.Street1,
                        City=address?.City,
                        State=address?.State,
                        PostalCode=address?.PostCode
                    }
                };
            }
            else
            {
                return new()
                {
                    new()
                        {

                            StreetAddress = "1 Testing Street",
                            City = "Woonona",
                            State = "NSW",
                            PostalCode = "2517"
                     }
                };
            }

        }

        List<PhoneNumber> GetPatientPhoneNumber(ResidentUser resident)
        {
            //new()
            //{
            //    TelecommunicationUseCode = "PRN",
            //    TelecommunicationEquipmentType = "CP",
            //    AreaCityCode = "04",
            //    TelephoneNumber = "88888888"
            //}
            if (!string.IsNullOrEmpty(resident.Mobile))
            {
                return new()
                {
                    new()
                    {
                        TelecommunicationUseCode="PRN",
                        TelecommunicationEquipmentType="CP",
                        CountryCode=resident.MobileCC,
                        AreaCityCode="04" ,
                        TelephoneNumber=resident.Mobile
                    }
                };
            }
            return new List<PhoneNumber>();

        }

        public async Task SendFiletoHealthLink(string hl7Message,string noteId, Hl7Request req, string name)
        {
            try
            {


                using var form = new MultipartFormDataContent();
                form.Add(new StringContent(hl7Message), "file");
                form.Add(new StringContent($"{name}.hl7"), "name");

                //Sent through Logic Apps
                using var httpClient = new HttpClient();
                var response = await httpClient.PostAsync(_conf["hl7-http"], form);
                response.EnsureSuccessStatusCode();

                #region save to db
                var message = Creator.Message(hl7Message);

                var hl7Response = new
                {
                    ExFacilityId = req.ExFacilityId,
                    ExResidentId = req.ExResidentId,
                    MessageContent = message.ToString(),
                    MessageID = message.MessageControlID,
                    MessageTimestamp = message.MessageCreationDateTime.UtcDateTime,
                    MessageType = $"{message.MessageType}-{message.MessageTrigger}",
                    SendingApplication = message.Segment("MSH").Field(3).AsStringRaw,
                    SendingEDI = message.Segment("MSH").Field(4).AsStringRaw,
                    ReceivingApplication = message.Segment("MSH").Field(5).AsStringRaw,
                    ReceivingEDI = message.Segment("MSH").Field(6).AsStringRaw,
                    ExNoteIds = req.ExNoteIds,
                    NoteId =noteId,
                    IsAllProgressNotes = req.IsAllProgressNotes,
                    Status = "Sent"
                };

                var json = HTJsonSerialiser.Serialise(hl7Response);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.upsertHL7Message", json);
                #endregion

                #region FTP
                //using (var ftpClient = new FtpClient(Hostname, new NetworkCredential(username, password)))
                //{
                //const string Hostname = "************";
                //const string username = "adminuser";
                //const string password = "HealthTeams@12345";
                //const int Port = 21;
                //    ftpClient.Connect();

                //    if (ftpClient.IsConnected)
                //    { // Convert the HL7 message to bytes
                //        byte[] hl7Bytes = Encoding.UTF8.GetBytes(hl7Message);
                //        using MemoryStream memoryStream = new MemoryStream(hl7Bytes);
                //        await ftpClient.UploadAsync(memoryStream, $"ht-{residentID}.hl7");
                //    }
                //    else
                //    {
                //        throw new Exception("Message sent failed");
                //    }
                //}
                #endregion
            }
            catch (Exception ex)
            {
                throw new Exception($"Error occurred while sending file to health link: {ex.Message}");
            }
        }

        string RemoveSpecialCharacters(string str)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < str.Length; i++)
            {
                if ((str[i] >= '0' && str[i] <= '9')
                    || (str[i] >= 'A' && str[i] <= 'z'
                        || (str[i] == '.' || str[i] == '_')))
                {
                    sb.Append(str[i]);
                }
            }

            return sb.ToString();
        }
    }
}
