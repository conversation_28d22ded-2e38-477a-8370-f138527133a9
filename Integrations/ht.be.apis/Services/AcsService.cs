﻿using Azure.Communication;
using Azure.Communication.CallingServer;
using Azure.Communication.Identity;
using ht.common.backend.shared.models.sys;

using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using ht.be.apis.models;
using Azure.Communication.Chat;
using ht.data.common.Telehealth;
using ht.be.apis.Utils;
using ht.data.common.Users;

namespace ht.be.apis.Services
{
    public class AcsService
    {
        public static string AcsConnStr { get; set; }
        public static string AcsEndpointUrl { get; set; }
        public static string AcsNotificationUrl { get; set; }

        private const string CallRecodingActiveErrorCode = "8553";
        private const string CallRecodingActiveError = "Recording is already in progress, one recording can be active at one time.";
        private const RecordingChannel CallRecordingChannel = RecordingChannel.Mixed;
        private const RecordingFormat CallRecordingFormat = RecordingFormat.Mp4;
        private const RecordingContent CallRecordingContent = RecordingContent.AudioVideo;

        public AcsService()
        {

        }
        public AcsService(string connStr)
        {

        }



        public async Task<GetUserTokenResponse> GetUserTokenAsync(string acsUserId)
        {
            var resp = new GetUserTokenResponse();

            var client = new CommunicationIdentityClient(AcsConnStr);
            CommunicationUserIdentifier uIdent = new CommunicationUserIdentifier(acsUserId);
            resp.UserId = uIdent.Id;

            Console.WriteLine($"Generated user with:{uIdent.Id}");

            var tok = await client.GetTokenAsync(uIdent, new List<CommunicationTokenScope> { CommunicationTokenScope.Chat, CommunicationTokenScope.VoIP });
            resp.Token = tok.Value.Token;
            resp.TokenExpiresUtc = tok.Value.ExpiresOn.UtcDateTime;
            return (resp);
        }
        public async Task<GetUserTokenResponse> GetUserTokenAsync()
        {
            var resp = new GetUserTokenResponse();

            var client = new CommunicationIdentityClient(AcsConnStr);
            var uIdent = await client.CreateUserAsync();
            resp.UserId = uIdent.Value.Id;

            Console.WriteLine($"Generated user with:{uIdent.Value.Id}");

            var tok = await client.GetTokenAsync(uIdent, new List<CommunicationTokenScope> { CommunicationTokenScope.Chat, CommunicationTokenScope.VoIP });
            resp.Token = tok.Value.Token;
            resp.TokenExpiresUtc = tok.Value.ExpiresOn.UtcDateTime;
            return (resp);
        }

        public async Task<CallConnection> CreateCall(CreateHTCallOptions opts)
        {
            var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);

            var md = new List<MediaType> { { MediaType.Audio }, { MediaType.Video } };
            var subEvents = new List<EventSubscriptionType> {
                { EventSubscriptionType.DtmfReceived },
                { EventSubscriptionType.ParticipantsUpdated}
            };

            var srcParty = new CommunicationUserIdentifier(opts.SourceParty.AcsUserId);
            var parties = (from l in opts.Parties
                           select new CommunicationUserIdentifier(l.AcsUserId)).ToList();

            var options = new CreateCallOptions(new Uri(opts.NotifcationCallbackUri),
               md, subEvents);
            var res = await client.CreateCallConnectionAsync(srcParty, parties, options);

            return (res.Value);
        }

        public async Task<CallConnection> JoinCall(CreateHTCallOptions opts)
        {
            var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);

            var md = new List<MediaType> { { MediaType.Audio }, { MediaType.Video } };
            var subEvents = new List<EventSubscriptionType> {
                { EventSubscriptionType.DtmfReceived },
                { EventSubscriptionType.ParticipantsUpdated}
            };

            var srcParty = new CommunicationUserIdentifier(opts.SourceParty.AcsUserId);

            var options = new JoinCallOptions(new Uri(opts.NotifcationCallbackUri), md, subEvents);
            var res = await client.JoinCallAsync(opts.acsCallId, srcParty, options);

            return (res.Value);
        }



        public async Task<CallConnection> GetCallConnection(string acsCallId)
        {
            try
            {
                var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);
                var res = client.GetCallConnection(acsCallId);

                return (res);
            }
            catch (Exception ex)
            {
                return (null);
            }
        }


        public async Task RemoveUser(string acsUserId)
        {
            var client = new CommunicationIdentityClient(AcsConnStr);
            var resp = client.DeleteUser(new CommunicationUserIdentifier(acsUserId));
        }


        public async Task<string> EndMeetingAsync(string acsCallId)
        {
            try
            {
                var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);


                var recordingId = "";
                return (recordingId);
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains(CallRecodingActiveErrorCode))
                    throw new NotSupportedException(CallRecodingActiveError);
                throw;
            }
        }

        #region Chat Details
        public async Task<string> CreateAndStoreNewChatThreadId(string topic, string exTaskId, DBService _dbSvc)
        {
            var tok = await GetServiceUserIdAcsToken(_dbSvc);
            //using the Service User acs token.
            var tId = await GetNewChatThreadId(topic, tok);
            if (!string.IsNullOrEmpty(tId))
            {
                var json = "{ \"exTaskId\": \"" + exTaskId + "\", \"acsChatThreadId\":\"" + tId + "\"}";
                await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Meeting_AcsChat_Update", json);
            }
            return tId;
        }

        public async Task<string> GetNewChatThreadId(string topic, string acsAccessToken)
        {
            var uri = new Uri(AcsService.AcsEndpointUrl);
            var tok = new CommunicationTokenCredential(acsAccessToken);

            var cClient = new Azure.Communication.Chat.ChatClient(uri, tok);
            var t = cClient.CreateChatThread(topic, new List<Azure.Communication.Chat.ChatParticipant>());
            return t?.Value.ChatThread?.Id;
        }

        public async Task<string> CreateOnetoOneChatThreadId(string topic, string acsAccessToken, List<Azure.Communication.Chat.ChatParticipant> lstParticipant)
        {
            var uri = new Uri(AcsService.AcsEndpointUrl);
            var tok = new CommunicationTokenCredential(acsAccessToken);

            var cClient = new Azure.Communication.Chat.ChatClient(uri, tok);
            var t = cClient.CreateChatThread(topic, lstParticipant);
            return t?.Value.ChatThread?.Id;
        }

        public async Task<List<ChatParticipant>> ListAllChatParticipants(string threadId, DBService _dbSvc)
        {
            var acsAccessToken = await this.GetServiceUserIdAcsToken(_dbSvc);
            var uri = new Uri(AcsService.AcsEndpointUrl);
            var tok = new CommunicationTokenCredential(acsAccessToken);
            var cClient = new Azure.Communication.Chat.ChatClient(uri, tok);

            //Check to see if they are already in the chat.
            var chat = cClient.GetChatThreadClient(threadId);
            return chat.GetParticipants().ToList();
        }

        public async Task<bool> RemoveAllChatParticipants(string threadId, DBService _dbSvc)
        {
            var acsAccessToken = await this.GetServiceUserIdAcsToken(_dbSvc);
            var uri = new Uri(AcsService.AcsEndpointUrl);
            var tok = new CommunicationTokenCredential(acsAccessToken);
            var cClient = new Azure.Communication.Chat.ChatClient(uri, tok);

            //Check to see if they are already in the chat.
            var chat = cClient.GetChatThreadClient(threadId);
            var lst = chat.GetParticipants();
            try
            {
                foreach (var p in lst)
                    chat.RemoveParticipant(p.User);
                return (true);
            }
            catch
            {
                return (false);
            }
        }

        public async Task<bool> CloseAllChatsForServiceUser(DBService _dbSvc)
        {
            var acsAccessToken = await this.GetServiceUserIdAcsToken(_dbSvc);
            var uri = new Uri(AcsService.AcsEndpointUrl);
            var tok = new CommunicationTokenCredential(acsAccessToken);
            var cClient = new Azure.Communication.Chat.ChatClient(uri, tok);

            var chats = cClient.GetChatThreads();
            foreach (var chat in chats)
            {
                cClient.DeleteChatThread(chat.Id);
            }

            return (true);
        }

        public async Task<bool> JoinChatThreadId(string threadId, string participantAcsUid, DBService _dbSvc, string displayName = "")
        {
            var uri = new Uri(AcsService.AcsEndpointUrl);
            var acsAccessToken = await this.GetServiceUserIdAcsToken(_dbSvc);
            var tok = new CommunicationTokenCredential(acsAccessToken);
            var cClient = new Azure.Communication.Chat.ChatClient(uri, tok);

            var newUser = new CommunicationUserIdentifier(participantAcsUid);
            var person = new ChatParticipant(newUser)
            { DisplayName = displayName };

            //Check to see if they are already in the chat.
            var chat = cClient.GetChatThreadClient(threadId);
            var lstParticipants = chat.GetParticipants()?.ToList();
            var participant = from p in lstParticipants
                              where p.User.ToString() == person.User.ToString()
                              select p;

            if (participant?.Count() > 0)
            {
                return true;
            }
            else
            {
                var resp = await chat.AddParticipantAsync(person);
                return (!resp.IsError);
            }


        }

        public async Task<GetUserTokenResponse> GetUserIdAsync()
        {
            var resp = new GetUserTokenResponse();

            var client = new CommunicationIdentityClient(AcsConnStr);
            var uIdent = await client.CreateUserAsync();
            resp.UserId = uIdent.Value.Id;

            return (resp);
        }

        #endregion

        #region Recording Details

        public async Task<string> StartRecordingAsync(string acsCallId)
        {
            try
            {
                var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);
                var uri = new Uri(AcsNotificationUrl);
                var resp = await client.InitializeServerCall(acsCallId).StartRecordingAsync(uri, CallRecordingContent, CallRecordingChannel, CallRecordingFormat).ConfigureAwait(false);
                var recordingId = resp.Value?.RecordingId;
                return (recordingId);
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains(CallRecodingActiveErrorCode))
                    throw new NotSupportedException(CallRecodingActiveError);
                throw;
            }
        }

        public async Task PauseRecordingAsync(string serverCallID, string acsRecordingId)
        {
            try
            {
                var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);
                var uri = new Uri(AcsNotificationUrl);
                var resp = await client.InitializeServerCall(serverCallID).PauseRecordingAsync(acsRecordingId);

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task ResumeRecordingAsync(string serverCallID, string acsRecordingId)
        {
            try
            {
                var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);
                var uri = new Uri(AcsNotificationUrl);
                var resp = await client.InitializeServerCall(serverCallID).ResumeRecordingAsync(acsRecordingId);

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task StopRecordingAsync(string serverCallID, string acsRecordingId)
        {
            try
            {
                var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);
                var uri = new Uri(AcsNotificationUrl);
                var resp = await client.InitializeServerCall(serverCallID).StopRecordingAsync(acsRecordingId);

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<CallRecordingState> GetRecordingStateAsync(string serverCallID, string acsRecordingId)
        {
            try
            {
                var client = new Azure.Communication.CallingServer.CallingServerClient(AcsConnStr);
                var uri = new Uri(AcsNotificationUrl);
                var resp = await client.InitializeServerCall(serverCallID).GetRecordingStateAsync(acsRecordingId);
                return (resp.Value.RecordingState);

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        #endregion


        #region ACS System Helper Functions
        public async Task<MeetingUser> GetUserAndStoreAcsId(string UserExId, DBService _dbSvc)

        {
            MeetingUser br = new();
            try
            {
                string acsUid = string.Empty;
                var resp = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_User_AcsDetails_by_UserexId", UserExId);
                br = HTJsonSerialiser.Deserialise<MeetingUser>(resp);

                if (string.IsNullOrEmpty(br?.AcsUserId))
                {
                    var r = await this.GetUserIdAsync();
                    var json = "{\"userExId\":\"" + UserExId + "\",\"userAcsId\":\"" + r.UserId + "\",\"acsToken\":\"" + r.Token + "\",\"acsTokenExpiresUtc\":\"" + r.TokenExpiresUtc + "\"}";
                    var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_User_Upsert_AcsDetails", json);
                    br = HTJsonSerialiser.Deserialise<MeetingUser>(res);
                }


            }
            catch (Exception ex)
            {
                br.DisplayName = $"ERROR: {ex.Message} {ex.StackTrace}";
            }
            return br;
        }

        public async Task<string> GetServiceUserIdAcsToken(DBService _dbSvc)

        {
            MeetingUser br = new();
            try
            {
                var acsUid = "";
                var uid = common.backend.shared.Globals.Properties["HtServiceUserId"];

                var resp = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_User_AcsDetails_by_UserexId", uid);
                br = HTJsonSerialiser.Deserialise<MeetingUser>(resp);

                if (string.IsNullOrEmpty(br?.AcsUserId))
                {
                    var r = await this.GetUserIdAsync();

                    var result = await GetUserAndStoreAcsId(uid, _dbSvc);
                    acsUid = result?.AcsToken;
                }

                else
                {
                    acsUid = br?.AcsUserId;
                }

                return (await this.GetUserTokenAsync(acsUid))?.Token;
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        public async Task<string> AcquireAcsUserIdForUser(string userExId, DBService _dbSvc)
        {
            MeetingUser br = new();
            try
            {
                var resp = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_User_AcsDetails_by_UserexId", userExId);
                br = HTJsonSerialiser.Deserialise<MeetingUser>(resp);

                if (string.IsNullOrEmpty(br?.AcsUserId))
                {
                    br.AcsUserId = (await this.GetUserIdAsync()).UserId;
                    var str = "{\"userExId\":\"" + userExId + "\", \"userAcsId\":\"" + br.AcsUserId + "\"}";
                    await _dbSvc.ExecSprocWithJsonAsync("[dbo].[sp_User_Upsert_AcsDetails]", str);
                }
                return br.AcsUserId;
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        #endregion

    }
}
