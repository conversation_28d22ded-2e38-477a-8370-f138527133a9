﻿using Hl7.Fhir.Model;
using Hl7.Fhir.Rest;
using ht.be.apis.Utils;
using ht.common.backend.shared.helpers;
using ht.data.common.partner;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using ht.data.common.Wounds;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.apis.Services
{
    public class MedtechIntegrationService
    {
        private DBService _dbSvc;
        private ILogger _logger;
        private PartnerService _partnerService;
        private FhirClient fhirClient;

        public MedtechIntegrationService(DBService dbSvc, ILogger logger)
        {
            _dbSvc = dbSvc;
            _logger = logger;
        }

        public MedtechIntegrationService(DBService dbSvc, ILogger logger, PartnerService partnerService)
        {
            _dbSvc = dbSvc;
            _logger = logger;
            _partnerService = partnerService;
        }

        public async Task<string> GetMedtechAccessToken(PartnerIntegrationMapping partner)
        {
            // Medtech uses microsoft authentication. The Microsoft.Identity.Client library handles the renewal of the token as needed.
            try
            {
                var client_id = partner.UserName; //"74b8c8fb-42e6-4545-a342-cd5b7363817f";
                var client_secret = partner.Password; // "****************************************";
                // Get the auth endpoint and scope from SettingsJSON
                string endpoint = "";
                string auth_scope = "";
                dynamic partnerSettings = JsonConvert.DeserializeObject(partner.SettingsJSON);
                if (partnerSettings != null)
                {
                    endpoint = partnerSettings.auth_endpoint; //"https://login.microsoftonline.com/8a024e99-aba3-4b25-b875-28b0c0ca6096/oauth2/v2.0/token";
                    auth_scope = partnerSettings.auth_scope; //"api://bf7945a6-e812-4121-898a-76fea7c13f4d/.default";
                }
                var scope = new string[] { auth_scope };

                // Setup MSAL
                var client = ConfidentialClientApplicationBuilder
                    .Create(client_id)
                    .WithAuthority(endpoint)
                    .WithClientSecret(client_secret)
                    .Build();

                // Cache the token
                client.AddInMemoryTokenCache();

                // Retrieve an access token
                var authResult = await client.AcquireTokenForClient(scope).ExecuteAsync();

                // The access token is in authResult.AccessToken
                if (string.IsNullOrEmpty(authResult.AccessToken))
                {
                    _logger.LogError("Error when authenticating for Medtech access token. Access token is empty.");
                    return "";
                }
                return authResult.AccessToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, "Error when authenticating for Medtech access token.");
                return "";
            }
        }

        // Generate a FhirClient
        private FhirClient GetFhirClient(PartnerIntegrationMapping partner)
        {
            string token = GetMedtechAccessToken(partner).GetAwaiter().GetResult();
            var messageHandler = new HttpClientEventHandler();
            messageHandler.OnBeforeRequest += (object sender, BeforeHttpRequestEventArgs e) =>
            {
                e.RawRequest.Headers.Add("Authorization", $"Bearer {token}");
                e.RawRequest.Headers.Add("mt-facilityid", partner.ExPartnerMapId);
            };
            FhirClientSettings settings = new FhirClientSettings()
            {
                PreferredFormat = ResourceFormat.Json,
                VerifyFhirVersion = true
            };
            return new FhirClient($"{partner.IntegrationURL}fhir/", messageHandler: messageHandler, settings: settings);
        }

        public async Task<bool> SendProgressNote(TelehealthProgressNote note, List<PartnerIntegrationMapping> partner)
        {
            if (_partnerService is null)
            {
                _logger.LogError("SendProgressNote - PartnerService is null.");
                return false;
            }

            _logger.LogInformation($"Sending progress note to Medtech. Mappings:{HTJsonSerialiser.Serialise(partner)}");
            _logger.LogInformation($"Progress note ExResidentId {note.ExResidentId}, ExCarerId {note.ExCarerId}");

            try
            {
                // Map the patient that the note is about
                var patientMap = partner.FirstOrDefault(p => p.UserExId == note.ExResidentId);
                if (patientMap is null)
                {
                    // TODO: do we need a way to search for the patient in medtech - min required is either Surname/DOB/Email or Surname/DOB/Phone or NHI
                    _logger.LogError($"SendProgressNote - No Medtech mapping found for patient {note.ExResidentId}");
                    return false;
                }

                // Get all the Medtech mappings for the facility
                //var facilityMaps = await _partnerService.GetFacilityPartnerMapping(patientMap.ExFacilityId, note.ExResidentId);

                // Map the user that authored the note
                var staffMap = partner.FirstOrDefault(p => p.UserExId == note.ExCarerId);
                if (staffMap is null)
                {
                    staffMap = partner.FirstOrDefault(p => p.EntityType.ToLower() == "doctor");
                    if (staffMap is null)
                    {
                        // TODO: do we need a way to lookup the provider in medtech, or perhaps send to a default??? Provider is mandatory in the API, but what if the user is a nurse?
                        _logger.LogError($"SendProgressNote - No Medtech mapping found for note author {note.ExCarerId} - {note.CarerName ?? string.Empty}");
                        return false;
                    }
                }

                // Get the patient details from HealthTeams
                var jsonSecurity = $"{{\"exFacilityId\":\"{note.ExFacilityId}\",\"userExId\":\"{note.ExCarerId}\"}}";
                HealthTeamsUser patient = null;
                try
                {
                    var patientUser = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_User_Details_by_UserexId", patientMap.UserExId, jsonSecurity);
                    if (string.IsNullOrEmpty(patientUser))
                    {
                        _logger.LogError($"SendProgressNote - Unable to get details for patient {patientMap.UserExId}");
                        return false;
                    }
                    patient = HTJsonSerialiser.Deserialise<HealthTeamsUser>(patientUser);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex.Message, "Error when getting patient details for progress note.");
                    return false;
                }

                // Get the staff details from HealthTeams
                HealthTeamsUser practitioner = null;
                try
                {
                    var staffUser = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_User_Details_by_UserexId", staffMap.UserExId, jsonSecurity);
                    if (string.IsNullOrEmpty(staffUser))
                    {
                        _logger.LogError($"SendProgressNote - Unable to get details for the note author {staffMap.UserExId}");
                        return false;
                    }
                    practitioner = HTJsonSerialiser.Deserialise<HealthTeamsUser>(staffUser);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex.Message, "Error when getting staff details for progress note.");
                    return false;
                }

                // Get the facility details from HealthTeams
                /*
                HealthTeamsFacility facility = null;
                try
                {
                    var facJson = $"{{\"exFacilityId\":\"{note.ExFacilityId}\"}}";
                    var facResult = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Facility_ById", facJson, jsonSecurity);
                    if (string.IsNullOrEmpty(facResult))
                        return false;
                    facility = HTJsonSerialiser.Deserialise<HealthTeamsFacility>(facResult);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex.Message, "Error when getting staff details for progress note.");
                    return false;
                }
                DateTime noteLocalDateTime = ht.data.common.Extensions.DateTimeExtensions.ToTimeZoneTime(note.NoteDateUtc.GetValueOrDefault(DateTime.UtcNow), facility.FacilityLocaleZone);
                */

                FhirClient client = GetFhirClient(patientMap);
                Hl7.Fhir.Model.DocumentReference documentReference = new Hl7.Fhir.Model.DocumentReference();
                documentReference.Author = new List<ResourceReference> { new ResourceReference { Reference = $"{staffMap.IntegrationURL}fhir/Practitioner/{staffMap.SourceId}" } };
                documentReference.Status = DocumentReferenceStatus.Current;
                documentReference.Type = new CodeableConcept("http://loinc.org", "11488-4", "Consult Note", "Consult Note");
                documentReference.Description = "Progress Note from Medtech Connect";
                documentReference.Subject = new ResourceReference { Reference = $"{staffMap.IntegrationURL}fhir/Patient/{patientMap.SourceId}" };

                var progressNoteHtml = new StringBuilder();
                var practitionerName = (practitioner.FirstName + " " + practitioner.LastName).Trim();
                //progressNoteHtml.Append($"<p><strong>Progress Note for {patient.FullName}</strong></p>");
                progressNoteHtml.Append("<style>body {font-family: Verdana,Arial, Helvetica, sans-serif; font-size: 11px;}</style>");
                progressNoteHtml.Append($"<p>Created by {practitionerName} in Medtech Connect</p>");
                progressNoteHtml.Append($"<p>{note.NoteDetails}</p>");

                RTFHelper rtfHelper = new RTFHelper(_logger);
                string noteRtf = rtfHelper.ConvertHTMLtoRTF(progressNoteHtml.ToString());

                _logger.LogInformation($"SendProgressNote - progress note converted to RTF: {noteRtf}");

                // Alex documentation says the content should be base64 encoded, but that doesnt work with the FHIR client, so we will send the RTF string as a byte array
                documentReference.Content = new List<DocumentReference.ContentComponent>
                {
                new DocumentReference.ContentComponent
                {
                    Attachment = new Attachment
                    {
                        ContentType = "application/rtf",
                        Data = Encoding.UTF8.GetBytes(noteRtf)
                    },
                    //Format = new Coding("http://loinc.org", "61149-1", "Objective Narrative")
                    Format = new Coding("http://loinc.org", "61150-9", "Subjective Narrative")
                }
                };

                documentReference.Context = new DocumentReference.ContextComponent
                {
                    FacilityType = new CodeableConcept("http://snomed.info/sct", "Other", "Progress note from Medtech Connect", "Other"),
                    Period = new Period
                    {
                        Start = note.NoteDateUtc.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                    }
                };

                var documentReferenceResponse = await client.CreateAsync<DocumentReference>(documentReference);
                if (documentReferenceResponse != null)
                {
                    _logger.LogInformation($"Progress note {note.ExNoteId} sent. Medtech Id: {documentReferenceResponse.Id}");

                    // Save the returned Medtech Id to the note in the database
                    dynamic upsert = new
                    {
                        ExNoteId = note.ExNoteId,
                        SrcNoteId = documentReferenceResponse.Id
                    };
                    var json = HTJsonSerialiser.Serialise(upsert);
                    var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Notes", json, jsonSecurity);
                    return true;
                }
                else
                {
                    _logger.LogError($"Progress note {note.ExNoteId} not sent. Medtech response: {HTJsonSerialiser.Serialize(documentReferenceResponse)}");
                    return false;
                }
            }

            catch (Exception ex)
            {
                _logger.LogError(ex.Message, $"Error when sending progress note {note.ExNoteId} to Medtech.");
                return false;
            }
        }

        public async Task<List<TelehealthProgressNote>> ReadProgressNotesFromMedtech(string residentExId, List<PartnerIntegrationMapping> partner)
        {
            // Define how many days in the past to search for consult notes
            int daysPast = 365;
            var notesList = new List<TelehealthProgressNote>();

            try
            {
                // Map the patient that the note is about
                var patientMap = partner.FirstOrDefault(p => p.UserExId == residentExId);
                if (patientMap is null)
                {
                    _logger.LogError($"Error when getting progress notes from Medtech. Patient {residentExId} not found in partner mapping.");
                    return null;
                }

                FhirClient client = GetFhirClient(patientMap);
                RTFHelper rtfHelper = new RTFHelper(_logger);

                // Get current date (no time) in the local timezone
                DateTime utcNow = DateTime.UtcNow;
                TimeZoneInfo newZealandTimeZone = TimeZoneInfo.FindSystemTimeZoneById(patientMap.FacilityLocaleZone);
                DateTime nzDate = TimeZoneInfo.ConvertTimeFromUtc(utcNow, newZealandTimeZone).Date;
                DateTimeOffset newZealandDate = new DateTimeOffset(nzDate, newZealandTimeZone.BaseUtcOffset);

                // Format the date and time as a string
                string formattedStart = newZealandDate.AddDays(-daysPast).ToString("yyyy-MM-ddTHH:mm:sszzz");
                string formattedEnd = newZealandDate.ToString("yyyy-MM-ddTHH:mm:sszzz");

                // Search for consult notes for the specific patient within the date range
                var searchParams = new SearchParams()
                    .Where($"patient._id={patientMap.SourceId}")
                    .Where($"type=http://loinc.org|11488-4")
                    .Where($"period=ge{formattedStart}")
                    .Where($"period=le{formattedEnd}");

                _logger.LogInformation($"Search params: {HTJsonSerialiser.Serialise(searchParams)}");
                _logger.LogInformation($"Search params: {searchParams.ToString()}");

                Bundle bundle = await client.SearchAsync<DocumentReference>(searchParams);

                _logger.LogInformation($"Found {bundle.Entry.Count} entries.");

                // Process the response
                foreach (var entry in bundle.Entry)
                {
                    DocumentReference docRef = (DocumentReference)entry.Resource;

                    TelehealthProgressNote note = new TelehealthProgressNote();
                    try
                    {
                        note.Title = "Consult Note from Medtech";
                        note.ExResidentId = residentExId;
                        note.CarerName = docRef.Author[0].Display;

                        DateTimeOffset dateTimeOffset = DateTimeOffset.Parse(docRef.Context.Period.Start);
                        note.NoteDateUtc = dateTimeOffset.UtcDateTime;

                        // Extract RTF content
                        string noteHtml = "";
                        foreach (var content in docRef.Content)
                        {
                            if (content.Attachment.ContentType == "application/rtf")
                            {
                                byte[] rtfData = content.Attachment.Data;
                                string rtfContent = System.Text.Encoding.UTF8.GetString(rtfData);
                                noteHtml = rtfHelper.ConvertRTFtoHTML(rtfContent);
                            }
                        }
                        note.NoteDetails = noteHtml;

                        // TODO: implement better way to filter out notes that are from Medtech Connect (us)
                        if (!string.IsNullOrEmpty(note.NoteDetails) && !note.NoteDetails.Contains("Medtech Connect"))
                            notesList.Add(note);
                    }
                    catch (Exception)
                    {
                        _logger.LogError($"Error when processing progress note {docRef.Id} from Medtech.");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message, $"Error when getting progress notes from Medtech.");
            }
            return notesList;
        }

        public async Task<bool> SendWoundAssessment(Wound wound, string userExId, string residentExId, List<PartnerIntegrationMapping> partner)
        {
            try
            {
                var woundNote = new StringBuilder();
                woundNote.Append("The following wound details have been recorded in Medtech Connect:<br>");

                if (!string.IsNullOrEmpty(wound.Name))
                    woundNote.Append($"Wound Name : {wound.Name}<br>");

                if (!string.IsNullOrEmpty(wound.Type))
                    woundNote.Append($"Wound Type : {wound.Type}<br>");

                if (!string.IsNullOrEmpty(wound.Vitalsigns))
                    woundNote.Append($"Vital Signs : {wound.Vitalsigns}<br>");

                if (!string.IsNullOrEmpty(wound.WoundPosition))
                    woundNote.Append($"Wound Position : {wound.WoundPosition}<br>");

                if (!string.IsNullOrEmpty(wound.Status))
                    woundNote.Append($"Status : {wound.Status}<br>");

                if (!string.IsNullOrEmpty(wound.WoundSide))
                    woundNote.Append($"Wound Side : {wound.WoundSide}<br>");

                if (wound.FirstNoticeDateUTC is not null)
                    woundNote.Append($"First Noticed : {wound.FirstNoticeDateUTC.Value.ToString("dd/MM/yyyy")}<br>");

                woundNote.Append("<br>");

                if (wound.WoundDetails is not null)
                {
                    var woundDetail = wound.WoundDetails.FirstOrDefault();
                    if (woundDetail.InspectionDate is not null)
                        woundNote.Append($"Inspection Date : {woundDetail.InspectionDate.Value.ToString("dd/MM/yyyy")}<br>");
                    if (woundDetail.Measurements is not null)
                        woundNote.Append($"Measurement : {woundDetail.Measurements}<br>");
                    if (woundDetail.Depth is not null)
                        woundNote.Append($"Wound Depth : {woundDetail.Depth}<br>");
                    if (woundDetail.BedSurface is not null)
                        woundNote.Append($"Bed Surface : {woundDetail.BedSurface}<br>");
                    if (woundDetail.ExudateDrainageType is not null)
                        woundNote.Append($"Exudate Drainage Type : {woundDetail.ExudateDrainageType}<br>");
                    if (woundDetail.Drainageamount is not null)
                        woundNote.Append($"Drainage Amount : {woundDetail.Drainageamount}<br>");
                    if (woundDetail.WoundOdour is not null)
                        woundNote.Append($"Wound Odour : {woundDetail.WoundOdour}<br>");

                    if (woundDetail.Woundedgecharacteristics is not null && woundDetail.Woundedgecharacteristics.Count > 0)
                        woundNote.Append($"Wound Edge Characteristics : {string.Join(", ", woundDetail.Woundedgecharacteristics)}<br>");
                    if (woundDetail.PeriWoundandSurroundingSkin is not null && woundDetail.PeriWoundandSurroundingSkin.Count > 0)
                        woundNote.Append($"Peri Wound and Surrounding Skin : {string.Join(", ", woundDetail.PeriWoundandSurroundingSkin)}<br>");

                    if (woundDetail.Pain is not null)
                        woundNote.Append($"Pain : {woundDetail.Pain}<br>");
                    if (woundDetail.Comments is not null)
                        woundNote.Append($"Comments : {woundDetail.Comments}<br>");
                    if (woundDetail.Progress is not null)
                        woundNote.Append($"Progress : {woundDetail.Progress}<br>");
                    if (woundDetail.ProgressDetails is not null)
                        woundNote.Append($"Progress Details : {woundDetail.ProgressDetails}<br>");

                    if (woundDetail.WoundImages.Count > 0)
                    {
                        woundNote.Append(woundDetail.WoundImages.Count > 1 ? "<br>Images:<br>" : "<br>Image:<br>");
                    }

                    foreach (var image in woundDetail.WoundImages)
                    {
                        // In order for links to be clickable in Medtech, we need the url as the filename too
                        woundNote.Append($"<a href='{image.FileUrl}'>{image.FileUrl}</a><br>");
                    }
                }

                var doctorMap = partner.FirstOrDefault(p => p.UserExId == userExId);
                if (doctorMap == null)
                    doctorMap = partner.FirstOrDefault(p => p.EntityType.ToLower() == "doctor");
                var patientMap = partner.FirstOrDefault(p => p.UserExId == residentExId); 

                if (await SendConsultNoteToMedtech(woundNote.ToString(), DateTime.UtcNow, patientMap, doctorMap))
                {
                    return true;
                }
                else
                {
                    _logger.LogError($"Error when sending wound assessment note to Medtech.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error when sending wound assessment note to Medtech: {ex.ToString()}");
                return false;
            }
        }

        public async Task<bool> SendConsultNoteToMedtech(string note, DateTime takenDateUtc, PartnerIntegrationMapping patientMap, PartnerIntegrationMapping doctorMap)
        {
            _logger.LogInformation($"Sending a consult note to Medtech. Mappings:{HTJsonSerialiser.Serialise(doctorMap)}");

            try
            {
                // I think we pass UTC to medtech
                //TimeZoneInfo newZealandTimeZone = TimeZoneInfo.FindSystemTimeZoneById("New Zealand Standard Time");
                //DateTime nzDate = TimeZoneInfo.ConvertTimeFromUtc(takenDateUtc, newZealandTimeZone);
                //DateTimeOffset facilityDatetimeOffset = new DateTimeOffset(nzDate, newZealandTimeZone.BaseUtcOffset);


                FhirClient client = GetFhirClient(patientMap);
                Hl7.Fhir.Model.DocumentReference documentReference = new Hl7.Fhir.Model.DocumentReference();
                documentReference.Author = new List<ResourceReference> { new ResourceReference { Reference = $"{doctorMap.IntegrationURL}fhir/Practitioner/{doctorMap.SourceId}" } };
                documentReference.Status = DocumentReferenceStatus.Current;
                documentReference.Type = new CodeableConcept("http://loinc.org", "11488-4", "Consult Note", "Consult Note");
                documentReference.Description = "Clinical data from Medtech Connect";
                documentReference.Subject = new ResourceReference { Reference = $"{doctorMap.IntegrationURL}fhir/Patient/{patientMap.SourceId}" };

                var progressNoteHtml = new StringBuilder();
                progressNoteHtml.Append("<style>body {font-family: Verdana,Arial, Helvetica, sans-serif; font-size: 11px;}</style>");
                progressNoteHtml.Append($"{note}");

                RTFHelper rtfHelper = new RTFHelper(_logger);
                string noteRtf = rtfHelper.ConvertHTMLtoRTF(progressNoteHtml.ToString());

                _logger.LogInformation($"SendConsultNoteToMedtech - consult note converted to RTF: {noteRtf}");

                // Alex documentation says the content should be base64 encoded, but that doesnt work with the FHIR client, so we will send the RTF string as a byte array
                documentReference.Content = new List<DocumentReference.ContentComponent>
                {
                new DocumentReference.ContentComponent
                {
                    Attachment = new Attachment
                    {
                        ContentType = "application/rtf",
                        Data = Encoding.UTF8.GetBytes(noteRtf)
                    },
                    Format = new Coding("http://loinc.org", "61149-1", "Objective Narrative")
                    //Format = new Coding("http://loinc.org", "61150-9", "Subjective Narrative")
                }
                };

                documentReference.Context = new DocumentReference.ContextComponent
                {
                    FacilityType = new CodeableConcept("http://snomed.info/sct", "Other", "Consult note from Medtech Connect", "Other"),
                    Period = new Period
                    {
                        Start = takenDateUtc.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                    }
                };

                var documentReferenceResponse = await client.CreateAsync<DocumentReference>(documentReference);
                if (documentReferenceResponse != null)
                {
                    _logger.LogInformation($"Consult note sent. Medtech Id: {documentReferenceResponse.Id}");

                    // TODO: Save the Medtech Id to the note in the database
                    // tblResidentNotes.SrcNoteId

                    //UpsertNotesRequest req = new UpsertNotesRequest();

                    //var json = HTJsonSerialiser.Serialise(note);
                    //var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Notes", json, jsonSecurity);

                    //br = HTJsonSerialiser.Deserialise<UpsertNotesResponse>(res);

                    return true;
                }
                else
                {
                    _logger.LogError($"Consult note not sent. Medtech response: {HTJsonSerialiser.Serialize(documentReferenceResponse)}");
                    return false;
                }
            }

            catch (Exception ex)
            {
                _logger.LogError($"Error when sending consult note to Medtech: {ex.ToString()}");
                return false;
            }
        }

        public async Task<string> GetIPAddress()
        {
            string ipAddress = "";
            using (HttpClient client = new HttpClient())
            {
                try
                {
                    HttpResponseMessage response = await client.GetAsync("https://whatsmyip.dev/api/ip");
                    response.EnsureSuccessStatusCode();
                    string responseBody = await response.Content.ReadAsStringAsync();

                    // Parse the JSON response
                    JObject json = JObject.Parse(responseBody);
                    ipAddress = json["addr"].ToString();
                    _logger.LogInformation($"The outbound IP Address is: {ipAddress}");
                }
                catch (HttpRequestException e)
                {
                    _logger.LogError($"GetIPAddress error: {e.ToString()}");
                }
            }
            return ipAddress;
        }

    }
}


