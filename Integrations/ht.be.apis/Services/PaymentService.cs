﻿using ht.be.apis.Utils;
using ht.data.common;
using ht.data.common.Billing;
using RestSharp;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.apis.Services
{
    public class PaymentService
    {
        public static string BusinessApiKey { get; set; }
        public static string BaseUrl { get; set; }
        public static string Env { get; set; }
        public static string AppId { get; set; }
        public static string AppVer { get; set; } = "0.1";

        public string AppToken { get; set; }

        public async Task Init()
        {
            if (string.IsNullOrEmpty(AppToken))
                await GetTransToken();
        }
        public async Task<string> GetTransToken()
        {
            using (var client = GetHttpClient())
            {
                var rr = GetRequest("/v3/auth/token", Method.Post)
                    .AddJsonBody(new
                    {
                        audience = "aud:business-sdk",
                        expiresIn = "1h"
                    });

                var resp = await client.ExecuteAsync(rr);
                if (resp.IsSuccessful == false)
                    throw new System.Exception("ERROR: " + resp.ErrorMessage);

                var tok = System.Text.Json.Nodes.JsonNode.Parse(resp.Content)?["token"]?.GetValue<string>();
                AppToken = tok;
                return (tok);
            }
        }

        public async Task<MedicareVerificationResponse> VerifyPatientMedicare(MedicareVerificationRequest req)
        {
            MedicareVerificationResponse result = new();
            var json = HTJsonSerialiser.Serialise(req);
            using (var client = GetHttpClient())
            {
                var rr = GetMainRequest("/v3/medicare/verify", Method.Post)
                    .AddStringBody(json, "application/json");
                    

                var resp = await client.ExecuteAsync(rr);
                if (resp.IsSuccessful == false)
                    throw new System.Exception("ERROR: " + resp.Content);

                result = HTJsonSerialiser.Deserialise<MedicareVerificationResponse>(resp.Content);
                result.Message = resp?.Content;
                return (result);
            }
        }

        public async Task<string> GetCreateTransactionUrl(CreatePaymentUrlRequest req,DBService _dbSvc)
        {
            var json = "{\"exTaskId\":\"" + req.ExTaskId + "\",\"exDoctorId\":\"" + req.ExDoctorId + "\",\"exResidentId\":\"" + req.ExResidentId + "\"}";
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Payment_GetTransactionUrl_Details", json);

            req.Payload = HTJsonSerialiser.Deserialise<Payload>(res);
            req.Payload.Funder = "medicare";
            req.Payload.Platform = "funder";
            req.Payload.ContainerId = "medipass-container";
            req.Payload.Version = "3";
            res = HTJsonSerialiser.Serialize(req.Payload);
            var pl = req.Payload;
            var cf = new Config
            {
                 AppId=PaymentService.AppId,
                 AppVersion = PaymentService.AppVer,
                 Token = this.AppToken,
                 Env="stg"
            };
            var strCfg = HTJsonSerialiser.Serialise(cf);
            res = System.Uri.EscapeDataString(res) + "&config=" + System.Uri.EscapeDataString(strCfg);
            res = res.Replace("%3A", ":").Replace("%2C",",");
            var sb = new StringBuilder();
            sb.Append("https://stg-connect.medipass.io/standalone/create-transaction/?payload=" + res);

            return (sb.ToString());
        }

        public static RestClient GetHttpClient()
        {
            return new RestSharp.RestClient(PaymentService.BaseUrl);
        }

        public static RestRequest GetRequest(string url,Method m= Method.Get)
        {
            var rr = new RestRequest(url,m);
            rr.AddHeader("authorization", $"Bearer {BusinessApiKey}");
            rr.AddHeader("x-appid", $"{AppId}");
            rr.AddHeader("x-appver", $"{AppVer}");
            return rr;
        }

        public RestRequest GetMainRequest(string url, Method m = Method.Get)
        {
            var rr = new RestRequest(url,m);
            rr.AddHeader("authorization", $"Bearer {AppToken ?? BusinessApiKey}");
            rr.AddHeader("x-appid", $"{AppId}");
            rr.AddHeader("x-appver", $"{AppVer}");
            return rr;
        }


    }
}
