﻿using ht.be.apis.Utils;
using ht.common.backend.shared.classes;
using System;
using System.Net.Http;
using System.Text;

namespace ht.be.apis.Services
{
    public class SmsService
    {
        public string _portalUrl = null;
        public string _apimKey = null;
        public string _apimsendSmsurl = null;

        public SmsService()
        {
            _portalUrl = common.backend.shared.Globals.Properties["PortalUrl"];
            _apimKey = common.backend.shared.Globals.Properties["APIMKey"];
            _apimsendSmsurl = common.backend.shared.Globals.Properties["APIMSendSmsURL"];
        }


        public string SendToSms(string toAddress, string content)
        {

            if (!toAddress.StartsWith("+"))
                toAddress = "+" + toAddress;

            var phUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
            var smsNum = phUtil.Parse(toAddress, null);
            if (smsNum.HasCountryCode && smsNum.HasNationalNumber)
            {
                using (var clnt = new HttpClient())
                {
                    var toNum = $"+{smsNum.CountryCode}{smsNum.NationalNumber}";
                    var sms = new SmsContent
                    {
                        ToAddress = toNum,
                        Message = content,
                        FromAddress = "HealthTeams",
                        UniqueId = Guid.NewGuid().ToString(),
                    };
                    var sJson = HTJsonSerialiser.Serialize(sms);
                    clnt.DefaultRequestHeaders.Add("x-api-key", _apimKey);

                    var json = new StringContent(sJson, Encoding.UTF8, "application/json");
                    var res = clnt.PostAsync(_apimsendSmsurl, json).GetAwaiter().GetResult();

                    if (res.IsSuccessStatusCode)
                        return (res.Content.ReadAsStringAsync().GetAwaiter().GetResult());
                    else
                        return "ERROR: " + res.ReasonPhrase;
                }

            }
            else
            {
                return $"ERROR: {toAddress} is not in the corrected international format";
            }
        }
    }
}
