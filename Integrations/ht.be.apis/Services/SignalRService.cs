﻿using ht.be.apis.Utils;
using RestSharp;

namespace ht.be.apis.Services
{
    public class SignalRService
    {
        public string BaseUrl { get; set; }

        public SignalRService()
        {
            BaseUrl = Utils.APIMManager.baseUrl;
        }

        public bool AddToGroup(string groupName,string userId)
        {
            using (var clnt = new RestClient(BaseUrl))
            { 
                var rr = new RestRequest($"events/v1/{groupName}/add/{userId}",Method.Get);
                var resp = clnt.GetAsync(rr).GetAwaiter().GetResult();
                if (!string.IsNullOrEmpty(resp?.ErrorMessage))
                    throw new System.Exception($"ERROR: Adding User {userId} to group {groupName} failed." + resp.ErrorMessage);
            }
            return (true);
        }

        public bool SendToGroup(string groupName, string target,string status, object data)
        {
            //var obj = HTJsonSerialiser.Deserialise<object>(data);
            return (true);
            //MICK: 2023-07-14  removing signalR

            using (var clnt = new RestClient(BaseUrl))
            {
                var rr = new RestRequest($"events/v1/{groupName}/send/{target}/{status}", Method.Post)
                    .AddJsonBody(data);

                var resp = clnt.PostAsync(rr).GetAwaiter().GetResult();
                if (!string.IsNullOrEmpty(resp?.ErrorMessage))
                    throw new System.Exception($"ERROR: Sending to Group {groupName} {target} {status}." + resp.ErrorMessage);
            }
            return (true);
        }

    }
}
