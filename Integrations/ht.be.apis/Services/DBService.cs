﻿
using Microsoft.ApplicationInsights;
using Microsoft.Extensions.Logging;

using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;

namespace ht.be.apis.Services;
public class DBService
{
    public ILogger _logger;
    public TelemetryClient _telemetryClient;

    public static string DBConstr { get; set; }

    public async Task<string> ExecSprocWithJsonAsync(string sprocName, string json)
    {
        return await ExecSprocAsync(sprocName, new Dictionary<string, string> { { "json", json } });
    }

    public async Task<string> ExecSprocWithJsonAsync(string sprocName, string json, string jsonSecurity)
    {
        return await ExecSprocAsync(sprocName, new Dictionary<string, string> { { "json", json }, { "jsonSecurity", jsonSecurity } });
    }

    public async Task<string> ExecSprocWithIdAsync(string sprocName, string id)
    {
        return await ExecSprocAsync(sprocName, new Dictionary<string, string> { { "id", id } });
    }

    public async Task<string> ExecSprocWithIdAsync(string sprocName, string id, string jsonSecurity)
    {
        return await ExecSprocAsync(sprocName, new Dictionary<string, string> { { "id", id }, { "jsonSecurity", jsonSecurity } });
    }
    public async Task<string> ExecSprocAsync(string sprocName, Dictionary<string, string> parms = null)
    {
        try
        {
            using (var conn = new SqlConnection(DBConstr))
            {
                conn.Open();
                using (var cmd = new SqlCommand(sprocName, conn) { CommandType = System.Data.CommandType.StoredProcedure })
                {
                    foreach (var p in parms)
                    {
                        if (p.Key == "json")
                            cmd.Parameters.Add("@json", System.Data.SqlDbType.VarChar, -1).Value = p.Value;
                        else
                            cmd.Parameters.Add($"@{p.Key}", System.Data.SqlDbType.VarChar).Value = p.Value;
                    }

                    var rdr = cmd.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                    var sb = new StringBuilder();
                    if (rdr.HasRows)
                    {
                        while (rdr.Read())
                        {

                            sb.Append(rdr.GetValue(0)?.ToString());
                        }
                    }
                    return (sb.ToString());
                }
            }
        }
        catch (SqlException sqlex)
        {
            if (_telemetryClient != null)
            {
                var props = new Dictionary<string, string>
                {
                    { "sproc",sprocName },
                    { "params",GetDict(parms)}
                };
                _telemetryClient?.TrackException(sqlex, props);
            }
            throw sqlex;
        }
        catch (System.Exception ex)
        {
            if (_telemetryClient != null)
                _telemetryClient?.TrackException(ex);
            throw;
        }
    }

    private string GetDict(Dictionary<string, string> dict)
    {
        if (dict == null)
            return string.Empty;
        var sb = new StringBuilder();
        foreach (var key in dict.Keys)
            sb.AppendLine(key + "=" + dict[key]);
        return sb.ToString();
    }
}
