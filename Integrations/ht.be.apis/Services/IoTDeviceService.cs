﻿using ht.be.apis.Utils;
using ht.common.backend.shared.models.iot;
using Microsoft.Azure.Devices;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ht.be.apis.Services
{
    public class IoTDeviceService
    {
        public DBService _dbSvc { get; set; }
        public ILogger _log { get; set; }

        private int iMaxDeviceCount = 4000;

        public static string IoTConnStr { get; set; }
        public static string IoTDevConnStr { get; set; }
        public static int MaxDeviceCount { get; set; }

        public async Task SyncDevicesToDB()
        {
            var lst = await GetDevices();
            var json = HTJsonSerialiser.Serialise(lst);
            await _dbSvc.ExecSprocWithJsonAsync("sp_FacilityIoTDevices_Upsert", json);
        }

        public async Task<string> GetNextAvailableDevice()
        {
            
            var json = await _dbSvc.ExecSprocWithJsonAsync("sp_FacilityIoTDevices_GetNextAllocated", "");
            return (json);
        }

        public async Task AddDevices(int iNum=800)
        {
            using (var svc = GetClient())
            {
                for (int i = 0; i <= iNum; i++)
                {
                    var n = $"ht-dev-{i}";
                    var d = new Device(n);
                    var dev = await svc.AddDeviceAsync(d);

                }
            }
        }


        public async Task<List<azureDevice>> GetDevices()
        {
            using (var svc = GetClient())
            {
               
                //var query = svc.CreateQuery("SELECT * from devices",400);
                var dev = await svc.GetDevicesAsync(iMaxDeviceCount);
                return dev.Select(d =>
                {
                    return new azureDevice
                    {
                        DeviceConnectionString = IoTDevConnStr?.Replace("[deviceid]", d.Id).Replace("[key]", d.Authentication?.SymmetricKey?.PrimaryKey),
                        DeviceName = d.Id
                    };
                    
                }).ToList<azureDevice>();

            }
        }

        private RegistryManager GetClient()
        {
            return RegistryManager.CreateFromConnectionString(IoTConnStr);

        }
    }
}
