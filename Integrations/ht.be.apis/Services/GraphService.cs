﻿using Microsoft.Graph.Core;
using Microsoft.Identity.Client;
using System.Threading.Tasks;
using System.Linq;
using ht.data.common.Users;
using System.Collections.Generic;
using Azure.Identity;
using Microsoft.Kiota.Abstractions;
using Microsoft.Graph.Models;
using System;
using Microsoft.Graph;

namespace ht.be.apis.Services
{
    public class GraphService
    {
        public static string AppId { get; set; }
        public static string TenantId { get; set; }
        public static string ClientSecret { get; set; }
        public static string Issuer { get; set; }

        private readonly Microsoft.Graph.GraphServiceClient _gsc;

        public GraphService()
        {
            // var scopes = new[] { "Directory.AccessAsUser.All" };
            var scopes = new[] { $"https://graph.microsoft.com/.default" };
            var options = new TokenCredentialOptions
            {
                AuthorityHost = AzureAuthorityHosts.AzurePublicCloud
            };
            /*
            IConfidentialClientApplication confClientApp = ConfidentialClientApplicationBuilder
                .Create(AppId)
                .WithTenantId(TenantId)
                .WithClientSecret(ClientSecret)
                .Build();

            var authProv = new Microsoft.Graph.Auth.ClientCredentialProvider(confClientApp);
            */
            var authProv = new ClientSecretCredential(TenantId, AppId, ClientSecret, options);

            //var authCodeCred = new AuthorizationCodeCredential(TenantId, AppId, ClientSecret, "1", options);

            _gsc = new Microsoft.Graph.GraphServiceClient(authProv);
            //_gsc = new GraphServiceClient(authCodeCred, scopes);
            /*
            _gsc = new GraphServiceClient(
                new DelegateAuthenticationProvider(
                    async (req) =>
                    {
                        //get an access token if required.
                        var authResult = await confClientApp.AcquireTokenForClient(scopes).ExecuteAsync();
                        req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer",authResult.AccessToken);
                    }
                ));
            */
        }

        public static GraphServiceClient GetGraphServiceClient()
        {
            // var scopes = new[] { "Directory.AccessAsUser.All" };
            var scopes = new[] { $"https://graph.microsoft.com/.default" };
            var options = new TokenCredentialOptions
            {
                AuthorityHost = AzureAuthorityHosts.AzurePublicCloud
            };

            // Granted to the application through here
            // https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/CallAnAPI/appId/0ca16d25-5a36-47a7-a8ca-f2e0a7bb3bdf/defaultBlade/CallAnAPI
            //if (env.ToLower().Contains("uat"))
            //{
            //    ClientId = "0ca16d25-5a36-47a7-a8ca-f2e0a7bb3bdf"; //AppId from 
            //    ClientSecret = "****************************************";
            //    TenantId = "234832e0-e532-492f-828d-c41642fd98b3";
            //}
            //else if (env.ToLower().Contains("demo"))
            //{

            //}


            var authProv = new ClientSecretCredential(TenantId, AppId, ClientSecret, options);


            return new GraphServiceClient(authProv);

        }
        public async Task<System.Collections.Generic.List<string>> GetUsers()
        {

            var result = await _gsc.Users
                .GetAsync(reqCfg => reqCfg.QueryParameters.Select = new string[] { "id", "DisplayName" });

            return result.Value.Select(x => x.DisplayName).ToList<string>();
        }

        public async Task ChangePassword(ChangeUserPasswordRequest req)
        {
            var user = new Microsoft.Graph.Models.User
            {
                PasswordPolicies = "DisablePasswordExpiration,DisableStrongPassword",
                PasswordProfile = new Microsoft.Graph.Models.PasswordProfile
                {
                    ForceChangePasswordNextSignIn = false,
                    Password = req.NewPassword
                }
            };


            await _gsc.Users[req.UserExId].PatchAsync(user);

        }

        public async Task<string> CreateUser(HealthTeamsUser req, string Password)
        {
            var gc = GetGraphServiceClient();
            var user = new User
            {
                AccountEnabled = true,
                DisplayName = req.FirstName + " " + req.LastName,
                MailNickname = req.FirstName + "_" + req.LastName,
                Mail = req.Email,
                UserPrincipalName = req.Email.Replace("@", "_") + "#EXT#@" + GraphService.Issuer,
                Identities = new List<ObjectIdentity>
                {
                    new ObjectIdentity
                    {
                        SignInType = "emailAddress",
                        Issuer = GraphService.Issuer,
                        IssuerAssignedId = req.Email,
                    }
                },
                PasswordProfile = new()
                {
                    Password = Password,// "Hello.123",
                    ForceChangePasswordNextSignIn = true, //<--- THIS COULD BE BIG
                }
            };
            var result = gc.Users.PostAsync(user).GetAwaiter().GetResult();

            return result?.Id;
        }

        public async Task<string> CreateOtherUsers(InviteUserResponse req, string Password)
        {
            var gc = GetGraphServiceClient();
            var user = new User
            {
                AccountEnabled = true,
                DisplayName = string.Concat(req.FirstName, " ", req.LastName).Trim(),
                MailNickname = string.Concat(req.FirstName + "_" + req.LastName).Trim(),
                Mail = req.Email,
                UserPrincipalName = req.Email.Replace("@", "_") + "#EXT#@" + GraphService.Issuer,
                Identities = new List<ObjectIdentity>
                {
                    new ObjectIdentity
                    {
                        SignInType = "emailAddress",
                        Issuer = GraphService.Issuer,
                        IssuerAssignedId = req.Email,
                    }
                },
                PasswordProfile = new()
                {
                    Password = Password,// "Hello.123",
                    ForceChangePasswordNextSignIn = true, //<--- THIS COULD BE BIG
                }
            };
            var result = gc.Users.PostAsync(user).GetAwaiter().GetResult();

            return result?.Id;
        }

        public async Task<string> CreateResident(HealthTeamsUser req, bool userHasEmail, string password)
        {
            req.UserExId ??= System.Guid.NewGuid().ToString();
            req.Role = "Resident";
            if (!userHasEmail)
                req.Email ??= $"{Convert.ToString(req?.Facility).ToLower()}_res.{Convert.ToString(req?.FirstName).ToLower()}.{Convert.ToString(req?.LastName).ToLower()}@healthteams.com.au";
            req.Mobile ??= "1";
            req.MobileCC ??= "";
            var result = await CreateUser(req, password);
            return "AAD:" + result;

        }

        public async Task<string> CreateInvitedUser(InviteUserResponse req, bool userHasEmail, string password)
        {
            req.UserExId ??= System.Guid.NewGuid().ToString();
            req.Role = req.Role;
            req.Email = req.Email;
            var result = await CreateOtherUsers(req, password);
            return "AAD:" + result;

        }



    }
}
