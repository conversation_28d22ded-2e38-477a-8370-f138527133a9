﻿using System;
using System.Collections.Generic;
using System.Security.Claims;

namespace ht.be.apis.models;

public class JwtUserAndRoles
{
    public string UserExId { get; set; }
    public List<HTRole> Roles { get; set; }

    public class HTRole
    {
        public string Role { get; set; }
        public string ExFacilityId { get; set; }
        public string Name { get; set; }
    }
}

public class GuestTokenRequest
{
    public string ExGuestUserId { get; set; }
    public string IdName { get; set; }
    public string TokenType { get; set; }
    public List<string> Roles { get; set; }
    public string UserRoles { get; set; }
    public DateTime TaskDateTimeUtc { get; set; }
    public List<Claim> CustomClaims { get; set; }
}

public class ClaimDto
{
    public string Type { get; set; }
    public string Value { get; set; }
}



