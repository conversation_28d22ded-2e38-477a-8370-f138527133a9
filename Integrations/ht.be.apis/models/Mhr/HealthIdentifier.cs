﻿using System;
using System.Text.RegularExpressions;

namespace ht.be.apis.models.Mhr
{
    public abstract class HealthIdentifier
    {
        private string _value;
        public abstract string InvalidMessage { get; }
        public abstract string IdentifierPrefix { get; }

        public string Value
        {
            get => FormatForDisplay(_value);
            set => _value = StripSpaces(value);
        }

        public HealthIdentifier(string value)
        {
            if (Validate(value))
            {
                _value = StripSpaces(value);
            }
            else
            {
                throw new ArgumentException(InvalidMessage);
            }
        }

        private string StripSpaces(string value)
        {
            return value.Replace(" ", "");
        }

        private string FormatForDisplay(string value)
        {
            return Regex.Replace(value, ".{4}", "$0 ");
        }

        public bool Validate(string value)
        {
            string strippedValue = StripSpaces(value);
            return strippedValue.Length == 16 &&
                   strippedValue.StartsWith(IdentifierPrefix) &&
                   LuhnCheck(strippedValue);
        }

        private bool LuhnCheck(string value)
        {
            int sum = 0;
            bool alternate = false;
            for (int i = value.Length - 1; i >= 0; i--)
            {
                int n = int.Parse(value[i].ToString());
                if (alternate)
                {
                    n *= 2;
                    if (n > 9)
                    {
                        n -= 9;
                    }
                }
                sum += n;
                alternate = !alternate;
            }
            return (sum % 10 == 0);
        }
    }
}
