﻿using System;

namespace ht.be.apis.models.TLW
{
    

    public class TlwStaffers
    {
        public Staffers[] data { get; set; }
        public Meta meta { get; set; }
    }

    public class Meta
    {
        public Pagination pagination { get; set; }
    }

    public class Pagination
    {
        public int current_page { get; set; }
        public int per_page { get; set; }
        public int total_pages { get; set; }
        public int total_count { get; set; }
    }

    public class Staffers
    {
        public int id { get; set; }
        public string[] admin_roles { get; set; }
        public string bio { get; set; }
        public string telephone { get; set; }
        public string avatar_url { get; set; }
        public int? access_role_id { get; set; }
        public Profile profile { get; set; }
        public Community[] communities { get; set; }
        public Role[] roles { get; set; }
        public object[] custom_attributes { get; set; }
    }

    public class Profile
    {
        public int id { get; set; }
        public string uuid { get; set; }
        public string email { get; set; }
        public string full_name { get; set; }
        public string preferred_name { get; set; }
        public string given_names { get; set; }
        public string family_name { get; set; }
        public DateTime created_at { get; set; }
        public DateTime updated_at { get; set; }
        public string telephone1 { get; set; }
        public string telephone2 { get; set; }
        public string date_of_birth { get; set; }
        public Address address { get; set; }
        public object[] custom_attributes { get; set; }
        public string avatar_url { get; set; }
        public string time_zone { get; set; }
    }

    public class Address
    {
        public string street1 { get; set; }
        public string street2 { get; set; }
        public string locality { get; set; }
        public string region { get; set; }
        public string postcode { get; set; }
    }

    public class Community
    {
        public int id { get; set; }
        public string name { get; set; }
        public DateTime updated_at { get; set; }
        public DateTime created_at { get; set; }
    }

    public class Role
    {
        public int id { get; set; }
        public int community_id { get; set; }
        public int staffer_id { get; set; }
        public bool is_public_role { get; set; }
        public string name { get; set; }
        public string title { get; set; }
        public string access_level { get; set; }
    }

}
