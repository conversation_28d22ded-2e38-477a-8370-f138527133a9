﻿
using ht.common.backend.shared.models;
using System;
using System.Collections.Generic;

namespace ht.be.apis.models;
public class UpsertUsersResponse : baseResponse
{
    public List<UpsertUserResponse> UpsertUserResponses { get; set; }

    
}

public class UpsertUserResponse
{
    public string MeetingTitle { get; set; }
    public string MeetingDescription {  get; set;}
    public string MeetingDateUtc { get; set; }

    public string DisplayName { get; set; }
    public string UserExId { get; set; }
    public string UserAcsId { get; set; }
    public string AcsToken { get; set; }
    public DateTime? AcsTokenExpiresUtc { get; set; }
    public string Role { get; set; }
}
