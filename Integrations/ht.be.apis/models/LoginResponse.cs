﻿
using ht.common.backend.shared.models;
using ht.common.backend.shared.models.security;
using System;
using System.Collections.Generic;

namespace ht.be.apis.models;
public class LoginResponse : baseResponse
{
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Mobile { get; set; }
    public string Email { get; set; }
    public string UserExId { get; set; }
    public List<UserFacilityRole> Roles { get; set; }
    public DateTime? LastUpdatedUtc { get; set; }
    public customJWTToken TokenDetails { get; set; }


    public class UserFacilityRole
    {
        public string Role { get; set; }
    }

    
}



