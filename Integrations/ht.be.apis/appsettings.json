{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "AcsConnStr": "endpoint=https://ht-acs-uat.communication.azure.com/;accesskey=N/oHtJwIcJyD/bFel7BaxS2xS59tz/tH1kus7gkVPTXBHq5aw/YvfWvF1RH8Kv3Ez7EcJEQVgT/RrnjB69JK9g==",
  "AcsEndpointUrl": "https://ht-acs-uat.communication.azure.com",
  "AcsCallbackUri": "https://app-ht-uat.azurewebsites.net/api/Callback?code=p8QGtoX0ZBWwnzCAYtSeRHW0zYS1lVz6oGr1BlVYQvy9aCEputB/Sg==",
  "WebHookQueue": "Endpoint=sb://htsysmsgs.servicebus.windows.net/;SharedAccessKeyName=basicsender;SharedAccessKey=Ms27AMFO0uFCUQiZMLfAizEByrAXzoDhJcp3kR80hTY=;EntityPath=webhkq",
  "HtSBConnStr": "Endpoint=sb://htsysmsgs.servicebus.windows.net/;SharedAccessKeyName=basicsender;SharedAccessKey=Ms27AMFO0uFCUQiZMLfAizEByrAXzoDhJcp3kR80hTY=;EntityPath=webhkq",
  "ht_beasync_url": "http://app-ht-uat.azurewebsites.net",

  "ht_weather_url": "https://api.openweathermap.org/data/2.5/weather",
  "ht_weather_key": "294a8f8a98d8f97ea64fb1c71dc4fb47",
  "ht_weather_host": "api.openweathermap.org",

  //"ApplicationInsights": {
  //  "ConnectionString": "InstrumentationKey=3f157b1e-3d27-43d6-ac00-637dfab0b420;IngestionEndpoint=https://australiaeast-0.in.applicationinsights.azure.com/"
  //},
  "AADClientId": "c345d48d-2909-4dc6-a787-be62ed78162c",
  "AADTenantId": "6b127268-dbaf-4ffc-a262-23fcb73619f9",
  "AADSecret": "",
  "AADResourceId": "api://c345d48d-2909-4dc6-a787-be62ed78162c",
  "AADInstance": "https://login.microsoftonline.com/",

  "HT_KV_URI": "https://ht-kv-aue-demo.vault.azure.net/",
  "HT_KV_ClientId": "",
  "HT_KV_Secret": "",
  "IotStorageAccountName": "htappuat",
  "IotStorageAccountKey": "****************************************************************************************",

  "JwtIssuer": "healthteam.com.au",
  "JwtAudience": "Audience1234",
  "JwtKey": "bsdfsd4435dxZfgfdhf7867037f361a4d351e7c0de65f0776bfc2f478ea8d312c763bb6caca"

}