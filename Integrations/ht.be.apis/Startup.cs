using ht.be.apis.Services;
using ht.common.backend.shared;
using ht.common.backend.shared.helpers;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Web;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.ApplicationInsights.DependencyCollector;
using ht.be.apis.Extensions;

namespace ht.be.apis
{
    public class Startup
    {
        public IConfiguration Configuration { get; }
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;

            try
            {
                ServicePointManager.Expect100Continue = true;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls
                                                       | SecurityProtocolType.Tls11
                                                       | SecurityProtocolType.Tls12
                                                       | SecurityProtocolType.Tls13;
                ServicePointManager.DefaultConnectionLimit = 800;

                Globals.Properties.Add("AcsConnStr", configuration["AcsConnStr"]);
                Globals.Properties.Add("AcsEndpointUrl", configuration["AcsEndpointUrl"]);
                Globals.Properties.Add("AcsCallbackUrl", configuration["acs-callback-url"]);
                Globals.Properties.Add("HtSBConnStr", configuration["HtSBConnStr"]);
                Globals.Properties.Add("WebHookQueue", configuration["WebHookQueue"]);
                Globals.Properties.Add("DBConstr", configuration["dbconstr"]);
                Globals.Properties.Add("pbi-config", configuration["pbi-config"]);

                Globals.Properties.Add("CustomAuthSigningKey", configuration["beapis-customauth-signingkey"]);
                Globals.Properties.Add("AppInsightsConstr", configuration["appinsights-constr"]);

                Globals.Properties.Add("HtBEAsyncUrl", configuration["ht-beasync-url"]);
                Globals.Properties["PortalUrl"] = configuration["portal-url"];
                Globals.Properties["MeetingUrl"] = configuration["MeetingUrl"];
                Globals.Properties["ResidentUrl"] = configuration["ResidentUrl"];
                Globals.Properties["APIMKey"] = configuration["apim-key"];
                Globals.Properties["APIMSendEmailURL"] = configuration["apim-sendemail-url"];
                Globals.Properties["APIMSendSmsURL"] = configuration["apim-sms-url"];

                Globals.Properties.Add("HtWeatherHost", configuration["ht-weather-host"]);
                Globals.Properties.Add("HtWeatherUrl", configuration["ht-weather-url"]);
                Globals.Properties.Add("HtWeatherKey", configuration["ht-weather-key"]);

                Globals.Properties.Add("HtServiceUserId", "10000001-1001-1001-1001-100000000001");
                Globals.Properties.Add("HealthTeamsEDI", configuration["health-teams-edi"]);

                Globals.HT_KV_URI = configuration["HT_KV_URI"];

                DBService.DBConstr = Globals.Properties["DBConstr"];

                IoTDeviceService.IoTConnStr = configuration["iot-admin-constr"];
                IoTDeviceService.IoTDevConnStr = configuration["iot-admin-devconstr"];

                //ACS Manager Settings
                AcsService.AcsConnStr = Globals.Properties["AcsConnStr"];
                AcsService.AcsNotificationUrl = Globals.Properties["AcsCallbackUrl"];
                AcsService.AcsEndpointUrl = Globals.Properties["AcsEndpointUrl"];

                WeatherService.Host = Globals.Properties["HtWeatherHost"];
                WeatherService.BaseUrl = Globals.Properties["HtWeatherUrl"];
                WeatherService.Key = Globals.Properties["HtWeatherKey"];

                //setup 
                string issuer = "https://www.healthteams.com.au";
                TokenService.Init(issuer, configuration["token-pubkey"], configuration["token-privkey"]);

                Globals.Properties["AdminAuthClientId"] = configuration["admin-auth-clientid"];
                Globals.Properties["AdminAuthTenantId"] = configuration["admin-auth-tenantid"];
                Globals.Properties["AdminAuthClientSecret"] = configuration["admin-auth-clientsecret"];
                Globals.Properties["AdminAuthClientIssuer"] = configuration["admin-auth-issuer"];

                GraphService.AppId = Globals.Properties["AdminAuthClientId"];
                GraphService.TenantId = Globals.Properties["AdminAuthTenantId"];
                GraphService.ClientSecret = Globals.Properties["AdminAuthClientSecret"];
                GraphService.Issuer = Globals.Properties["AdminAuthClientIssuer"];
                Globals.Properties["AuthAudience"] = configuration["auth-audience"]; //this gives the audience Id to validate the system token against.
                Globals.Properties["AuthAADIssuer"] = configuration["auth-aad-issuer"]; //this gives the audience Id to validate the system token against.

                //Credentials to be able to Call the APIM gateway
                Globals.Properties["APIMBaseUrl"] = configuration["apim-baseurl"]; // this has the trailing /
                Globals.Properties["APIMApiKey"] = configuration["apim-apikey"];
                Utils.APIMManager.baseUrl = configuration["apim-baseurl"];
                Utils.APIMManager.apikey = configuration["apim-apikey"];

                BlobHelper.acctName = configuration["IotStorageAccountName"];
                BlobHelper.acctKey = configuration["IotStorageAccountKey"];
                Globals.Properties["blobusersConns"] = configuration["blobusersConns"];
                Globals.Properties["blobusersurl"] = configuration["blobusersurl"];
                PaymentService.BaseUrl = configuration["medipass-baseurl"];
                PaymentService.BusinessApiKey = configuration["medipass-apikey"];
                PaymentService.Env = configuration["medipass-env"];
                PaymentService.AppId = configuration["medipass-appid"];

                AzureKeyvaultHelper.KeyVaultUrl = configuration["HT_KV_URI"];
                AzureKeyvaultHelper.TenantId = configuration["AADTenantId"];
                AzureKeyvaultHelper.ClientId = configuration["HT_KV_ClientId"];
                AzureKeyvaultHelper.ClientSecret = configuration["HT_KV_Secret"];

            }
            catch
            {

            }
        }




        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            try
            {

                services.Configure<FormOptions>(o =>
                {
                    o.ValueLengthLimit = int.MaxValue;
                    o.MultipartBodyLengthLimit = int.MaxValue;
                    o.MemoryBufferThreshold = int.MaxValue;
                });


                //services.AddMicrosoftIdentityWebAppAuthentication(Configuration);
                Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
                services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(opt =>
                {
                    opt.Audience = Configuration["AADResourceId"];
                    opt.Authority = $"{Configuration["AADInstance"]}{Configuration["AADTenantId"]}";
                });


                services.AddControllers()
                    .AddJsonOptions(options =>
                    {
                        options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                        options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
                        options.JsonSerializerOptions.Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
                    });

                services.AddSingleton<IConfiguration>(Configuration);
                services.AddScoped<AcsService>(sp => { return new AcsService(Globals.Properties["AcsConnStr"]); });
                services.AddScoped<TokenService>(sp => { return new TokenService(Globals.Properties["CustomAuthSigningKey"], 3600 * 24); }); //24 hour tokens
                services.AddScoped<MhrService>();
                services.AddScoped<WeatherService>();
                services.AddScoped<GraphService>();
                services.AddScoped<PaymentService>();
                services.AddScoped<SignalRService>();
                services.AddScoped<Hl7Service>();
                services.AddScoped<BlobHelper>();
                services.AddScoped<DBService>();
                services.AddScoped<EmailService>();
                services.AddScoped<SmsService>();
                services.AddScoped<PBiEmbedService>(sp => { return new PBiEmbedService(Globals.Properties["pbi-config"]); });

                services.AddScoped<IoTDeviceService>();
                services.AddScoped<PartnerService>();
                services.AddSwaggerGen(c =>
                {
                    c.SwaggerDoc("v1", new OpenApiInfo { Title = "ht.be.apis", Version = "v1" });
                });

                /* configure app insights through code */
                /*
                var aiOptions = new Microsoft.ApplicationInsights.AspNetCore.Extensions.ApplicationInsightsServiceOptions
                {
                    DeveloperMode = false,
                    EnableAdaptiveSampling = false,
                    EnableDependencyTrackingTelemetryModule = false,
                    EnableDebugLogger = false,
                    EnableQuickPulseMetricStream = false, //turns off live stream
                    EnableDiagnosticsTelemetryModule = false, //less info sent
                    EnableHeartbeat = false //no heartbeat - true/false
                };
                aiOptions.RequestCollectionOptions.TrackExceptions= true;
                services.AddApplicationInsightsTelemetry(aiOptions);
                */

                services.AddApplicationInsightsTelemetry(Globals.Properties["AppInsightsConstr"]);


                services.ConfigureTelemetryModule<DependencyTrackingTelemetryModule>((module, o) =>
                {
                    module.EnableSqlCommandTextInstrumentation = true;
                });

                services.AddTransient<ht.be.apis.Extensions.RequestBodyLoggingMiddleware>();
                services.AddTransient<ht.be.apis.Extensions.ResponseBodyLoggingMiddleware>();




            }
            catch
            {

            }

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            //#if DEBUG
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "ht.be.apis v1"));

            app.UseRequestBodyLogging();
            app.UseResponseBodyLogging();

            //#endif

            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
