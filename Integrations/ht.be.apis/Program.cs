using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Azure.Identity;
using Microsoft.Azure.Services.AppAuthentication;
using Microsoft.Azure.KeyVault;
using System.IO;
using Microsoft.Extensions.Configuration.AzureKeyVault;
using ht.be.apis.Utils;
using ht.common.backend.shared.helpers;

namespace ht.be.apis
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.ConfigureAppConfiguration((hostingContext, config) =>
                    {
                        var settings = config
                                       //.SetBasePath(Directory.GetCurrentDirectory())
                                       .AddEnvironmentVariables()
                                       .AddEnvironmentVariables(prefix: "HTBE:")
                                       .AddJsonFile("appsettings.local.json", optional: true, reloadOnChange: true)
                                       .Build();
                                
                        var azTokProvider = new AzureServiceTokenProvider();

                        // Load creds from app settings for non-azure environments
                        var kv_uri = settings["HT_KV_URI"];
                        var clientId = settings.GetValue<string>("HT_KV_ClientId", defaultValue: "");
                        var secret = settings.GetValue<string>("HT_KV_Secret", defaultValue: "");

                        // Creds for Azure environments
                        var creds = new DefaultAzureCredential();

                        if (Utils.Environment.IsInAzure)
                            config.AddAzureKeyVault(new Uri(kv_uri), creds, new Utils.PrefixKeyVaultSecretManager("htbe"));
                        else
                            config.AddAzureKeyVault(kv_uri, clientId, secret, new Utils.PrefixKeyVaultSecretManager("htbe"));

                    }).UseStartup<Startup>();
                });
    }
}
