{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:58450", "sslPort": 0}}, "profiles": {"ht.be.apis": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "https://localhost:5100;http://localhost:5101", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "HT_KV": "https://kv-ht-uat.vault.azure.net/"}, "dotnetRunMessages": true}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "HT_KV": "https://kv-ht-uat.vault.azure.net/"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "environmentVariables": {}, "publishAllPorts": true}}}