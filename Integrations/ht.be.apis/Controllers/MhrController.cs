﻿using ht.be.apis.models.Mhr;
using ht.be.apis.Services;
using ht.common.backend.shared.models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;


namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MhrController : ControllerBase
    {
        private MhrService _MyHealthRecordsvc;
        DBService _dbSvc;
        ILogger _logger;

        public MhrController(MhrService svc, DBService dBService, ILogger<MhrController> logger)
        {
            _MyHealthRecordsvc = svc;
            _dbSvc = dBService;
            _logger = logger;
        }

        [Route("GetMhrPortal")]
        [HttpPost]
        public async Task<IActionResult> GetMhrPortal(MhrPortalRequest req)
        {
            MhrPortalResponse resp = new MhrPortalResponse();
            resp = await _MyHealthRecordsvc.GetMhrPortal(req);
            return (new JsonResult(resp));
        }

        [Route("UpsertHPIOCertificate")]
        [HttpPost]
        public async Task<IActionResult> UpsertHPIOCertificate(UpsertHPIOCertificateRequest req)
        {
            var response = new baseResponse();
            response = await _MyHealthRecordsvc.UpsertHPIODetails(req);
            return (new JsonResult(response));
        }

        [HttpGet("GetFacilityMHRSetup/{ExFacilityId}")]
        public async Task<IActionResult> GetFacilityMHRSetup(string ExFacilityId)
        {
            MhrSetupResponse resp = new MhrSetupResponse();
            resp = await _MyHealthRecordsvc.GetMhrSetup(ExFacilityId);
            return (new JsonResult(resp));
        }

    }
}
