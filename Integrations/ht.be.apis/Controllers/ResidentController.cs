﻿using System;
using ht.be.apis.Extensions;
using ht.be.apis.Services;
using ht.be.apis.Utils;
using ht.common.backend.shared.helpers;
using ht.data.common.Dashboards;
using ht.data.common.partner;
using ht.data.common.Telehealth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using System.Collections.Generic;
using System.Threading.Tasks;
using ht.data.common.FacilitySetup;
using ht.data.common;
using ht.data.common.Users;
using ht.be.apis.models;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ResidentController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;

        private string _authAudienceId = null;
        public ResidentController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
        }


        [AllowAnonymous]
        [HttpPost("GetDashboard")]
        public async Task<IActionResult> GetDashboard([FromBody] GetResidentDashboardRequest req)

        {
            GetResidentDashboardResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Dashboard", json, jsonSecurity);

                br = HTJsonSerialiser.Deserialise<GetResidentDashboardResponse>(res);
                if (br?.surveys == null)
                    br.surveys = new List<data.common.Surveys.HTSurveySummary>();
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpPost("GetResidentPartnerEventDetails")]
        public async Task<IActionResult> GetResidentPartnerEventDetails([FromBody] GetResidentPartnerEventDetailsRequest req)

        {
            GetResidentPartnerEventDetailsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Partner_event_Details", json, jsonSecurity);

                br = HTJsonSerialiser.Deserialise<GetResidentPartnerEventDetailsResponse>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpPost("GetResidentPartnerMappingDetails")]
        public async Task<IActionResult> GetResidentPartnerMappingDetails([FromBody] GetPartnerIntegrationMappingDetailsRequest req)
        {
            GetPartnerIntegrationMappingDetailsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Partner_Mapping_Details", json, jsonSecurity);

                br.partnerIntegrationMappingDetails = HTJsonSerialiser.Deserialise<List<PartnerIntegrationMapping>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpGet("GetResidentOffline/{ExFacilityId}")]
        public async Task<IActionResult> GetResidentOffline(string ExFacilityId)

        {
            GetUsersSearchResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_resident_offline", ExFacilityId, jsonSecurity);
                if (res != null)
                    br.Results = HTJsonSerialiser.Deserialise<List<HealthTeamsUser>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [AllowAnonymous]
        [HttpGet("GetClientDiscoverForResident/{ExFacilityId}")]
        public async Task<IActionResult> GetClientDiscoverForResident(string ExFacilityId)
        {
            var residentHash = await _dbSvc.ExecSprocAsync("sp_resident_version_update", new Dictionary<string, string> { { "VersionName", ExFacilityId } });

            var resp = new DiscoveryResponse
            {
                Properties = new Dictionary<string, string> { 
                    { "ResidentHash", residentHash },
                    }
            };


            return (new JsonResult(resp));
        }

        [AllowAnonymous]
        [HttpGet("GetResidentDirectLink/{meetingCode}")]
        public async Task<IActionResult> GetResidentDirectLink(string meetingCode)

        {
            InviteUserOnCallResponse br = new();

            try
            {
                var jsonSecurity = "";//HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Resident_Direct_Link", meetingCode, jsonSecurity);

                br = HTJsonSerialiser.Deserialise<InviteUserOnCallResponse>(res);

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

    }
}
