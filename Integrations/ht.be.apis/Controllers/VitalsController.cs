﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.be.apis.Utils;
using ht.data.common.Users;
using System.Text.Json.Nodes;
using ht.data.common.Weather;
using System.Net.Http;
using System.Net.Http.Json;
using ht.be.apis.Extensions;
using QuickChart;
using ht.data.common.Vitals;
using ht.data.common.Surveys;
using ht.data.common.Tasks;
using Microsoft.ApplicationInsights;
using Microsoft.Extensions.Logging;

namespace ht.be.apis.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class VitalsController : ControllerBase
{

    DBService _dbSvc;
    TelemetryClient _telemetryClient;
    ILogger<VitalsController> _logger;

    private string _authAudienceId = null;
    private string _beasyncUrl = null;

    public VitalsController(DBService dbSvc, ILogger<VitalsController> logger, TelemetryClient temClient)
    {
        _dbSvc = dbSvc;
        _logger = logger;
        _telemetryClient = temClient;
    }

    [AllowAnonymous]

    [HttpGet("GetVitalsInformation")]
    public async Task<IActionResult> GetVitalsInformation()

    {
        GetVitalsInformationResponse br = new() { Status = "Success", VitalsInformation = new() };
        try
        {
            //var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

            //var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Vitals_GetInformation", String.Empty);
            br.VitalsInformation.Vitals = HTJsonSerialiser.Deserialise<List<VitalInformation>>(res);
            br.Status = "Success";
        }


        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("GetVitalsListForResidentGraphs/{exResidentId}")]
    public async Task<IActionResult> GetVitalsListForResidentGraphs(string exResidentId)
    {
        GetVitalsListResponse br = new();

        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Vital_list_for_Grpah_by_residentId", exResidentId, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetVitalsListResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]

    [HttpPost("SendErrorLogDetails")]
    public async Task<IActionResult> SendErrorLogDetails([FromBody] VitalsErrorLogDetailsRequest req)

    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            _logger.LogError("Vital upload Error:", Convert.ToString(json));
            br.Status = "Success";
            br.Message = "Errors reported successfully.";
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }
}
