﻿using ht.be.apis.Services;
using ht.common.backend.shared;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ht.be.apis.Controllers
{
    [Route("/")]
    [ApiController]

    public class ApiHealthcheckController : ControllerBase
    {
       
        public ApiHealthcheckController()
        {
            
        }


        [AllowAnonymous]
        [Route("/")]
        [HttpGet()]
        [HttpPost]
        
        public IActionResult Get()

        {
            return new OkResult();
            
        }

    }
}
