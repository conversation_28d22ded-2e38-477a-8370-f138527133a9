﻿using ht.be.apis.models;
using ht.be.apis.Services;
using ht.be.apis.Utils;
using ht.common.backend.shared;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class GenericController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;

        private string _authAudienceId = null;
        public GenericController(DBService dbSvc, TokenService tkSvc,AcsService acsSvc)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = Globals.Properties["AuthAudience"];
        }

        [AllowAnonymous]
       
      
        [HttpPost("GenericUpser")]
        public async Task<IActionResult> GenericUpsert([FromBody] object req) //object used here - as we need to define the received json based request
           
        {
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_GENERIC_Upsert", json);
            var resp = new baseResponse
            {
                Message = res
            };

            return (new JsonResult(resp));
        }


    }
}
