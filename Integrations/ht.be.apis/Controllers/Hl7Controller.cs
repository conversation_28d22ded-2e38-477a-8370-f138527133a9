﻿using ht.be.apis.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System;
using System.Threading.Tasks;
using ht.be.apis.Utils;
using ht.common.backend.shared.models;
using System.Text.Json;
using System.Text;
using ht.data.common.Telehealth;
using ht.be.apis.Extensions;
using System.IO;
using System.Net;
using ht.data.common;
using System.Net.Http.Headers;
using ht.common.backend.shared.models.hl7;
using PeterPiper.Hl7.V2.Model;
using System.Collections.Generic;
using ht.data.common.Users;
using static PeterPiper.Hl7.V2.Schema.XmlParser.HL7v2Xsd;
using Microsoft.Graph.Models;
using Newtonsoft.Json.Linq;
using System.Linq;
using ht.common.backend.shared.classes;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class Hl7Controller : ControllerBase
    {
        private readonly Hl7Service _hl7Service;
        private readonly DBService _dbSvc;
        private readonly IConfiguration _conf;
        private string _beasyncUrl = null;

        public Hl7Controller(Hl7Service hl7Service, DBService dbSvc, IConfiguration configuration)
        {
            _beasyncUrl = common.backend.shared.Globals.Properties["HtBEAsyncUrl"];
            this._hl7Service = hl7Service;
            this._dbSvc = dbSvc;
            this._conf = configuration;

        }
        

        [HttpPost("CreateMessage")]
        public async Task<IActionResult> CreateMessage(Hl7Request req)
        {
            data.common.baseResponse br = new();

            try
            {
                //resident & doctor details
                var residentData = await GetResidentDetails(req);
                var userDetails = await GetUserDetails(req);


                // Generate and send HL7 message to HealthLink
                var hl7Messagse = await _hl7Service.GenerateHl7(userDetails, residentData.Resident, req);
                if (hl7Messagse.Count>0)
                {
                    await SendHl7Message(hl7Messagse, req);
                    br.Status = "HL7 file sent to HealthLink successfully";
                    List<string> messageIds = new();
                    hl7Messagse.Values.ToList().ForEach((m) =>
                    {
                       messageIds.Add( Creator.Message(m).Segment("MSH").Field(10).AsString);
                    });

                    br.Message= JsonSerializer.Serialize(new { messageId = messageIds });
                     
                }

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return Ok(br);
        }

        [HttpPost("CreateAmendedMessage")]
        public async Task<IActionResult> CreateAmendedMessage(Hl7Request req)
        {
            data.common.baseResponse br = new();

            try
            {

                req.PdfEncode = await GetPdfEncode(req);

                //resident details
                var residentData = await GetResidentDetails(req);

                if (residentData == null)
                {
                    throw new Exception("resident data not found");
                }
                req.ReferToProvider = new()
                {
                    FamilyNameLastNamePrefix = "Doctor",
                    GivenName = "Demo",
                    Prefix = "Dr",
                };
                req.CopyToProvider = null;
                req.IsAmended = true;
                req.PdfEncode = await GetPdfEncode(req);
                var hl7 = _hl7Service.GenerateHl7Amend(residentData.Resident, req);
                if (!string.IsNullOrEmpty(hl7))
                {
                    //await SendHl7Message(new() { hl7 }, req);
                    br.Status = "HL7 file sent to HealthLink successfully";
                    br.Message = Creator.Message(hl7).Segment("MSH").AsString;
                }
                br.Status = "HL7 file sent to HealthLink successfully";
                br.Message = Creator.Message(hl7).Segment("MSH").AsString;



            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return Ok(br);
        }
        private async Task SendHl7Message(Dictionary<string, string> hl7, Hl7Request req)
        {
            foreach(var msg in hl7) {
                await _hl7Service.SendFiletoHealthLink(msg.Value,msg.Key, req, $"{Guid.NewGuid().ToString()}");

            }
            //send original message

            #region copy-to
            //send copy-to message
            //if (req.CopyToProvider != null)
            //{
            //    await SendCopyToMessage(residentData.Resident, req);
            //}
            #endregion
        }
        async Task SendCopyToMessage(ResidentUser details, Hl7Request req)
        {
            req.IsAmended = false;
            req.CopyToProvider = new()
            {
                FamilyNameLastNamePrefix = "Doctor",
                GivenName = "Demo",
                Prefix = "Dr",
            };
            req.ReferToProvider = new()
            {
                FamilyNameLastNamePrefix = "Provider",
                GivenName = "Test",
                Prefix = "Dr"
            };
            req.PdfEncode = await GetPdfEncode(req);

            var hl7Copy = _hl7Service.GenerateHl7CopyTo(details, req);
            if (!string.IsNullOrEmpty(hl7Copy))
            {
                string fileName = $"{Guid.NewGuid().ToString()}";
                //await _hl7Service.SendFiletoHealthLink(hl7Copy, req, fileName);
            }
        }

        async Task SendAmendedMessage(ResidentUser details, Hl7Request req)
        {
            req.ReferToProvider = new()
            {
                FamilyNameLastNamePrefix = "Doctor",
                GivenName = "Demo",
                Prefix = "Dr",
            };
            req.CopyToProvider = null;
            req.IsAmended = true;
            req.PdfEncode = await GetPdfEncode(req);
            var hl7Amend = _hl7Service.GenerateHl7Amend(details, req);

            if (!string.IsNullOrEmpty(hl7Amend))
            {
                string fileName = $"{Guid.NewGuid().ToString()}";
                // await _hl7Service.SendFiletoHealthLink(hl7Amend, req, fileName);
            }
        }

        [HttpGet("GetHl7Messages/{exFacilityId}")]
        public async Task<IActionResult> GetHl7Messages(string exFacilityId)
        {
            Hl7Response response = new();

            try
            {
                if (string.IsNullOrEmpty(exFacilityId))
                    return BadRequest();

                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var result = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Hl7Messages_List", exFacilityId);
                response.Hl7Data = HTJsonSerialiser.Deserialise<List<Hl7Data>>(result);

                if (response.Hl7Data.Count == 0)
                {
                    return NotFound();
                }
            }
            catch (Exception ex)
            {

                response.FromException(ex);
            }

            return (new JsonResult(response));
        }

        [HttpGet("GetHl7Acknowledgment/{messageId}")]
        public async Task<IActionResult> GetHl7Acknowledgment(string messageId)
        {
            Hl7Response response = new();

            try
            {
                if (string.IsNullOrEmpty(messageId))
                    return BadRequest();

                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_HL7_acknowledgment_by_MessageId", messageId);
                response.HL7Acknowledgment = HTJsonSerialiser.Deserialise<HL7Acknowledgment>(res);

                if (response == null)
                {
                    return NotFound();
                }

            }
            catch (Exception ex)
            {

                response.FromException(ex);
            }
            return (new JsonResult(response));
        }

        private async Task<string> GetPdfEncode(Hl7Request req)
        {
            var beUrl = "http://localhost:7071/api/GenerateHl7Report";
            byte[] pdfResponse = null;
            try
            {

                if (req.ExNoteIds.Count == 0 && !req.IsAllProgressNotes)
                    throw new Exception("No Noteid found");

                using (var hc = new HttpClient())
                {
                    var json = HTJsonSerialiser.Serialise(req);

                    hc.DefaultRequestHeaders.Add("x-functions-key", _conf["function-key"]);
                    var resp = await hc.PostAsync(beUrl, new StringContent(JsonSerializer.Serialize(req), Encoding.UTF8, "application/json"));
                    resp.EnsureSuccessStatusCode();
                    pdfResponse = await resp.Content.ReadAsByteArrayAsync();
                    return Convert.ToBase64String(pdfResponse);
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private async Task<GetTeleHealthDetailsResponse> GetResidentDetails(Hl7Request request)
        {
            var req = new GetTeleHealthDetailsRequest()
            {
                CurrentDate = DateTime.Now,
                ExFacilityId = request.ExFacilityId,
                Role = "nurse",
                ExResidentId = request.ExResidentId,
                TabName = "summary"

            };
            GetTeleHealthDetailsResponse br = new GetTeleHealthDetailsResponse { Status = "Success" };

            if (!Guid.TryParse(req.ExResidentId, out Guid _result))
                throw new NullReferenceException("ERROR: Please supply a valid resident Id");
            if (string.IsNullOrEmpty(req.Role) || string.IsNullOrEmpty(req.TabName))
                throw new NullReferenceException("ERROR: Missing required fields - role + tabName");


            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Details", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetTeleHealthDetailsResponse>(res);
            br.Status = "Success";
            if (br == null)
            {
                throw new Exception("resident data not found");
            }


            return br;
        }
        async Task<HealthTeamsUser> GetUserDetails(Hl7Request req)
        {


            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                string userExId = JObject.Parse(jsonSecurity)["userExId"].Value<string>();
                GetUserProfileResponse br = new GetUserProfileResponse();
                var json = "{ \"userExternalId\":\"" + userExId + "\"}";

                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Users_Get_UserProfile_ByExternalId", json, jsonSecurity);
                br.User = HTJsonSerialiser.Deserialise<HealthTeamsUser>(res);
                if (br.User == null)
                {
                    throw new Exception("user Details not found");
                }
                return br.User;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }
}
