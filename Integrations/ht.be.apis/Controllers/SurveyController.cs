﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Users;
using ht.be.apis.Utils;
using ht.be.apis.Extensions;
using ht.data.common.Surveys;

namespace ht.be.apis.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class SurveyController : ControllerBase
{

    DBService _dbSvc;
    TokenService _tokenService;

    private string _authAudienceId = null;
    public SurveyController(DBService dbSvc, TokenService tkSvc)
    {
        _dbSvc = dbSvc;
        _tokenService = tkSvc;
    }

    [AllowAnonymous]
    
    [HttpPost("GetSurveyListForFacility")]
    public async Task<IActionResult> GetSurveyListForFacility([FromBody] GetSurveyListRequest req)

    {
        GetSurveyListResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Survey_GetList_For_Facility", req.ExFacilityId, jsonSecurity);
            br.Surveys = HTJsonSerialiser.Deserialise<List<HTSurveySummary>>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("GetSurvey")]
    public async Task<IActionResult> GetSurvey([FromBody] GetSurveyRequest req)

    {
        GetSurveyResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Survey_GetQuestions_ById", req.ExSurveyId, jsonSecurity);
            br.Survey = HTJsonSerialiser.Deserialise<HTSurvey>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }


    [AllowAnonymous]

    [HttpPost("UpsertSurveyForResident")]
    public async Task<IActionResult> UpsertSurveyForResident([FromBody] UpsertSurveyRequest req)

    {
        UpsertSurveyResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Survey_Upsert_For_Resident", json, jsonSecurity);
            br.Message = res;
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("UpsertSurveyForFacility")]
    public async Task<IActionResult> UpsertSurveyForFacility([FromBody] UpsertSurveyRequest req)

    {
        UpsertSurveyResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Survey_Upsert_For_Facility", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<UpsertSurveyResponse>(res); 
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GetSurveyResultsListForResident")]
    public async Task<IActionResult> GetSurveyResultsListForResident([FromBody] GetSurveyListRequest req)
    {
        GetSurveyResultsListResponse br = new ();
       
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Survey_Results_GetList_For_Resident", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetSurveyResultsListResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]
    [HttpGet("GetSurveyResult/{exSurveyResponseId}")]
    public async Task<IActionResult> GetSurveyResult(string exSurveyResponseId)
    {
        GetSurveyResponse br = new();

        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Survey_Get_Result_By_surveyId", exSurveyResponseId, jsonSecurity);
            br.Survey = HTJsonSerialiser.Deserialise<HTSurvey>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]
    [HttpPost("GetSurveyResultsListForFacility")]
    public async Task<IActionResult> GetSurveyResultsListForFacility([FromBody] GetSurveyListRequest req)
    {
        GetSurveyResultsListResponse br = new();

        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("sp_Survey_Results_GetList_For_Facility", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetSurveyResultsListResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]
    [HttpGet("GetQuestionList/{exFacilityId}")]
    public async Task<IActionResult> GetQuestionList(string exFacilityId)
    {
        GetQuestionListResponse br = new();

        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Survey_GetQuestionList_For_Facility", exFacilityId, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetQuestionListResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]
    [HttpGet("GetAnswerList/{exFacilityId}")]
    public async Task<IActionResult> GetAnswerList(string exFacilityId)
    {
        GetAnswerListResponse br = new();

        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Survey_GetAnswerList_For_Facility", exFacilityId, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetAnswerListResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }
    [AllowAnonymous]
    [HttpPost("UpsertQuestionForFacility")]
    public async Task<IActionResult> UpsertQuestionForFacility([FromBody] UpsertQuestionRequest req)

    {
        UpsertQuestionResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Survey_Upsert_question", json, jsonSecurity);
            br.question = HTJsonSerialiser.Deserialise<Questionsjson>(res); 
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("UpdateSurveyStatus")]
    public async Task<IActionResult> UpdateSurveyStatus([FromBody] UpdateSurveyStatusRequest req)

    {
        UpdateSurveyStatusResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Survey_Update_Survey_Status", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<UpdateSurveyStatusResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

}



