﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Users;
using ht.be.apis.Utils;
using ht.be.apis.Extensions;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DoctorController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;

        private string _authAudienceId = null;
        public DoctorController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
        }

        [AllowAnonymous]

        [HttpPost("GetDashboard")]
        public async Task<IActionResult> GetDashboard([FromBody] GetDoctorDashboardRequest req)

        {
            GetDoctorDashboardResponse br = new GetDoctorDashboardResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Doctor_Dashboard", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<GetDoctorDashboardResponse>(res);
                if (br.Appointments == null)
                    br.Appointments = new List<data.common.Tasks.HealthTeamsTask>();
                if (br.Billsessions == null)
                    br.Billsessions = "0";
                if (br.NewAppointments == null)
                    br.NewAppointments = "0";
            }
            catch (Exception ex)
            {
                br.FromException(ex);

            }
            return (new JsonResult(br));
        }
    }


}

