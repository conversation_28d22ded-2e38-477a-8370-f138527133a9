﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;

using ht.be.apis.Utils;
using Microsoft.Extensions.Logging;
using Microsoft.ApplicationInsights;
using ht.be.apis.Extensions;
using static ht.be.apis.Services.EmailService;
using ht.data.common.Shared;
using ht.data.common.Users;
using static ht.be.apis.models.LoginResponse;
using Newtonsoft.Json;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using ht.common.backend.shared.models.security;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.Extensions.Configuration;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class TaskController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;
        TelemetryClient _telemetryClient;
        ILogger<TaskController> _logger;
        EmailService _emailSerive;

        private readonly IConfiguration _configuration;
        private string _meetingUrl = string.Empty;
        private string _authAudienceId = null;
        private string _portalUrl = null;
        PartnerService _partnerSerive;
        private readonly SmsService _smsService;

        public TaskController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc,
                                ILogger<TaskController> logger, TelemetryClient temClient, EmailService emailSvc, PartnerService partnerSerive, SmsService smsService, IConfiguration configuration)
        {
            _configuration = configuration;
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
            _dbSvc._logger = logger;
            _dbSvc._telemetryClient = temClient;
            _portalUrl = common.backend.shared.Globals.Properties["PortalUrl"];
            _emailSerive = emailSvc;
            _meetingUrl = common.backend.shared.Globals.Properties["MeetingUrl"];
            _partnerSerive = partnerSerive;
            _smsService = smsService;
        }

        [AllowAnonymous]

        [HttpPost("GetTasksList")]
        public async Task<IActionResult> GetTasksList([FromBody] GetTaskListRequest req)
        {
            GetTaskSearchResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                _telemetryClient?.TrackEvent("GetTasksList", new Dictionary<string, string> { { "call", json } });
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Task_List", json, jsonSecurity);
                br.Tasks = HTJsonSerialiser.Deserialise<List<HealthTeamsTask>>(res);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));

        }

        [AllowAnonymous]
        [HttpGet("GetTask/{ExTaskId}")]
        [ProducesResponseType(200, Type = typeof(GetTaskSearchResponse))]
        public async Task<IActionResult> GetTask(string ExTaskId)
        {
            GetTaskSearchResponse br = new();
            try
            {

                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Task_by_TaskId", ExTaskId, jsonSecurity);
                var resp = new GetTaskSearchResponse
                {
                    Tasks = HTJsonSerialiser.Deserialise<List<HealthTeamsTask>>(res)
                };

                return (new JsonResult(resp));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [AllowAnonymous]

        [HttpPost("UpsertTask")]
        public async Task<IActionResult> UpsertTask([FromBody] UpsertTaskRequest req)
        {
            UpsertTaskResponse br = new();
            GetMeetingDetailsForUserResponse mds = new();
            GetMeetingDetailsForUserRequest mreqs = new();
            var brEmailTemplate = new SharedListItem();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Task", json, jsonSecurity);
                var obj = HTJsonSerialiser.Deserialise<UpsertTaskResponse>(res);
                br.ExTaskId = obj?.ExTaskId;
                br.AcsChatThreadId = obj?.AcsChatThreadId;

                #region TLW Integration
                if (IsTaskValidForTLW(br, req))
                {
                    var partnerMappingDetails = await _partnerSerive.GetFacilityPartnerMapping(req.Task.ExFacilityId, req.Task.ExResidentId);
                    //send task to TLW
                    if (_partnerSerive.IsPartnerTheLookOut(partnerMappingDetails))
                    {
                        await _partnerSerive.SendTaskToLookOut(jsonSecurity, req.Task.ExResidentId, req.Task.AssignedToId, br.ExTaskId, partnerMappingDetails);
                    }
                }
                #endregion
                //check to see if we need to create an ACS chat thread Id + save against the meeting.
                if (req.Task?.TaskType == TaskTypes.Telehealth && string.IsNullOrEmpty(br.AcsChatThreadId))
                {
                    br.AcsChatThreadId = await _acsService.CreateAndStoreNewChatThreadId(req?.Task?.TaskName, br.ExTaskId, _dbSvc);
                    ///Send an email to family member for the virtual consult.

                    mreqs.ExTaskId = br.ExTaskId;
                    json = HTJsonSerialiser.Serialise(mreqs);
                    var mres = await _dbSvc.ExecSprocWithJsonAsync("sp_Get_User_And_Meeting_Details", json, jsonSecurity);
                    var mobj = HTJsonSerialiser.Deserialise<List<GetMeetingDetailsForUserResponse>>(mres);
                    GetMeetingDetailsForUserResponse familyuser = new();
                    familyuser = mobj.FirstOrDefault(i => i.Role == "family");


                    if (familyuser != null && !string.IsNullOrEmpty(familyuser.Email))// check if family member is available for the resident before sending an email.
                    {
                        var InviteLink = _portalUrl;
                        brEmailTemplate = await _emailSerive.GetEmailTemplate("InviteOnTeleHealth");
                        string emailTemplate = string.Format(brEmailTemplate.Description, familyuser.TaskDateTime?.ToString("dd/MM/yyyy hh:mm tt"), familyuser?.FirstName, familyuser?.InvitedBy, familyuser.TaskDateTime?.ToString("dd/MM/yyyy hh:mm tt"), familyuser?.ResidentName, familyuser?.FacName, InviteLink);
                        var result = await _emailSerive.SendEmail(emailTemplate, familyuser?.Email, brEmailTemplate.Category, _portalUrl);
                    }

                    GetMeetingDetailsForUserResponse doctoruser = new();
                    doctoruser = mobj.FirstOrDefault(i => i.Role.ToLower() == "doctor");

                    if (doctoruser != null && !string.IsNullOrEmpty(doctoruser.Email))// check if family member is available for the resident before sending an email.
                    {
                        InviteUserOnCallRequest inviteuser = new();
                        inviteuser.FirstName = doctoruser.FirstName;
                        inviteuser.LastName = doctoruser.LastName;
                        inviteuser.Email = doctoruser.Email;
                        inviteuser.Role = doctoruser.Role;
                        inviteuser.ExTaskId = br.ExTaskId;
                        inviteuser.ExMeetingId = doctoruser.ExMeetingId;
                        var inviteResp = InviteUserOnCall(inviteuser);
                    }
                }
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }
        private bool IsTaskValidForTLW(UpsertTaskResponse br, UpsertTaskRequest req)
        {
            return !string.IsNullOrEmpty(br.ExTaskId)
                && req.Task?.TaskType == TaskTypes.CollectVitals
                && !string.IsNullOrEmpty(req.Task?.AssignedToId);
        }
        private async Task<IActionResult> InviteUserOnCall(InviteUserOnCallRequest req)
        {
            var brGuestTokenRequest = new GuestTokenRequest();
            var brEmailTemplate = new SharedListItem();
            var br = new InviteUserOnCallResponse();
            var json = HTJsonSerialiser.Serialise(req);
            string meetingUrl = string.Empty;
            try
            {
                string audience = "https://bffapis.healthteams.com.au";

                // The withholdNotifications header can be used to stop the sending to email and sms
                var withholdNotification = HttpContext.Request.Headers["withholdNotifications"].FirstOrDefault();
                bool sendNotifications = true;
                if (!string.IsNullOrEmpty(withholdNotification) && withholdNotification.ToLower() == "true")
                    sendNotifications = false;

                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Invite_User_On_Call", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<InviteUserOnCallResponse>(res);

                if (br?.Status != "Error")
                {

                    var logingResponse = new LoginResponse();
                    var rs = new List<LoginResponse.UserFacilityRole>();

                    var roles = new UserFacilityRole();
                    roles.Role = br.Role + ":" + req.ExFacilityId;
                    rs.Add(roles);

                    var lst = rs?.Select(x => x.Role.ToString()).ToList<string>(); // br.Roles?.Select(x => x.Role.ToString()).ToList<string>();
                    string userRoles = br.InviteLink.ToString();
                    //generate token
                    var customClaims = new List<Claim>
                    {
                        new Claim("FirstName",br.FirstName??""),
                        new Claim("LastName",br.LastName??""),
                        new Claim("Email",br.Email??"")
                    };

                    brGuestTokenRequest.ExGuestUserId = br.ExGuestUserId;
                    brGuestTokenRequest.IdName = "App";
                    brGuestTokenRequest.UserRoles = userRoles;
                    brGuestTokenRequest.TaskDateTimeUtc = br.TaskDateTimeUTC;
                    brGuestTokenRequest.Roles = lst;
                    brGuestTokenRequest.CustomClaims = customClaims;
                    var token = ReturnGuestUserSecurityToken(brGuestTokenRequest);
                    string MeetingCode = await _emailSerive.RandomString(6);
                    br.InviteLink = _portalUrl + "telehealth?taskid=" + br.ExTaskId + "&id_token=" + token + "&type=external";
                    _meetingUrl = _meetingUrl + "/" + MeetingCode;
                    br.MeetingCode = MeetingCode;

                    //Update actual meeting link in the database.
                    var jsonr = HTJsonSerialiser.Serialise(br);
                    var resp = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_Meeting_Link", jsonr, jsonSecurity);

                    if (sendNotifications)
                    {
                        brEmailTemplate = await _emailSerive.GetEmailTemplate("InviteOnTeleHealth");
                        string emailTemplate = string.Format(brEmailTemplate.Description, br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt"), req.FirstName, br.InvitedBy, br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt"), br.ResidentName, br.FacName, _meetingUrl);
                        var result = await _emailSerive.SendEmail(emailTemplate, br.Email, brEmailTemplate.Category, _meetingUrl);

                        if (!string.IsNullOrEmpty(br.Mobile))
                        {
                            if (!br.Mobile.StartsWith("+"))
                                br.Mobile = "+" + br.Mobile;
                            var smsNum = new PhoneNumbers.PhoneNumber();
                            var phUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
                            try
                            {
                                smsNum = phUtil.Parse(br.Mobile, null);
                            }
                            catch (Exception ex)
                            {
                                br.Message = Convert.ToString(ex.InnerException);
                            }

                            if (smsNum.HasCountryCode && smsNum.HasNationalNumber)
                            {
                                StringBuilder sb = new StringBuilder();
                                // sb.AppendLine($"HealthTeams: Virtual Consult Invitation on {br.UserTaskDateTimeUTC}");
                                // sb.AppendLine($"");
                                sb.AppendLine($"Hello {req.FirstName}");
                                sb.AppendLine($"");
                                sb.AppendLine($"{br.InvitedBy} has invited you to join the virtual consult scheduled on {br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt")} for the resident {br.ResidentName} from facility {br.FacName}.");
                                sb.AppendLine($"");
                                sb.AppendLine("Please click the following link to join the consult.\r\n");
                                sb.AppendLine($"{_meetingUrl}");

                                _smsService.SendToSms(br.Mobile, sb.ToString());
                            }
                        }
                    }

                    // Send short meeting link to front end 
                    br.InviteLink = _meetingUrl;
                }
                return (new JsonResult(br));
            }

            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        public string ReturnGuestUserSecurityToken(GuestTokenRequest brGuestTokenRequest)
        {

            customJWTToken res = null;
            try
            {

                var dt = DateTime.UtcNow;
                var expDt = dt.AddHours(12);

                if (brGuestTokenRequest.TaskDateTimeUtc > DateTime.UtcNow)
                {
                    dt = brGuestTokenRequest.TaskDateTimeUtc.AddMinutes(-10);
                    expDt = brGuestTokenRequest.TaskDateTimeUtc.AddHours(12);
                }

                var unixTimeSeconds = new DateTimeOffset(dt).ToUnixTimeSeconds();
                var issuer = _configuration["JwtIssuer"];
                var audience = _configuration["JwtAudience"];
                var key = Encoding.UTF8.GetBytes(_configuration["JwtKey"]);
                var signingCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha512Signature);

                var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Iat, unixTimeSeconds.ToString(), ClaimValueTypes.Integer64),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(ClaimTypes.NameIdentifier, brGuestTokenRequest.IdName),
            new Claim("ht_roles", Convert.ToBase64String(Encoding.UTF8.GetBytes(brGuestTokenRequest.UserRoles)))
        };

                if (brGuestTokenRequest.CustomClaims != null)
                    claims.AddRange(brGuestTokenRequest.CustomClaims);

                if (brGuestTokenRequest.Roles != null)
                    brGuestTokenRequest.Roles.ForEach(r => claims.Add(new Claim(ClaimTypes.Role, r)));

                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(claims),
                    Expires = expDt,
                    Issuer = issuer,
                    Audience = audience,
                    SigningCredentials = signingCredentials
                };

                var tokenHandler = new JwtSecurityTokenHandler();
                var token = tokenHandler.CreateToken(tokenDescriptor);
                var jwtToken = tokenHandler.WriteToken(token);
                return jwtToken;
            }
            catch
            {
                throw;
            }
        }


        [AllowAnonymous]

        [HttpGet("GetChatThreadIdForTask/{exTaskId}")]
        public async Task<IActionResult> GetChatThreadIdForTask(string exTaskId)

        {
            UpsertTaskResponse br = new();
            try
            {
                if (string.IsNullOrEmpty(exTaskId))
                    throw new ArgumentNullException("ERROR: exTaskId cannot be null");
                br.AcsChatThreadId = await _acsService.CreateAndStoreNewChatThreadId("Test from Server Side Code", exTaskId, _dbSvc);

            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }


        [AllowAnonymous]

        [HttpPost("GetTasksSearch")]
        public async Task<IActionResult> GetTasksSearch([FromBody] GetTasksSearchRequest req)

        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Tasks_Search", json, jsonSecurity);
            var resp = new baseResponse
            {
                Message = res
            };

            return (new JsonResult(resp));
        }

        [AllowAnonymous]
        [HttpPost("GetVitalsList")]
        public async Task<IActionResult> GetVitalsList([FromBody] GetVitalsListRequest req)

        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Vitals_List", json, jsonSecurity);
            var resp = new GetVitalsListResponse
            {
                Vitals = HTJsonSerialiser.Deserialise<List<string>>(res)
            };

            return (new JsonResult(resp));
        }

        [AllowAnonymous]
        [HttpGet("DeleteTask/{ExTaskId}")]
        public async Task<IActionResult> DeleteTask(string ExTaskId)
        {
            UpsertTaskResponse br = new();
            try
            {

                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Delete_Task_by_TaskId", ExTaskId, jsonSecurity);
                br.ExTaskId = res;

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]

        [HttpPost("GetUnassignedTasksList")]
        public async Task<IActionResult> GetUnassignedTasksList([FromBody] GetUnAssignedTaskListRequest req)
        {
            GetTaskSearchResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                _telemetryClient?.TrackEvent("GetUnassignedTasksList", new Dictionary<string, string> { { "call", json } });
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_UnassignedTask_List", json, jsonSecurity);
                br.Tasks = HTJsonSerialiser.Deserialise<List<HealthTeamsTask>>(res);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [AllowAnonymous]

        [HttpPost("AssignTasks")]
        public async Task<IActionResult> AssignTasks([FromBody] AssignTasksRequest req)
        {
            baseResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Assign_Tasks", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<baseResponse>(res);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));

        }

        [AllowAnonymous]

        [HttpPost("UpdateTasksStatus")]
        public async Task<IActionResult> UpdateTasksStatus([FromBody] UpdateTasksStatusRequest req)
        {
            baseResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_Tasks_Status", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<baseResponse>(res);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }


        [AllowAnonymous]

        [HttpPost("UpsertAppointmentRequest")]
        public async Task<IActionResult> UpsertAppointmentRequest([FromBody] UpsertAppointmentRequest req)

        {
            GetAppointmentRequestSearchResponse br = new();
            AppointmentRequest appt = new();
            var brEmailTemplate = new SharedListItem();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Appointment_Request", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<GetAppointmentRequestSearchResponse>(res);
                if (br.Requests.Count > 0)
                {
                    appt = br.Requests.FirstOrDefault();
                }

                //check to see if we need to create an ACS chat thread Id + save against the meeting.
                if (br?.Status == "Success")
                {
                    //Get fac admin users to send and request email.
                    GetUsersSearchBriefResponse userresp = new() { Status = "Success" };
                    try
                    {
                        var userreq = "{\"role\":\"Facility\",\"searchText\":\"\",\"exFacilityId\":\"" + req.ExFacilityId + "\"}";
                        var userres = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_UsersBrief_Search", userreq, jsonSecurity);
                        userresp.Users = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<List<HTListUser>>(userres) : new List<HTListUser>());
                    }
                    catch (Exception ex)
                    {
                        br.FromException(ex);
                    }
                    if (userresp?.Users?.Count > 0)
                    {
                        for (int i = 0; i < userresp.Users.Count; i++)
                        {
                            if (!string.IsNullOrEmpty(userresp.Users[i].Email))// check if family member is available for the resident before sending an email.
                            {
                                brEmailTemplate = await _emailSerive.GetEmailTemplate("AppointmentRequest");
                                string emailTemplate = string.Format(brEmailTemplate.Description, appt?.ResidentName, userresp.Users[i]?.FullName, appt?.ResidentName, appt?.FacilityName, appt?.TaskType, appt?.TaskDateTimeLocal, appt?.TaskDescription);
                                var result = await _emailSerive.SendEmail(emailTemplate, userresp.Users[i]?.Email, brEmailTemplate.Category, _portalUrl);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }


        [AllowAnonymous]
        [HttpPost("GetAppointmentRequestList")]
        public async Task<IActionResult> GetAppointmentRequestList([FromBody] GetAppointmentRequestSearchRequest req)
        {
            GetAppointmentRequestSearchResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Appointment_Request_list", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<GetAppointmentRequestSearchResponse>(res);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));

        }

        [AllowAnonymous]
        [HttpPost("AcknowledgeAppointmentRequests")]
        public async Task<IActionResult> AcknowledgeAppointmentRequests([FromBody] AckAppointmentRequest req)
        {
            baseResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Acknowledg_Appointment_Requests", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<baseResponse>(res);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }
    }
}
