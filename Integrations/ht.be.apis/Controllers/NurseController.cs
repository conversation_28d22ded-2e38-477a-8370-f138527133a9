﻿using ht.be.apis.Extensions;
using ht.be.apis.Services;
using ht.be.apis.Utils;
using ht.common.backend.shared.helpers;
using ht.data.common.Dashboards;
using ht.data.common.Users;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class NurseController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;

        private string _authAudienceId = null;
        public NurseController(DBService dbSvc, TokenService tkSvc,AcsService acsSvc)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
        }

        [AllowAnonymous]      
      
        [HttpPost("GetDashboard")]
        public async Task<IActionResult> GetDashboard([FromBody] GetNurseDashboardRequest req) 
        {
            GetNurseDashboardResponse br = new GetNurseDashboardResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Nurse_Dashboard", json,jsonSecurity);
                br = HTJsonSerialiser.Deserialise<GetNurseDashboardResponse>(res);
                br.NoofNewTasks = br?.NewlyAssigned?.Count();
                if (br.Tasks == null)
                    br.Tasks = new List<data.common.Tasks.HealthTeamsTask>();
                if (br.ConnectedFacilities == null)
                    br.ConnectedFacilities = new List<Facility>();
                if (br.RecentConnectionRequests == null)
                    br.RecentConnectionRequests = new List<string>();
                if (br.UnassignedTasks == null)
                    br.UnassignedTasks = new List<data.common.Tasks.HealthTeamsTask>();
                if (br.Onlineusers == null)
                    br.Onlineusers = new List<data.common.Users.HTListUser>();
                if (br.NewlyAssigned == null)
                    br.NewlyAssigned = new List<data.common.Users.ResidentUser>();
            }
            catch(Exception ex)
            {
                br.FromException(ex);
               
            }
            return (new JsonResult(br));
        }


    }
}
