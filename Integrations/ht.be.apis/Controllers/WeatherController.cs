﻿

using ht.be.apis.Services;
using ht.be.apis.Utils;
using ht.common.backend.shared.helpers;
using ht.data.common.Weather;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace ht.be.apis.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class WeatherController : ControllerBase
{

    DBService _dbSvc;
    TokenService _tokenService;
    WeatherService _weatherService;
   

    private string _authAudienceId = null;
    private string _beasyncUrl = null;

    public WeatherController(DBService dbSvc, TokenService tkSvc, WeatherService weatherSvc)
    {
        _dbSvc = dbSvc;
        _tokenService = tkSvc;
       _weatherService = weatherSvc;
    }

    [AllowAnonymous]
    [HttpPost("GetWeather")]

    [ProducesResponseType(typeof(GetWeatherResponse), 200, "application/json")]
    public async Task<IActionResult> GetWeather([FromBody] GetWeatherRequest req)
    {
        return new JsonResult(await _weatherService.FetchAndUpdateWeather(req));
    }

}
