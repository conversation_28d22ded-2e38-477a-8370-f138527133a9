﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Users;
using ht.data.common.FacilitySetup;
using ht.be.apis.Utils;
using ht.be.apis.Extensions;
using ht.common.backend.shared.models;
using Microsoft.Graph.Models;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class FacilityController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;

        private string _authAudienceId = null;
        public FacilityController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
        }

        [AllowAnonymous]

        [HttpPost("GetDashboard")]
        public async Task<IActionResult> GetDashboard([FromBody] GetFacilityDashboardRequest req)

        {
            GetFacilityDashboardResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Facility_Dashboard", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<GetFacilityDashboardResponse>(res);

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]

        [HttpPost("UpsertFacility")]
        public async Task<IActionResult> UpsertFacility([FromBody] UpsertFacilityRequest req)

        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Facility", json, jsonSecurity);
            var resp = new UpsertFacilityResponse
            {
                ShowDetails = res
            };

            return (new JsonResult(resp));
        }

        [AllowAnonymous]

        [HttpPost("GetFacilities")]
        public async Task<IActionResult> GetFacilities([FromBody] GetFacilitiesRequest req)

        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Facilities", json, jsonSecurity);
            var resp = new data.common.baseResponse
            {
                Message = res
            };

            return (new JsonResult(resp));
        }

        [AllowAnonymous]

        [HttpPost("GetParentFacilities")]
        public async Task<IActionResult> GetParentFacilities([FromBody] GetFacilitiesRequest req)

        {
            GetFacilitiesResponse br = new();
            try
            {

                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Parent_Facilities", json, jsonSecurity);
                br.Facilities = HTJsonSerialiser.Deserialise<List<Facility>>(res);

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]

        [HttpGet("GetPharmacies/{ExFacilityId}")]
        public async Task<IActionResult> GetPharmacies(string ExFacilityId)

        {
            GetPharmaciesResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Pharmacies", ExFacilityId, jsonSecurity);
                br.Pharmacies = HTJsonSerialiser.Deserialise<List<HealthTeamsPharmacy>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [HttpPost("UpsertPharmacy")]
        public async Task<IActionResult> UpsertPharmacy([FromBody] UpsertPharmacyRequest req)

        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Pharmacy", json, jsonSecurity);
            var resp = new UpsertPharmacyResponse
            {
                ExPharmacyId = res
            };

            return (new JsonResult(resp));
        }


        [AllowAnonymous]

        [HttpPost("GetFacility")]
        public async Task<IActionResult> GetFacility([FromBody] GetFacilityRequest req)
        {
            var br = new GetFacilityResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

                if (req?.ExFacilityId == null && req?.ExFacilityId == null)
                    throw new Exception("Null input");

                var json = HTJsonSerialiser.Serialize(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Facility_ById", json, jsonSecurity);
                if (res != null)
                    br.Facility = HTJsonSerialiser.Deserialise<HealthTeamsFacility>(res);

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return new NotFoundObjectResult(br);
            }
        }

        [AllowAnonymous]

        [HttpPost("GetAvailableFacilityDevices")]
        public async Task<IActionResult> GetAvailableFacilityDevices([FromBody] GetFacilityDevicesRequest req)
        {
            var br = new GetFacilityDevicesResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                if (req?.ExFacilityId == null)
                    throw new Exception("Null input");

                var json = HTJsonSerialiser.Serialize(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Available_Facility_Devices", json, jsonSecurity);
                if (res != null)
                    br.FacilityDevices = HTJsonSerialiser.Deserialise<List<HealthTeamsFacilityDevice>>(res);

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return new NotFoundObjectResult(br);
            }
        }

        [AllowAnonymous]

        [HttpPost("GetSelectedFacilityDevices")]
        public async Task<IActionResult> GetSelectedFacilityDevices([FromBody] GetFacilityDevicesRequest req)
        {
            var br = new GetFacilityDevicesResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                if (req?.ExFacilityId == null)
                    throw new Exception("Null input");

                var json = HTJsonSerialiser.Serialize(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Selected_Facility_Devices", json, jsonSecurity);
                if (res != null)
                    br.FacilityDevices = HTJsonSerialiser.Deserialise<List<HealthTeamsFacilityDevice>>(res);

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return new NotFoundObjectResult(br);
            }
        }

        [AllowAnonymous]

        [HttpPost("UpsertFacilityDevices")]
        public async Task<IActionResult> UpsertFacilityDevices([FromBody] UpsertFacilityDevicesRequest req)
        {
            var br = new data.common.baseResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialize(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Facility_Devices", json, jsonSecurity);
                if (res != null)
                    br = HTJsonSerialiser.Deserialise<data.common.baseResponse>(res);
                br.Message = "Devices updated successfully";
                br.Status = "Success";
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return new NotFoundObjectResult(br);
            }
        }

        [AllowAnonymous]
        [HttpGet("GetFacilityAdmins/{ExFacilityId}")]
        public async Task<IActionResult> GetFacilityAdmins(string ExFacilityId)
        {
            GetUsersSearchBriefResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Facility_Admins", ExFacilityId, jsonSecurity);
                br.Users = HTJsonSerialiser.Deserialise<List<HTListUser>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpGet("GetReports/{ExFacilityId}")]
        public async Task<IActionResult> GetReports(string ExFacilityId)

        {
            GetHTReportsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Reports", ExFacilityId, jsonSecurity);
                br.Reports = HTJsonSerialiser.Deserialise<List<HealthTeamsReport>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [HttpPost("UpsertAssignableFacilityDevice")]
        public async Task<IActionResult> UpsertAssignableFacilityDevice([FromBody] UpsertFacilityDevicesRequest req)
        {
            var br = new data.common.baseResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialize(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Assignable_Devices", json, jsonSecurity);
                if (res != null)
                    br = HTJsonSerialiser.Deserialise<data.common.baseResponse>(res);
                br.Message = "Device updated successfully";
                br.Status = "Success";
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return new NotFoundObjectResult(br);
            }
        }

        [AllowAnonymous]
        [HttpPost("UpsertFacilityCustomStyles")]
        public async Task<IActionResult> UpsertFacilityCustomStyles([FromBody] UpsertCustomStylesRequest req)
        {
            var br = new UpsertCustomStylesResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialize(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Facility_CustomStyles", json, jsonSecurity);
                if (res != null)
                    br.StyleJSON = HTJsonSerialiser.Deserialise<StyleJSON>(res);
                br.Message = "Custom styles updated successfully.";
                br.Status = "Success";

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return new NotFoundObjectResult(br);
            }
        }

        [AllowAnonymous]
        [HttpGet("GetFacilityCustomStyles/{ExFacilityId}")]
        public async Task<IActionResult> GetFacilityCustomStyles(string ExFacilityId)

        {
            GetFacilityCustomStyleResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Facility_CustomStyles", ExFacilityId, jsonSecurity);
                br.CustomStyles = HTJsonSerialiser.Deserialise<StyleJSON>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpGet("GetClientDiscoverForFacility/{userExID}")]
        public async Task<IActionResult> GetClientDiscoverForResident(string userExID)
        {
            var facilityHash = await _dbSvc.ExecSprocWithIdAsync("sp_get_facility_hash", userExID);

            var resp = new DiscoveryResponse
            {
                Properties = new Dictionary<string, string> {
                    { "facilityVersion", facilityHash },
                    }
            };

            return (new JsonResult(resp));
        }

        [AllowAnonymous]
        [HttpGet("GetLiveFacilitiesDetails/{userExId}")]
        public async Task<IActionResult> GetLiveFacilitiesDetails(string userExId)
        {
            var br = new GetFacilityDetailsResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.SP_Get_Live_Facilities_Details", userExId, jsonSecurity);
                br.FacilityDetails = HTJsonSerialiser.Deserialise<List<HTFacilityDetais>>(res);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }
    }
}
