﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Users;
using ht.be.apis.Utils;
using ht.be.apis.Extensions;
using Microsoft.Extensions.Logging;
using ht.common.backend.shared.models.iot;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class IoTDeviceController : ControllerBase
    {

        private DBService _dbSvc;
        private IoTDeviceService _iotSvc;

        public static string IotConnStr;

        
        public IoTDeviceController(DBService dbSvc, IoTDeviceService iotSvc, ILogger<IoTDeviceController> log)
        {
            _dbSvc = dbSvc;
            _iotSvc = iotSvc;
            _iotSvc._dbSvc = dbSvc;
            _iotSvc._log = log;

        }

        [AllowAnonymous]

        [HttpGet("GetDeviceList")]
        public async Task<IActionResult> GetDeviceList()

        {
            baseResponse br = new();
            try
            {
                var lst = await _iotSvc.GetDevices();
                return (new JsonResult(lst));
                

            }
            catch (Exception ex)
            {
                br.FromException(ex);

            }
            return (new JsonResult(br));
        }

        [AllowAnonymous]
        [HttpGet("SyncDevicesToDB")]
        public async Task<IActionResult> SyncDevicesToDB()

        {
            baseResponse br = new() { Status = "Success" };
            try
            {
                await _iotSvc.SyncDevicesToDB();
                return new JsonResult(br);


            }
            catch (Exception ex)
            {
                br.FromException(ex);

            }
            return (new JsonResult(br));
        }

        [AllowAnonymous]
        [HttpGet("AddIoTDevices")]
        public async Task<IActionResult> AddIoTDevices()

        {
            baseResponse br = new() { Status = "Success" };
            try
            {
                await _iotSvc.AddDevices();
                return new JsonResult(br);


            }
            catch (Exception ex)
            {
                br.FromException(ex);

            }
            return (new JsonResult(br));
        }

        [AllowAnonymous]
        [HttpGet("GetNextAvailableDevice")]
        public async Task<IActionResult> GetNextAvailableDevice()

        {
            baseResponse br = new() { Status = "Success" };
            try
            {
                var ad = HTJsonSerialiser.Deserialise<azureDevice>(await _iotSvc.GetNextAvailableDevice());
                br.Message = ad.DeviceConnectionString;
                return new JsonResult(br);


            }
            catch (Exception ex)
            {
                br.FromException(ex);

            }
            return (new JsonResult(br));
        }
    }


}

