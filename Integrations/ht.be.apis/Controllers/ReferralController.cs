﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Users;
using ht.be.apis.Utils;
using ht.be.apis.Extensions;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ReferralController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;

        private string _authAudienceId = null;
        public ReferralController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
        }



        [AllowAnonymous]
        [HttpPost("GetReferrals")]

        public async Task<IActionResult> GetReferrals([FromBody] GetReferralRequest req)
        {
            GetReferralResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Referrals_by_UserExId", json,jsonSecurity);
                br.Referrals = HTJsonSerialiser.Deserialise< List<TelehealthReferral>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [AllowAnonymous]
        [HttpGet("GetReferralDetails/{exreferralId}")]
        public async Task<IActionResult> GetReferralDetails(string exreferralId)
        {
            GetReferralResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Referral_Details_by_ExreferralId", exreferralId,jsonSecurity);
                br.Referrals = HTJsonSerialiser.Deserialise<List<TelehealthReferral>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [AllowAnonymous]

        [HttpPost("UpsertReferral")]
        public async Task<IActionResult> UpsertReferral([FromBody] UpsertReferralsRequest req)

        {
            UpsertReferralsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Referral", json,jsonSecurity);

                br = HTJsonSerialiser.Deserialise<UpsertReferralsResponse>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [AllowAnonymous]

        [HttpPost("SendtoSpecialist")]
        public async Task<IActionResult> SendtoSpecialist([FromBody] SendtoSpecialistRequest req)

        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Send_to_Specialist", json,jsonSecurity);
            var resp = new baseResponse
            {
                Message = res
            };

            return (new JsonResult(resp));
        }


    }
}
