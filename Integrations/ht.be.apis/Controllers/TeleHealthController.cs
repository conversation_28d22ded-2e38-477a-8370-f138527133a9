﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.be.apis.Utils;
using ht.data.common.Users;
using System.Text.Json.Nodes;
using ht.data.common.Weather;
using System.Net.Http;
using System.Net.Http.Json;
using ht.be.apis.Extensions;

using QuickChart;
using System.Threading;
using Microsoft.Azure.Amqp.Framing;
using Newtonsoft.Json.Linq;
using ht.data.common.Tasks;
using ht.data.common.Wounds;
using Newtonsoft.Json;
using ht.data.common.Partner.TheLookOut;
using static Azure.Core.HttpHeader;
using Microsoft.Extensions.Logging;

namespace ht.be.apis.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class TeleHealthController : ControllerBase
{

    DBService _dbSvc;
    TokenService _tokenService;
    AcsService _acsService;
    WeatherService _weatherService;
    SignalRService _signalRService;
    BlobHelper _blobHelper;
    ParallelOptions _parallelOptions;

    private string _authAudienceId = null;
    private string _beasyncUrl = null;
    PartnerService _partnerService;
    ILogger<TeleHealthController> _logger;

    public TeleHealthController(DBService dbSvc,
            TokenService tkSvc,
            AcsService acsSvc,
            WeatherService weather,
            SignalRService sigr,
            BlobHelper blobHelper,
            PartnerService partnerService,
            ILogger<TeleHealthController> logger)
    {
        _dbSvc = dbSvc;
        _tokenService = tkSvc;
        _acsService = acsSvc;
        _weatherService = weather;
        _signalRService = sigr;
        _blobHelper = blobHelper;
        _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
        _beasyncUrl = common.backend.shared.Globals.Properties["HtBEAsyncUrl"];

        _parallelOptions = new() { MaxDegreeOfParallelism = 3 };
        _partnerService = partnerService;
        _logger = logger;
    }
    [AllowAnonymous]
    [HttpGet("GetAcsId")]
    public async Task<IActionResult> GetAcsId()

    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            //validate the request - these should be global somewhere - maybe a custom attribute.
            var roles = HttpContext.Request.ExtractClientUserRoles();
            if (roles == null)
                throw new NullReferenceException("ERROR: Please supply correct credientials to the backend call.");

            var userId = roles.UserExId;
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(null, true);
            //We need to grab or create the AcsId
            br.Message = (await _acsService.GetUserAndStoreAcsId(userId, _dbSvc))?.AcsUserId;

        }


        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("GetTeleHealthDetails")]
    public async Task<IActionResult> GetTeleHealthDetails([FromBody] GetTeleHealthDetailsRequest req)

    {
        GetTeleHealthDetailsResponse br = new GetTeleHealthDetailsResponse { Status = "Success" };
        try
        {
            //validate the request - these should be global somewhere - maybe a custom attribute.
            /*
            if (req == null)
                throw new NullReferenceException("ERROR: Request cannot be null");
            NOTE: this case is covered by the '!!' in the parameters
            
             */
            if (!Guid.TryParse(req.ExResidentId, out Guid _result))
                throw new NullReferenceException("ERROR: Please supply a valid resident Id");
            if (string.IsNullOrEmpty(req.Role) || string.IsNullOrEmpty(req.TabName))
                throw new NullReferenceException("ERROR: Missing required fields - role + tabName");


            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Details", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetTeleHealthDetailsResponse>(res);
            br.Status = "Success";

            #region Read progress notes from partner integration systems
            if (req.TabName == "Progress Notes")
            {
                dynamic user = JsonConvert.DeserializeObject(jsonSecurity);

                var partnerMappingDetails = await _partnerService.GetFacilityPartnerMapping(req.ExFacilityId, req.ExResidentId);
                
                // If integrating with Lookout
                if (_partnerService.IsPartnerTheLookOut(partnerMappingDetails))
                {
                    _logger.LogInformation("Getting progress notes from The LookOut");
                    var tlwNote = await _partnerService.ReadProgressNoteFromTLW(req.ExResidentId, partnerMappingDetails);

                    if (tlwNote.Count > 0)
                    {
                        var note = tlwNote.Select(p => new TelehealthProgressNote()
                        {
                            NoteDetails = p.Content,
                            Title = p.Title,
                            ExResidentId = req.ExResidentId,
                            NoteDateUtc = p.Created_At,
                            CarerName = p?.Creator?.Full_Name
                           
                          
                        });
                      
                        br.Resident ??= new ResidentUser();
                        br.Resident.ProgressNotes ??= new List<TelehealthProgressNote>();
                        br.Resident.ProgressNotes.AddRange(note);
                        br.Count=tlwNote.Count;
                    }
                }
                // If integrating with Medtech
                else if (_partnerService.IsPartnerMedtech(partnerMappingDetails))
                {
                    // Get progress notes from Medtech and merge them with our existing notes
                    _logger.LogInformation($"Getting progress notes from Medtech. We already have {br.Count ?? 0} notes from our db.");

                    var medtech = new MedtechIntegrationService(_dbSvc, _logger);
                    try
                    {
                        var medtechNotes = await medtech.ReadProgressNotesFromMedtech(req.ExResidentId, partnerMappingDetails);

                        _logger.LogInformation($"Found {medtechNotes?.Count ?? 0} notes from Medtech");

                        if (medtechNotes.Count > 0)
                        {
                            //var note = medtechNotes.Select(p => new TelehealthProgressNote()
                            //{
                            //    NoteDetails = p.Content,
                            //    Title = p.Title,
                            //    ExResidentId = req.ExResidentId,
                            //    NoteDateUtc = p.Created_At,
                            //    CarerName = p?.Creator?.Full_Name
                            //});

                            br.Resident ??= new ResidentUser();
                            br.Resident.ProgressNotes ??= new List<TelehealthProgressNote>();
                            br.Resident.ProgressNotes.AddRange(medtechNotes);
                            br.Count += medtechNotes.Count;

                            br.Resident.ProgressNotes = br.Resident.ProgressNotes.OrderByDescending(x => x.NoteDateUtc ?? DateTime.MinValue).ToList();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error getting progress notes from Medtech: " + ex.ToString());
                    }
                    _logger.LogInformation($"After getting progress notes from Medtech, Count: {br.Count ?? 0} and ProgressNotes.Count: {br.Resident?.ProgressNotes?.Count}");
                }
            }
            #endregion
            #region for Monitoring data get accessible urls by using AskForAccess method
            if (br.Resident?.Monitoring?.Count > 0 && !string.IsNullOrEmpty(req?.ExTransactionId) && !string.IsNullOrEmpty(req?.TabName))
            {
                var token = CancellationToken.None;
                var md = br.Resident.Monitoring;

                if (md?.Count > 0)
                {
                    await Parallel.ForEachAsync(md, _parallelOptions, async (item, token) =>
                    {
                        await Parallel.ForEachAsync(item.Readings, _parallelOptions, async (reading, token) =>
                        {
                            if (reading.Files?.Count > 0)
                            {
                                foreach (var f in reading.Files)
                                {
                                    var res = await AskForAccessAsync(new AskForAccessRequest { Files = f.FileUrls });
                                    f.FileUrls = res?.Files;
                                }
                            }

                        });

                    });
                };
            };

            #endregion

            #region ETS Scope get accessible urls

            if (br.Resident?.ETSScopes?.Count > 0 && !string.IsNullOrEmpty(req?.TabName))
            {
                var md = br.Resident.ETSScopes;
                var token = CancellationToken.None;

                if (md?.Count > 0)
                {
                    await Parallel.ForEachAsync(md, _parallelOptions, async (item, token) =>
                    {
                        await Parallel.ForEachAsync(item.Readings, _parallelOptions, async (reading, token) =>
                        {
                            if (reading.Files?.Count > 0)
                            {
                                foreach (var f in reading.Files)
                                {
                                    var res = await AskForAccessAsync(new AskForAccessRequest { Files = f.FileUrls });
                                    f.FileUrls = res?.Files;
                                }
                            }

                        });

                    });
                };
            };
            #endregion
        }


        catch (Exception ex)
        {
            _logger.LogError("Error in GetTeleHealthDetails: " + ex.ToString());
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    private async Task<AskForAccessResponse> AskForAccessAsync(AskForAccessRequest req)
    {
        AskForAccessResponse br = new AskForAccessResponse { Files = new List<HealthTeamsFile>() };
        if (req.Files?.Count == 0)
            return br;
        try
        {
            br.Files = req.Files;
            foreach (var f in br.Files)
            {
                try
                {
                    if (!string.IsNullOrEmpty(f.FileUrl) && f.FileUrl.StartsWith("https://"))
                    {
                        var key = await _blobHelper.GetSasToken(f.FileUrl, 30);
                        if (string.IsNullOrEmpty(key))
                            throw new NullReferenceException("Token cannot be null");
                        f.FileUrl = f.FileUrl + "?" + key;
                    }
                    if (!string.IsNullOrEmpty(f.FileThumbUrl) && f.FileThumbUrl.StartsWith("https://"))
                    {
                        var key = await _blobHelper.GetSasToken(f.FileThumbUrl, 30);
                        if (string.IsNullOrEmpty(key))
                            throw new NullReferenceException("Thumb Token cannot be null");
                        f.FileThumbUrl = f.FileThumbUrl + "?" + await _blobHelper.GetSasToken(f.FileThumbUrl, 30);
                    }
                    else
                    {
                        f.FileThumbUrl = f.FileUrl;
                    }
                }
                catch (Exception fex)
                {
                    if (!_blobHelper.CheckIfBlobExists(f.FileUrl))
                        f.Status = $"ERROR: File does not exist {f.FileUrl}\r\n" + fex.Message;
                }
            }
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (br);

    }

    [AllowAnonymous]

    [HttpPost("ConvertTaskToMeeting")]
    [ProducesResponseType(200, Type = typeof(ConvertTaskToMeetingResponse))]
    public async Task<IActionResult> ConvertTaskToMeeting([FromBody] ConvertTaskToMeetingRequest req)

    {
        ConvertTaskToMeetingResponse br = new ConvertTaskToMeetingResponse();
        try
        {
            if (string.IsNullOrEmpty(req?.ExTaskId))
                throw new ArgumentNullException("ExTaskId cannot be null");

            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_TeleHealth_Convert_Task_To_Meeting", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<ConvertTaskToMeetingResponse>(res);
            if (br != null)
            {
                foreach (var u in br?.MeetingDetails?.Users)
                {
                    if (string.IsNullOrEmpty(u.AcsUserId))
                    {
                        var resp = await _acsService.GetUserAndStoreAcsId(u.UserExId, _dbSvc);
                        u.DisplayName = resp.DisplayName;
                        u.AcsUserId = resp.AcsUserId;
                        u.Mobile = resp.Mobile;
                        u.Email = resp.Email;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }


    [AllowAnonymous]
    [HttpPost("LaunchMeeting")]
    [ProducesResponseType(200, Type = typeof(ConvertTaskToMeetingResponse))]
    public async Task<IActionResult> LaunchMeeting([FromBody] LaunchMeetingRequest req)

    {
        LaunchMeetingResponse br = new();
        //CreateHTCallOptions ht = new CreateHTCallOptions { Parties = new List<MeetingUser>(), acsCallId = req.ExMeetingId, NotifcationCallbackUri = AcsService.AcsNotificationUrl };
        var acsTok = "";
        var acsUserId = "";
        MeetingUser userResident = null;

        try
        {
            if (string.IsNullOrEmpty(req?.ExMeetingId))
                throw new ArgumentNullException("ExMeetingId cannot be null");

            // things in here
            //var json = HTJsonSerialiser.Serialise(req);
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            string loggedinuser = JObject.Parse(jsonSecurity)["userExId"].ToString();
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Meeting_GetMeetingDetails", req.ExMeetingId, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<LaunchMeetingResponse>(res);
            if (br != null)
            {
                foreach (var u in br?.MeetingDetails?.Users)
                {
                    var role = u.Role?.ToLower();
                    if (role == "resident")
                        userResident = u; //saving the reference to the resident in the meeting to inject the weather in.

                    if (string.IsNullOrEmpty(u?.AcsUserId)) //we need to fetch and assign an acsUserID
                    {
                        u.AcsUserId = (await _acsService.GetUserAndStoreAcsId(u.UserExId, _dbSvc))?.AcsUserId;
                    }

                    //ht.Parties.Add(new MeetingUser { AcsUserId = u.AcsUserId });
                    if (role == req.Role?.ToLower() && u.UserExId == loggedinuser)
                    {
                        try
                        {
                            //we need to get them an ACS token.
                            u.AcsToken = (await _acsService.GetUserTokenAsync(u.AcsUserId))?.Token;
                            acsTok = u.AcsToken;
                            acsUserId = u.AcsUserId;
                            if (!string.IsNullOrEmpty(br.MeetingDetails?.AcsChatThreadId))
                            {
                                await _acsService.JoinChatThreadId(br.MeetingDetails?.AcsChatThreadId, acsUserId, _dbSvc, u.DisplayName);
                            };

                        }
                        catch (Exception ex)
                        {
                            u.Properties ??= new();
                            u.Properties["ERROR"] = "ERROR: " + ex.Message;
                            if (string.IsNullOrEmpty(u.AcsToken))
                                u.AcsToken = "ERROR: " + ex.Message;
                        }
                    }
                }


                br.MeetingDetails.AcsCallConnectionId = "<Client to make directly>";

                //We are storing the meeting against the client, not so much on the connection.
                //update the DB with the meeting + connection details for tracking + auditing.
                var json = "{\"exMeetId\":\"" + br.MeetingDetails.ExMeetingId + "\",\"acsUserId\":\"" + acsUserId + "\"}";
                //await _dbSvc.ExecSprocWithJsonAsync("dbo.[sp_MeetingLogs_Insert_ConnectionId]", json);

                LaunchMeetingResponse brr = new();
                //TODO: Madhav you need to add the Resident Info here if appropriate.
                //if (req?.Role.ToLower() == "nurse" || req?.Role.ToLower() == "doctor")
                //Removed this condition and added in backend SP
                {
                    try
                    {
                        string reqr = "{\"exMeetingId\":\"" + req?.ExMeetingId + "\",\"role\"" + ":\"" + req?.Role + "\"}";
                        var resr = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Summary", reqr, jsonSecurity);
                        brr = HTJsonSerialiser.Deserialise<LaunchMeetingResponse>(resr);
                    }
                    catch (Exception ex)
                    {
                        brr.FromException(ex);
                    }
                    br.Resident = brr.Resident;
                    //Get the weather for the Resident
                    //Added the blank check.
                    if (userResident != null && !string.IsNullOrEmpty(brr.Resident?.Address?.City)) //Is there a resident in this call?
                    {

                        var result = await _weatherService.FetchAndUpdateWeather(new GetWeatherRequest
                        {
                            City = brr.Resident?.Address?.City,
                            CountryCode = "au"
                        });

                        userResident.WeatherInfo = result.Weather;
                    }
                }

            }

            //add to the signal R group

            /* MICK: 2023-07-14 Removed signalR call
            var userId = HttpContext.Request.ExtractClientUserRoles()?.UserExId;
            _signalRService.AddToGroup(req.ExMeetingId, userId);
            */

            br.Status = "Success";
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("GetAcsServiceId")]
    public async Task<IActionResult> GetAcsServiceId()
    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            br.Message = await _acsService.GetServiceUserIdAcsToken(_dbSvc);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return new JsonResult(br);
    }

    [AllowAnonymous]
    [HttpGet("RefreshToken/{userExId}")]
    public async Task<IActionResult> RefreshToken(string userExId)
    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            br.Message = await _acsService.AcquireAcsUserIdForUser(userExId, _dbSvc);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return new JsonResult(br);
    }

    [AllowAnonymous]

    [HttpPost("GetMeetingId")]
    public async Task<IActionResult> GetMeetingId([FromBody] GetMeetingIdRequest req)

    {
        GetMeetingIdResponse br = new GetMeetingIdResponse();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Meeting_Id", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetMeetingIdResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GetResidentVitalGraphData")]
    public async Task<IActionResult> GetResidentVitalGraphData([FromBody] GetResidentVitalGraphDataRequest req)
    {
        GetResidentVitalGraphDataResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Vitals_graph_Data", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetResidentVitalGraphDataResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GenerateVitalChart")]
    public async Task<IActionResult> GenerateVitalChart([FromBody] GetResidentVitalGraphDataRequest req)
    {
        GetResidentVitalGraphDataResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Vitals_graph_Data", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetResidentVitalGraphDataResponse>(res);
            //GenerateCHartImage(br);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    private string GenerateCHartImage(GetResidentVitalGraphDataResponse br)
    {
        int i = 0;
        Chart qc = new Chart();

        DateTime[] labels = new DateTime[br.VitalDataList.Count];
        string[] data = new string[br.VitalDataList.Count];
        foreach (var l in br.VitalDataList)
        {
            labels[i] = Convert.ToDateTime(l.ReadingdDate);
            data[i] = l.ReadValue.ToString();
            i++;
        }


        string lowercriticalmin = br.VitalThreshold.AgeLow.ToString();
        string lowercriticalmax = br.VitalThreshold.LowCriticalValue.ToString();
        string lowerwarningmin = br.VitalThreshold.LowCriticalValue.ToString();
        string lowerwarningmax = br.VitalThreshold.LowWarningValue.ToString();
        string normalmin = br.VitalThreshold.LowWarningValue.ToString();
        string normalmax = br.VitalThreshold.HighWarningValue.ToString();
        string uppercriticalmin = br.VitalThreshold.HighCriticalValue.ToString();
        string uppercriticalmax = br.VitalThreshold.AgeHigh.ToString();
        string upperwarningmin = br.VitalThreshold.HighWarningValue.ToString();
        string upperwarningmax = br.VitalThreshold.HighCriticalValue.ToString();
        string lbl = HTJsonSerialiser.Serialize(labels);//.Replace("""""");
        string dt = HTJsonSerialiser.Serialize(data);
        qc.Width = 650;
        qc.Height = 400;
        qc.Version = "3";
        qc.Config = "{\"type\":\"line\",\"data\":{\"labels\":" + lbl + ",\"datasets\":[{\"data\":" + dt + ",\"label\":\"BPSystolic\",\"borderColor\":\"blue\",\"pointBackgroundColor\":\"#fff\",\"pointBorderColor\":\"rgba(47,82,143)\",\"pointHoverBackgroundColor\":\"#fff\",\"pointHoverBorderColor\":\"rgba(148,159,177,0.8)\"}]},\"options\":{\"elements\":{\"line\":{\"tension\":0.5}},\"scales\":{\"x-axis-0\":{\"type\":\"time\",\"time\":{\"unit\":\"week\",\"isoWeekday\":true,\"displayFormats\":{\"week\":\"DDMMMYYYY\"}},\"ticks\":{callback:function(val,index,ticks){let seqno=index+1; return 'Week '+seqno;}},\"grid\":{\"drawBorder\":true,\"color\":\"#2F528F\"}},\"y-axis-0\":{\"position\":\"left\",\"min\":" + lowercriticalmin + ",\"ticks\":{\"stepSize\":10},\"grid\":{\"drawBorder\":true,\"color\":\"#2F528F\"}}},\"plugins\":{\"legend\":{\"display\":false},\"annotation\":{\"annotations\":[{\"drawTime\":\"beforeDatasetsDraw\",\"type\":\"box\",\"xScaleID\":\"x-axis-0\",\"yScaleID\":\"y-axis-0\",\"borderWidth\":0,\"label\":{\"position\":\"right\",\"enabled\":true,\"color\":\"rgba(68,67,67,0.8)\",\"content\":\"LowerUrgent\",\"font\":{\"weight\":\"bold\"}},\"yMin\":" + lowercriticalmin + ",\"yMax\":" + lowercriticalmax + ",\"backgroundColor\":\"rgba(255,127,127,0.9)\"},{\"drawTime\":\"beforeDatasetsDraw\",\"type\":\"box\",\"xScaleID\":\"x-axis-0\",\"yScaleID\":\"y-axis-0\",\"borderWidth\":0,\"yMin\":" + lowerwarningmin + ",\"yMax\":" + lowerwarningmax + ",\"label\":{\"position\":\"right\",\"enabled\":true,\"color\":\"rgba(68,67,67,0.8)\",\"content\":\"LowerWarning\",\"font\":{\"weight\":\"bold\"}},\"backgroundColor\":\"rgba(255,255,127,0.9)\"},{\"drawTime\":\"beforeDatasetsDraw\",\"type\":\"box\",\"xScaleID\":\"x-axis-0\",\"yScaleID\":\"y-axis-0\",\"borderWidth\":0,\"yMin\":" + normalmin + ",\"yMax\":" + normalmax + ",\"label\":{\"position\":\"right\",\"enabled\":true,\"color\":\"rgba(68,67,67,0.8)\",\"content\":\"Normal\",\"font\":{\"weight\":\"bold\"}},\"backgroundColor\":\"rgba(127,215,167,0.9)\"},{\"drawTime\":\"beforeDatasetsDraw\",\"type\":\"box\",\"xScaleID\":\"x-axis-0\",\"yScaleID\":\"y-axis-0\",\"borderWidth\":0,\"yMin\":" + upperwarningmin + ",\"yMax\":" + upperwarningmax + ",\"label\":{\"position\":\"right\",\"enabled\":true,\"color\":\"rgba(68,67,67,0.8)\",\"content\":\"UpperWarning\",\"font\":{\"weight\":\"bold\"}},\"backgroundColor\":\"rgba(255,255,127,0.9)\"},{\"drawTime\":\"beforeDatasetsDraw\",\"type\":\"box\",\"xScaleID\":\"x-axis-0\",\"yScaleID\":\"y-axis-0\",\"borderWidth\":0,\"label\":{\"position\":\"right\",\"enabled\":true,\"color\":\"rgba(68,67,67,0.8)\",\"content\":\"UpperUrgent\",\"font\":{\"weight\":\"bold\"}},\"yMin\":" + uppercriticalmin + ",\"yMax\":" + upperwarningmax + ",\"backgroundColor\":\"rgba(255,127,127,0.9)\"}]}}}}";

        Console.WriteLine(qc.GetUrl());

        // Or get the image
        byte[] imageBytes = qc.ToByteArray();

        // Or write it to a file
        qc.ToFile("chart.jpeg");
        return "";
    }


    [AllowAnonymous]
    [HttpGet("GetSelectionsByType/{selectionType}")]
    public async Task<IActionResult> GetSelectionsByType(string selectionType)

    {
        GetSelectionsResponse br = new();

        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("sp_Get_selections_by_Type", selectionType, jsonSecurity);

            br.Selections = HTJsonSerialiser.Deserialise<List<Selection>>(res);

        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("DeleteVitals")]
    public async Task<IActionResult> DeleteVitals([FromBody] DeleteVitalsRequest req)

    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Delete_Vitals", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("GetMeetingLink/{meetingCode}")]
    public async Task<IActionResult> GetMeetingLink(string meetingCode)

    {
        InviteUserOnCallResponse br = new();

        try
        {
            var jsonSecurity = "";//HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Meeting_Link", meetingCode, jsonSecurity);

            br = HTJsonSerialiser.Deserialise<InviteUserOnCallResponse>(res);

        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("UpsertWoundAreaDetails")]
    public async Task<IActionResult> UpsertWoundAreaDetails([FromBody] UpsertWoundAreaDetailsRequest req)

    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Wound_AreaDetails", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("DeleteWound/{exWoundId}")]
    public async Task<IActionResult> DeleteTask(string exWoundId)
    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Delete_Wound_by_Id", exWoundId, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]
    [HttpPost("GetWoundTreatmentPlan")]
    public async Task<IActionResult> GetWoundTreatmentPlan([FromBody] GetWoundTreatmentPlanRequest req)
    {
        GetWoundTreatmentPlanResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Wound_GettreatmentPlan", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetWoundTreatmentPlanResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GetFacilityWoundTreatmentPlan")]
    public async Task<IActionResult> GetFacilityWoundTreatmentPlan([FromBody] GetFacilityWoundTreatmentPlanRequest req)
    {
        GetWoundTreatmentPlanResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Wound_GettreatmentPlan", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetWoundTreatmentPlanResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    //Insert facility level wound treatment plan
    [AllowAnonymous]
    [HttpPost("UpsertFacilityWoundTreatmentPlan")]
    public async Task<IActionResult> UpsertFacilityWoundTreatmentPlan([FromBody] UpsertFacilityWoundTreatmentPlanRequest req)
    {
        GetWoundTreatmentPlanResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Wound_Upsert_treatmentPlan", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetWoundTreatmentPlanResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("UpsertFacilityHSVRanges")]
    public async Task<IActionResult> UpsertFacilityHSVRanges([FromBody] UpsertFacilityHSVRangesRequest req)
    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Wound_Upsert_Facility_HSVRanges", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("GetFacilityHSVRanges/{exFacilityId}")]
    public async Task<IActionResult> GetFacilityHSVRanges(string exFacilityId)
    {
        GetFacilityHSVRangeResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Wound_Get_Facility_HSVRanges", exFacilityId, jsonSecurity);
            br.HSVRanges = HTJsonSerialiser.Deserialise<HSVRanges>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    #region ACS Server Services
    [AllowAnonymous]

    [HttpPost("StartRecording")]
    public async Task<IActionResult> StartRecording([FromBody] LaunchMeetingRequest req)

    {
        baseResponse br = new();
        try
        {
            var callId = await _acsService.StartRecordingAsync(req?.ServerCallId);

            var json = "{\"exMeetId\":\"" + req.ExMeetingId + "\", \"acsCallRecordingId\":\"" + callId + "\", \"serverCallId\":\"" + req?.ServerCallId + "\"}";
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Meeting_Upsert_CallRecordingId", json);
            br.Message = callId;
            br.Status = "Success";

            _signalRService.SendToGroup(req.ExMeetingId, "TelehealthRecording", "started", req);

        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }
    [AllowAnonymous]
    [HttpPost("PauseRecording")]
    public async Task<IActionResult> PauseRecording([FromBody] LaunchMeetingRequest req)

    {
        baseResponse br = new();
        try
        {
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Meeting_Get_CallRecordingId", req.ExMeetingId);
            var callId = JsonObject.Parse(res)["acsCallRecordingId"].GetValue<string>();
            var serverCallId = JsonObject.Parse(res)["serverCallId"].GetValue<string>();
            await _acsService.PauseRecordingAsync(serverCallId, callId);

            br.Message = "Paused - " + callId;
            br.Status = "Success";

            _signalRService.SendToGroup(req.ExMeetingId, "TelehealthRecording", "paused", req);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }
    [AllowAnonymous]
    [HttpPost("ResumeRecording")]
    public async Task<IActionResult> ResumeRecording([FromBody] LaunchMeetingRequest req)

    {
        baseResponse br = new();
        try
        {
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Meeting_Get_CallRecordingId", req.ExMeetingId);
            var callId = JsonObject.Parse(res)["acsCallRecordingId"].GetValue<string>();
            var serverCallId = JsonObject.Parse(res)["serverCallId"].GetValue<string>();
            await _acsService.ResumeRecordingAsync(serverCallId, callId);

            br.Message = "Recording - " + callId;
            br.Status = "Success";

            _signalRService.SendToGroup(req.ExMeetingId, "TelehealthRecording", "resumed", req);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }
    [AllowAnonymous]
    [HttpPost("StopRecording")]
    public async Task<IActionResult> StopRecording([FromBody] LaunchMeetingRequest req)

    {
        baseResponse br = new();
        try
        {
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Meeting_Get_CallRecordingId", req.ExMeetingId);
            var callId = JsonObject.Parse(res)["acsCallRecordingId"].GetValue<string>();
            var serverCallId = JsonObject.Parse(res)["serverCallId"].GetValue<string>();
            await _acsService.StopRecordingAsync(serverCallId, callId);

            br.Message = "Stopped - " + callId;
            br.Status = "Success";

            _signalRService.SendToGroup(req.ExMeetingId, "TelehealthRecording", "stopped", req);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }
    [AllowAnonymous]
    [HttpPost("GetRecordingState")]
    public async Task<IActionResult> GetRecordingState([FromBody] LaunchMeetingRequest req)

    {
        baseResponse br = new();
        try
        {
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Meeting_Get_CallRecordingId", req.ExMeetingId);
            var callId = JsonObject.Parse(res)["acsCallRecordingId"].GetValue<string>();
            var serverCallId = JsonObject.Parse(res)["serverCallId"].GetValue<string>();
            var r = await _acsService.GetRecordingStateAsync(serverCallId, callId);

            br.Message = r.ToString();
            br.Status = "Success";
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    #endregion



    #region Private Helper Functions




    #endregion

}
