﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Users;
using ht.be.apis.Utils;
using ht.be.apis.Extensions;
using System.Net.Http;
using Microsoft.Extensions.Configuration;
using ht.common.backend.shared.models;
using System.Text;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class NotesController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;
        private readonly IConfiguration _conf;
        private string _beasyncUrl = null;
        PartnerService _partnerService;
        ILogger<NotesController> _logger;

        private string _authAudienceId = null;
        public NotesController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc, IConfiguration configuration, PartnerService partnerService, ILogger<NotesController> logger)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            this._conf = configuration;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
            _beasyncUrl = common.backend.shared.Globals.Properties["HtBEAsyncUrl"];
            _partnerService = partnerService;
            _logger = logger;
        }

        [AllowAnonymous]
        [HttpPost("GetNotes")]
        public async Task<IActionResult> GetNotes([FromBody] GetNotesRequest req)
        {
            GetNotesResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Notes_by_UserexId", json, jsonSecurity);

                br.Notes = HTJsonSerialiser.Deserialise<List<TelehealthProgressNote>>(res);

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpGet("GetNotesDetails/{exnoteId}")]
        public async Task<IActionResult> GetNotesDetails(string exnoteId)
        {
            GetNotesResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Note_Details_by_exnoteId", exnoteId, jsonSecurity);
                var resp = new GetNotesResponse
                {
                    Notes = HTJsonSerialiser.Deserialise<List<TelehealthProgressNote>>(res)
                };

                return (new JsonResult(resp));

            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpPost("UpsertNotes")]
        public async Task<IActionResult> UpsertNotes([FromBody] UpsertNotesRequest req)
        {
            UpsertNotesResponse br = new();
            _logger.LogInformation("Processing UpsertNotes: " + JsonConvert.SerializeObject(req));
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Notes", json, jsonSecurity);

                br = HTJsonSerialiser.Deserialise<UpsertNotesResponse>(res);

                #region Partner Integration
                if (!string.IsNullOrEmpty(br.ExNoteId))
                {
                    req.Note.ExNoteId = br.ExNoteId;
                    dynamic user = JsonConvert.DeserializeObject(jsonSecurity);

                    var partnerMappingDetails = await _partnerService.GetFacilityPartnerMapping(HttpContext.Request.Headers["x-ExFacilityId"].FirstOrDefault(), req.Note.ExResidentId);
                    if (_partnerService.IsPartnerTheLookOut(partnerMappingDetails))
                    {
                        //send task to TLW
                        await _partnerService.SendProgressNote(req.Note.NoteDetails, (string)user.userExId, req.Note.ExResidentId ,partnerMappingDetails);
                    }
                    else if (_partnerService.IsPartnerMedtech(partnerMappingDetails))
                    {
                        // Send note to Medtech
                        _logger.LogInformation("Processing UpsertNotes, sending to Medtech");
                        var medtech = new MedtechIntegrationService(_dbSvc, _logger, _partnerService);
                        medtech.SendProgressNote(req.Note, partnerMappingDetails).GetAwaiter().GetResult();
                    }
                }
                #endregion

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                _logger.LogError("Error in UpsertNotes: " + ex.ToString());
                br.FromException(ex);
                return (new JsonResult(br));
            }


        }

        [AllowAnonymous]
        [HttpPost("GetNotePdf")]
        public async Task<IActionResult> GetNotePdf([FromBody] GetNotePdfRequest req)
        {
            var beUrl = _beasyncUrl + "/api/GenerateNoteReport";
            byte[] pdfResponse = null;
            try
            {
                if (req.ExNoteIds.Count == 0 && !req.IsAllProgressNotes)
                    throw new Exception("No Noteid found");

                using (var hc = new HttpClient())
                {
                    var json = HTJsonSerialiser.Serialise(req);

                    hc.DefaultRequestHeaders.Add("x-functions-key", _conf["function-key"]);
                    var resp = await hc.PostAsync(beUrl, new StringContent(System.Text.Json.JsonSerializer.Serialize(req), Encoding.UTF8, "application/json"));
                    resp.EnsureSuccessStatusCode();
                   pdfResponse= await resp.Content.ReadAsByteArrayAsync();
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
            return new FileContentResult(pdfResponse,"application/pdf");
        }
    }
}
