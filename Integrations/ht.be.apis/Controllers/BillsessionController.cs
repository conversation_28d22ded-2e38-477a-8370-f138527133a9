﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Users;
using ht.be.apis.Utils;
using ht.data.common.Billing;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class BillsessionController : ControllerBase
    {

        DBService _dbSvc;
        PaymentService _paymentSvc;

        private string _authAudienceId = null;
        public BillsessionController(DBService dbSvc, PaymentService paySvc)
        {
            _dbSvc = dbSvc;
            _paymentSvc = paySvc;
            _paymentSvc.Init().GetAwaiter().GetResult(); //this acquires a short term token.
        }

        #region Payment Operations
        [AllowAnonymous]
        [HttpGet("GetPayToken")]

        public async Task<IActionResult> GetPayToken()
        {
            baseResponse br = new();
            try
            {
                br.Message = await _paymentSvc.GetTransToken();
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        [AllowAnonymous]
        [HttpPost("VerifyMedicare")]

        public async Task<IActionResult> VerifyMedicare(MedicareVerificationRequest req)
        {
            MedicareVerificationResponse br = new();
            try
            {
                if (!string.IsNullOrEmpty(req.DobString) && (DateTime.TryParse(req.DobString,out DateTime dt)))
                    req.DobString = dt.ToString("yyyy-MM-dd");
                br = await _paymentSvc.VerifyPatientMedicare(req);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        #endregion

        [AllowAnonymous]
        [HttpPost("GetPaymentUrl")]

        public async Task<IActionResult> GetPaymentUrl(CreatePaymentUrlRequest req)
        {
            baseResponse br = new();
            try
            {
                br.Message = await _paymentSvc.GetCreateTransactionUrl(req,_dbSvc);
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }



        [AllowAnonymous] 
        [HttpGet("GetBillsession/{UserExId}")]

        public async Task<IActionResult> GetBillsession(string UserExId)
        {
            GetBillSessionResponse br = null;
            try
            {
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Bill_Session_by_exUserId", UserExId);
                br = HTJsonSerialiser.Deserialise<GetBillSessionResponse>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [AllowAnonymous]
        [HttpGet("GetBillsessionDetails/{exbillId}")]

        public async Task<IActionResult> GetBillsessionDetails(string exbillId)
        {
            GetBillSessionResponse br = null;
            try
            {
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Bill_Session_Details_by_exbillId", exbillId);
                br = HTJsonSerialiser.Deserialise<GetBillSessionResponse>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }
        

        [AllowAnonymous]

        [HttpPost("UpsertBillsession")]
        public async Task<IActionResult> UpsertBillsession([FromBody] UpsertBillSessionRequest req)

        {
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Bill_Session", json);
            var resp = new baseResponse
            {
                Message = res
            };

            return (new JsonResult(resp));
        }




    }
}
