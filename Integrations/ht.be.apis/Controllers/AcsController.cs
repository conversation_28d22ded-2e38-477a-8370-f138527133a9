﻿using ht.be.apis.Services;
using ht.common.backend.shared;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
#if DEBUG
    [Authorize]
#endif
    public class AcsController : ControllerBase
    {
        private AcsService _acsService;
        private DBService _dbSvc;

        public AcsController(AcsService svc, DBService dbSvc)
        {
            _acsService = svc;
            _dbSvc = dbSvc;
        }


        [AllowAnonymous]
        [Route("GetUserId")]
        [HttpGet]
        [HttpPost]
        public async Task<IActionResult> GetUserId()

        {
            var resp = await _acsService.GetUserIdAsync();

            var res2 = new models.GetCompatUserTokenResponse
            {
                user = new models.GetCompatUserTokenResponse.User
                {
                    communicationUserId = resp.UserId
                }
            };
            return new OkObjectResult(res2);
        }

        [AllowAnonymous]
        [Route("GetTokenForUserId/{acsUserId}")]
        [HttpGet]
        public async Task<IActionResult> GetTokenForUserId(string acsUserId)

        {
            var resp = await _acsService.GetUserTokenAsync(acsUserId);

            var res2 = new models.GetCompatUserTokenResponse
            {
                user = new models.GetCompatUserTokenResponse.User
                {
                    communicationUserId = resp.UserId
                }
            };
            return new OkObjectResult(res2);
        }

        [AllowAnonymous]
        [Route("CreateAndGetUserToken")]
        [HttpGet]
        [HttpPost]
        public async Task<IActionResult> CreateAndGetUserToken()
           
        {
            var resp = await _acsService.GetUserTokenAsync();

            var res2 = new models.GetCompatUserTokenResponse
            {
                token = resp.Token,
                expiresOn = resp.TokenExpiresUtc.Value,
                user = new models.GetCompatUserTokenResponse.User
                {
                    communicationUserId = resp.UserId
                }
            };
            return new OkObjectResult(res2);
        }

        [Route("CreateServerCall")]
        [HttpPost]
        public async Task<IActionResult> CreateServerCall([FromBody] CreateHTCallOptions ht)
        {
            
            var resp = await _acsService.CreateCall(ht);
            return new OkObjectResult(resp);
        }

        [Route("GetServerCall/{acsCallId}")]
        [HttpGet]
        public async Task<IActionResult> GetServcerCall(string acsCallId)
        {
            var resp = await _acsService.GetCallConnection(acsCallId);
            return new OkObjectResult(resp);
        }

        [Route("JoinServerCall")]
        [HttpPost]
        public async Task<IActionResult> JoinServerCall([FromBody] CreateHTCallOptions ht)
        {
            var resp = await _acsService.JoinCall(ht);
            return new OkObjectResult(resp);
        }

        [Route("RemoveUser/{acsUserId}")]
        [HttpGet]
        public async Task<IActionResult> RemoveUser(string acsUserId)
        {
            await _acsService.RemoveUser(acsUserId);
            return new OkObjectResult(new { });
        }

        [AllowAnonymous]
        [HttpGet("ListParticipants/{threadId}")]
        public async Task<IActionResult> ListParticipants([FromRoute] string threadId)
        {
            return new JsonResult(await _acsService.ListAllChatParticipants(threadId, _dbSvc));
        }
    }
}
