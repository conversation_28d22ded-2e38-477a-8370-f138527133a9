﻿using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Dashboards;
using ht.data.common.Tasks;
using ht.data.common.Users;
using ht.be.apis.Utils;
using ht.be.apis.Extensions;
namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ScriptsController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;

        private string _authAudienceId = null;
        public ScriptsController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
        }



        [AllowAnonymous]
        [HttpPost("GetScripts")]

        public async Task<IActionResult> GetScripts([FromBody] GetScriptsRequest req)
        {
            GetScriptsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Scripts_by_UserExId", json, jsonSecurity);
                br.Scripts = HTJsonSerialiser.Deserialise<List<TelehealthScript>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            } 
        }


        [AllowAnonymous]
        [HttpGet("GetScriptDetails/{exscriptId}")]

        public async Task<IActionResult> GetScriptDetails(string exscriptId)
        {
            GetScriptsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Script_Details_by_ExscriptId", exscriptId, jsonSecurity);
                br.Scripts = HTJsonSerialiser.Deserialise<List<TelehealthScript>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [AllowAnonymous]

        [HttpPost("UpsertScripts")]
        public async Task<IActionResult> UpsertScripts([FromBody] UpsertScriptsRequest req)

        {
            UpsertScriptsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Scripts", json, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<UpsertScriptsResponse>(res);
                return (new JsonResult(br));

            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


    }
}
