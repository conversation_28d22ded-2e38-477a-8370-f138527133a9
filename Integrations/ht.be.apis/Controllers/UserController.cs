﻿#region Usings
using ht.be.apis.models;
using ht.data.common;
using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.sys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using ht.be.apis.Utils;
using ht.data.common.Shared;
using System.IO;
using ht.be.apis.Extensions;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json.Nodes;

using Azure.Communication;
using Azure.Communication.CallingServer;
using Azure.Communication.Identity;
using System.Data;
using ht.common.backend.shared.models;
using baseResponse = ht.data.common.baseResponse;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using System.Text;
using ht.data.common.EmailVerification;
#endregion

namespace ht.be.apis.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class UserController : ControllerBase
{

    DBService _dbSvc;
    TokenService _tokenService;
    AcsService _acsService;
    GraphService _graphService;
    EmailService _emailSerive;
    private readonly IConfiguration _conf;
    private string _authAudienceId = null;
    private string _beasyncUrl = null;
    private string _portalUrl = null;
    private string _apimKey = null;
    private readonly IWebHostEnvironment HostingEnvironment;
    public UserController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc, GraphService svc, IConfiguration configuration, EmailService emailSvc)
    {
        _dbSvc = dbSvc;
        _tokenService = tkSvc;
        _acsService = acsSvc;
        _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
        _beasyncUrl = common.backend.shared.Globals.Properties["HtBEAsyncUrl"];
        _portalUrl = common.backend.shared.Globals.Properties["PortalUrl"];
        _apimKey = common.backend.shared.Globals.Properties["APIMKey"];
        _graphService = svc;
        _emailSerive = emailSvc;
        this._conf = configuration;
    }

    #region Presence
    //IMPORTANT: These operations are not to be callable from the FE APIs - these are called by BE Async functions.

    [AllowAnonymous]
    [HttpGet("PresenceOnline/{userexId}")]

    [ProducesResponseType(typeof(baseResponse), 200, "application/json")]
    public async Task<IActionResult> PresenceOnline(string userexId)
    {
        baseResponse br = new baseResponse { Status = "Success" };
        try
        {
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_User_SetOnline_UserexId", userexId);
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }
    [AllowAnonymous]
    [HttpGet("PresenceOffline/{userexId}")]
    [ProducesResponseType(typeof(baseResponse), 200, "application/json")]
    public async Task<IActionResult> PresenceOffline(string userexId)
    {
        baseResponse br = new baseResponse { Status = "Success" };
        try
        {
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_User_SetOffline_UserexId", userexId);
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    #endregion

    [AllowAnonymous]
    [HttpGet("GetUserDetails/{userexId}")]

    [ProducesResponseType(typeof(GetUserDetailsResponse), 200, "application/json")]
    public async Task<IActionResult> GetUserDetails(string userexId)
    {
        GetUserDetailsResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_User_Details_by_UserexId", userexId, jsonSecurity);
            br.User = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<HealthTeamsUser>(res) : new HealthTeamsUser());
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("AskForAccess")]
    [ProducesResponseType(200, Type = typeof(AskForAccessResponse))]
    public async Task<IActionResult> AskForAccess([FromBody] AskForAccessRequest req)

    {
        AskForAccessResponse br = new() { Files = new List<HealthTeamsFile>(), Status = "Success" };

        var beUrl = _beasyncUrl + "/api/UserFileGetSasToken";

        try
        {
            if (req?.Files == null || req.Files.Count == 0)
                throw new Exception("No Urls found");

            var json = HTJsonSerialiser.Serialise(req);
            using (var hc = new HttpClient())
            {
                var sc = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                hc.DefaultRequestHeaders.Add("x-functions-key", _conf["function-key"]);

                var resp = await hc.PostAsync(beUrl, sc);
                resp.EnsureSuccessStatusCode();
                br = HTJsonSerialiser.Deserialise<AskForAccessResponse>(resp.Content.ReadAsStringAsync().Result);
                br.Status = "Success";
            }

        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return new JsonResult(br);
    }

    [AllowAnonymous]
    [HttpPost("GetResidentMonitoringTimes")]
    [ProducesResponseType(200, Type = typeof(GetResidentMonitoringTimesResponse))]
    public async Task<IActionResult> GetResidentMonitoringTimes([FromBody] GetResidentMonitoringTimesRequest req)

    {
        GetResidentMonitoringTimesResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Monitoring_Times", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetResidentMonitoringTimesResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GetResidentLimitsNotifications")]
    [ProducesResponseType(200, Type = typeof(GetResidentMonitoringLimitsNotificationsResponse))]
    public async Task<IActionResult> GetResidentLimitsNotifications([FromBody] GetResidentMonitoringLimitsNotificationsRequest req)

    {
        GetResidentMonitoringLimitsNotificationsResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Monitoring_Limits_Notifications", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetResidentMonitoringLimitsNotificationsResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("UpsertResidentMonitoringTimes")]
    [ProducesResponseType(200, Type = typeof(baseResponse))]
    public async Task<IActionResult> UpsertResidentMonitoringTimes([FromBody] UpsertResidentMonitoringTimesRequest req)

    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Resident_Monitoring_Times", json, jsonSecurity);
            br.Message = res;
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("UpsertResidentLimitsNotifications")]
    [ProducesResponseType(200, Type = typeof(baseResponse))]
    public async Task<IActionResult> UpsertResidentLimitsNotifications([FromBody] UpsertResidentMonitoringLimitsNotificationsRequest req)

    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Resident_Limits_Notifications", json, jsonSecurity);
            br.Message = res;
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("UpsertResidentMonitoringSetup")]
    [ProducesResponseType(200, Type = typeof(UpsertResidentThresholdsResponse))]
    public async Task<IActionResult> UpsertResidentMonitoringSetup([FromBody] UpsertResidentThresholdsRequest req)

    {

        UpsertResidentThresholdsResponse br = new() { Status = "Success" };
        try
        {
            //CHECK NO DUPLICATES
            var query = req?.MonitoringThresholds?.Limits.GroupBy(x => new { x.ItemType, x.ItemSubType })
              .Where(g => g.Count() > 1)
              .Select(y => y.Key)
              .ToList();

            if (query?.Count() > 0)
                throw new Exception("ERROR: duplicate item types in the limits collection. Please supply one of each per call");

            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.SP_Monitoring_setup_upsert", json, jsonSecurity);
            br.MonitoringThresholds = HTJsonSerialiser.Deserialise<MonitoringThresholds>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GetResidentAlertContactList")]
    [ProducesResponseType(200, Type = typeof(GetAlertContactListResponse))]
    public async Task<IActionResult> GetResidentAlertContactList([FromBody] GetAlertContactListRequest req)

    {
        GetAlertContactListResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Alert_Contact_list", json, jsonSecurity);
            br.ContactList = HTJsonSerialiser.Deserialise<List<ContactPerson>>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }


    [AllowAnonymous]

    [HttpPost("GetResident")]
    [ProducesResponseType(200, Type = typeof(GetResidentResponse))]
    public async Task<IActionResult> GetResident([FromBody] GetResidentRequest req)

    {
        GetResidentResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident", json, jsonSecurity);
            br.Users = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<List<ResidentUser>>(res) : new List<ResidentUser>());


            if (br?.Users?.Count > 0)
            {
                var u = br.Users[0];
                if (u.Summary?.ChronicDiseases == null)
                    u.Summary.ChronicDiseases = new List<string>();
                if (u.Summary?.FamilyHistory == null)
                    u.Summary.FamilyHistory = new List<string>();
                if (u.Summary?.Allergies == null)
                    u.Summary.Allergies = new List<string>();
                if (u.Summary?.Medications == null)
                    u.Summary.Medications = new List<string>();
            }
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("ChangePassword")]
    public async Task<ChangeUserPasswordResponse> ChangePassword([FromBody] ChangeUserPasswordRequest req)
    {
        ChangeUserPasswordResponse br = new() { Status = "Success" };
        try
        {
            /* TODO: we need to check they are a nurse or fac admin for a resident under their care */

            /* */

            if (string.IsNullOrEmpty(req.UserExId))
                throw new DllNotFoundException("UserExId not found in request");

            // req.UserExId = req.UserExId.Replace("hello", "");
            //var json = await _dbSvc.ExecSprocWithIdAsync("[dbo].[sp_User_Get_AAD_Id]", req.UserExId);
            //var obj = JsonNode.Parse(json);
            //var aadId = obj?["ExUserId"]?.GetValue<string>();

            //if (string.IsNullOrEmpty(aadId))
            //    throw new NullReferenceException("AAD ID not found");

            //  req.UserExId = aadId;
            //await _graphService.ChangePassword(req);

            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_User_Change_Password", json, jsonSecurity);
            br = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<ChangeUserPasswordResponse>(res) : new ChangeUserPasswordResponse());

            //  br.Message = "User Password changed";
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (br);
    }

    [AllowAnonymous]
    [HttpPost("CreateAADResident")]
    [ProducesResponseType(200, Type = typeof(ChangeUserPasswordResponse))]
    public async Task<baseResponse> CreateAADResident([FromBody] HealthTeamsUser req)
    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            br.Message = await _graphService.CreateResident(req, true, "Hello.123");
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (br);
    }


    [AllowAnonymous]
    [RequestFormLimits(ValueLengthLimit = int.MaxValue, MultipartBodyLengthLimit = int.MaxValue)]
    [HttpPost("UpdateProfilePic")]
    public async Task<IActionResult> UpdateProfilePic()

    {

        baseResponse br = new() { Status = "Success" };
        try
        {
            var beUrl = _beasyncUrl + "/api/UserFileProcessor";
            var userRoles = HttpContext.Request.ExtractClientUserRoles();
            if (userRoles == null)
                throw new NotSupportedException("Please pass the client token");

            //beUrl += "&fname=profilepic.jpg"; //&userid=" + userRoles.UserExId;

            IList<string> AllowedFileExtensions = new List<string> { ".jpg", ".gif", ".png", ".pdf" };
            var req = HttpContext.Request;

            var parser = await HttpMultipartParser.MultipartFormDataParser.ParseAsync(req.Body).ConfigureAwait(false);
            var file = parser.Files.First();
            var userid = string.Empty;
            var fname = string.Empty;
            var ftype = string.Empty;
            //passing UserExId for which profile pic need to update.
            foreach (var p in parser.Parameters)
            {
                if (p.Name == "userid")
                {
                    userid = p.Data;


                }
                else if (p.Name == "ftype")
                {
                    ftype = p.Data;
                    if (p.Data == "logo")
                    {
                        fname = "logo.png";
                    }
                    else
                    {
                        fname = "profilepic.jpg";
                    }
                }

            }
            beUrl += "?ftype=" + ftype + "&fname=" + fname + "&userid=" + userid;

            var ext = Path.GetExtension(file.FileName);
            if (!AllowedFileExtensions.Contains(ext))
                return BadRequest("Invalid File extension");


            //upload the file to the backend.
            using (var ms = new MemoryStream())
            {
                await file.Data.CopyToAsync(ms);
                using (var fileContent = new ByteArrayContent(ms.ToArray()))
                {
                    fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("multipart/form-data");
                    using (var hc = new HttpClient())
                    {
                        hc.DefaultRequestHeaders.Add("x-functions-key", _conf["function-key"]);
                        var resp = await hc.PostAsync(beUrl, fileContent);
                        resp.EnsureSuccessStatusCode();
                        br.Message = await resp?.Content?.ReadAsStringAsync();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return new JsonResult(br);
    }

    [AllowAnonymous]

    [HttpPost("GetResidentsSearch")]
    [ProducesResponseType(200, Type = typeof(GetResidentsSearchResponse))]
    public async Task<IActionResult> GetResidentsSearch([FromBody] GetResidentsSearchRequest req)

    {
        GetResidentsSearchResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Residents_Search", json, jsonSecurity);
            br.Residents = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<List<ResidentUser>>(res) : new List<ResidentUser>());
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("GetUsersSearchBrief")]
    [ProducesResponseType(200, Type = typeof(GetUsersSearchBriefResponse))]
    public async Task<IActionResult> GetUsersSearchBrief([FromBody] GetUsersSearchRequest req)

    {
        GetUsersSearchBriefResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_UsersBrief_Search", json, jsonSecurity);
            br.Users = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<List<HTListUser>>(res) : new List<HTListUser>());
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("GetUsersSearch")]
    [ProducesResponseType(200, Type = typeof(GetUsersSearchResponse))]
    public async Task<IActionResult> GetUsersSearch([FromBody] GetUsersSearchRequest req)

    {
        GetUsersSearchResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Users_Search_List", json, jsonSecurity);
            br.Results = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<List<HealthTeamsUser>>(res) : new List<HealthTeamsUser>());
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("GetFacilities")]
    [ProducesResponseType(200, Type = typeof(GetFacilitiesResponse))]
    public async Task<IActionResult> GetFacilities([FromBody] GetFacilitiesRequest req)

    {
        GetFacilitiesResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Facilities", json, jsonSecurity);
            br.Facilities = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<List<Facility>>(res) : new List<Facility>());
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("DisconnectFacility")]
    [ProducesResponseType(200, Type = typeof(DisconnectFacilityResponse))]
    public async Task<IActionResult> DisconnectFacility([FromBody] DisconnectFacilityRequest req)

    {
        DisconnectFacilityResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Facility_Disconnect", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<DisconnectFacilityResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("GetUserConnectedFacilities/{UserExId}")]
    [ProducesResponseType(typeof(ConnectedFacilitiesResponse), 200)]
    public async Task<IActionResult> GetUserConnectedFacilities(string UserExId)

    {
        ConnectedFacilitiesResponse br = new() { Status = "Success" };
        try
        {

            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Facility_Get_UserConnected", UserExId, jsonSecurity);
            br.Facilities = HTJsonSerialiser.Deserialise<List<ConnectedUserFacility>>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }


    [AllowAnonymous]
    [HttpGet("GetCountries")]
    [ProducesResponseType(typeof(GetCountriesResponse), 200)]
    public async Task<IActionResult> GetCountries()

    {
        GetCountriesResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_CountryList", null, jsonSecurity);
            br.Countries = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<List<Country>>(res) : new List<Country>());
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpGet("GetNewFacilityRequests/{UserExId}")]
    [ProducesResponseType(typeof(GetNewFacilityRequestsResponse), 200, "application/json")]
    public async Task<IActionResult> GetNewFacilityRequests(string UserExId)

    {
        GetNewFacilityRequestsResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Facility_Get_New_requests_by_userId", UserExId, jsonSecurity);
            br.Facilityrequests = HTJsonSerialiser.Deserialise<List<NewFacilityRequest>>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("ConfirmFacilityRequest")]
    [ProducesResponseType(typeof(GetFacilityConfirmResponse), 200)]
    public async Task<IActionResult> ConfirmFacilityRequest([FromBody] GetFacilityConnectRequests req)

    {
        var br = new GetFacilityConfirmResponse();
        try
        {
            var json = HTJsonSerialiser.Serialise(req);
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_user_confirm_facility_request", json, jsonSecurity);

            br.Facility = HTJsonSerialiser.Deserialise<Facility>(res);
            var bm = HTJsonSerialiser.Deserialise<baseResponse>(res);
            br.Status = bm?.Status;
            br.Message = bm?.Message;
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("DeleteFacilityRequest")]
    [ProducesResponseType(typeof(GetFacilityConfirmResponse), 200)]
    public async Task<IActionResult> DeleteFacilityRequest([FromBody] GetFacilityConnectRequests req)

    {
        var br = new GetFacilityConfirmResponse();
        try
        {
            var json = HTJsonSerialiser.Serialise(req);
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_user_Reject_facility_request", json, jsonSecurity);
            br.Facility = HTJsonSerialiser.Deserialise<Facility>(res);
            var bm = HTJsonSerialiser.Deserialise<baseResponse>(res);
            br.Status = bm?.Status;
            br.Message = bm?.Message;
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }
    [AllowAnonymous]

    [HttpPost("GetFacilitybyInvitecode")]
    [ProducesResponseType(typeof(GetFacilitiesResponse), 200)]
    public async Task<IActionResult> GetFacilitybyInvitecode([FromBody] GetFacilityConnectRequests req)

    {
        var br = new GetFacilitiesResponse();
        try
        {
            var json = HTJsonSerialiser.Serialise(req);
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_facility_by_invitecode", json, jsonSecurity);
            br.Facilities = HTJsonSerialiser.Deserialise<List<Facility>>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("UserConnectFacility")]
    [ProducesResponseType(typeof(GetFacilityConfirmResponse), 200)]
    public async Task<IActionResult> UserConnectFacility([FromBody] GetFacilityConnectRequests req)

    {
        var br = new GetFacilityConfirmResponse();
        try
        {
            var json = HTJsonSerialiser.Serialise(req);
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_User_connect_facility", json, jsonSecurity);

            br.Facility = HTJsonSerialiser.Deserialise<Facility>(res);
            br.Status = "Success";

        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("UpsertResident")]
    [ProducesResponseType(typeof(UpsertResidentResponse), 200)]
    public async Task<IActionResult> UpsertResident([FromBody] UpsertResidentRequest req)

    {

        var br = new UpsertResidentResponse();
        var brEmailTemplate = new SharedListItem();
        try
        {
            string password = string.Empty;
            if (string.IsNullOrEmpty(req.resident?.ExResidentId) && (Convert.ToString(req.resident?.Email).ToLower() == $"{Convert.ToString(req.resident?.Facility).ToLower()}_res.{Convert.ToString(req.resident?.FirstName).ToLower()}.{Convert.ToString(req.resident?.LastName).ToLower()}@healthteams.com.au"))
            {
                password = "Hello.123";
            }
            else
            {
                password = await _emailSerive.RandomString(8);
            }


            var exResidentId = string.Empty;
            req.resident.PasswordHash = password;
            var json = HTJsonSerialiser.Serialise(req);
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Resident_Upsert", json, jsonSecurity);

            br = HTJsonSerialiser.Deserialise<UpsertResidentResponse>(res);
            br.Status = "Success";
            baseResponse bsr = new() { Status = "Success" };
            if (string.IsNullOrEmpty(req.resident?.ExResidentId) && (Convert.ToString(req.resident?.Email).ToLower() == $"{Convert.ToString(req.resident?.Facility).ToLower()}_res.{Convert.ToString(req.resident?.FirstName).ToLower()}.{Convert.ToString(req.resident?.LastName).ToLower()}@healthteams.com.au"))
            {
                try
                {
                    req.resident.Email = $"{Convert.ToString(req.resident?.Facility).ToLower()}_res.{Convert.ToString(req.resident?.FirstName).ToLower()}.{Convert.ToString(req.resident?.LastName).ToLower()}@healthteams.com.au";
                    req.resident.UserExId = br.resident.ExResidentId;
                    // bsr.Message = await _graphService.CreateResident(req.resident, false, "Hello.123");
                    //br.resident.UserAcsToken = bsr.Message;

                    //json = HTJsonSerialiser.Serialise(br);
                    //UpdateResidentAADId(json, jsonSecurity);
                }
                catch (Exception ex)
                {
                    br.FromException(ex);
                }
            }
            else if (string.IsNullOrEmpty(req.resident?.ExResidentId) && (Convert.ToString(req.resident?.Email).ToLower() != $"{Convert.ToString(req.resident?.Facility).ToLower()}_res.{Convert.ToString(req.resident?.FirstName).ToLower()}.{Convert.ToString(req.resident?.LastName).ToLower()}@healthteams.com.au"))
            {
                // Will uncomment following lines once we go back to Azure Ad again.
                //bsr.Message = await _graphService.CreateResident(req.resident, true, password);
                //br.resident.UserAcsToken = bsr.Message;

                //json = HTJsonSerialiser.Serialise(br);
                //UpdateResidentAADId(json, jsonSecurity);

                brEmailTemplate = await _emailSerive.GetEmailTemplate("Welcome");
                string emailTemplate = string.Format(brEmailTemplate.Description, req.resident.FirstName, req.resident.Email, password, _portalUrl);
                var result = await _emailSerive.SendEmail(emailTemplate, req.resident.Email, brEmailTemplate.Category, _portalUrl);
            }

        }
        catch (Exception ex)
        {
            br.FromException(ex);

        }
        return (new JsonResult(br));
    }




    private async Task<IActionResult> SendEmail(string email, string firstName, string Password)
    {
        string Body = string.Empty;
        string htmlTemplate = System.IO.File.ReadAllText("InviteTemplate.html");


        var body = new
        {
            Content = string.Format(htmlTemplate, firstName, email, Password, _portalUrl),
            Subject = "Welcome to HealthTeams",
            To = email,
            Link = _portalUrl
        };

        using (var clnt = new HttpClient())
        {
            clnt.DefaultRequestHeaders.Add("x-api-key", _apimKey);

            var resp = await clnt.PostAsync("https://api-ht-uat.azure-api.net/b2c/v1/api/b2c/send-email",
                new StringContent(JsonSerializer.Serialize(body), Encoding.UTF8, "application/json")
                );
            resp.EnsureSuccessStatusCode();
        };

        return (Ok());
    }
    ///update AAD ID for the newly added resident.
    private void UpdateResidentAADId(string json, string jsonSecurity)
    {
        var res = _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_resident_ADD", json, jsonSecurity);
    }

    [AllowAnonymous]
    [HttpGet("GenerateAndSendEmail/{mode}")]
    public async Task<IActionResult> GenerateAndSendEmail(string mode, [FromQuery] string code, [FromQuery] string email, [FromQuery] string facName, [FromQuery] string approle, [FromQuery] string lastname)
    {
        var br = new ht.common.backend.shared.models.baseResponse();
        try
        {
            var url = $"b2c/v1/api/Identity/{(mode == "signup" ? "SendSignupEmail/" : "SendSigninEmail/")}{email}?code={code}&facname={facName}&approle={approle}&lastname={lastname}";
            using (var client = APIMManager.GetClient())
            {
                //call and get the link
                var resp = await client.GetAsync(url);
                resp.EnsureSuccessStatusCode();
                br.Message = await resp.Content.ReadAsStringAsync(); //should have the link right there.
                br.Status = "Success";
            };
            return (new JsonResult(br));

        }
        catch (Exception ex)
        {
            br = await ht.common.backend.shared.models.baseResponse.FromException(br, ex);
            return (new NotFoundObjectResult(br));
        }
    }

    [AllowAnonymous]
    [HttpPost("GetAlertResidentList")]
    [ProducesResponseType(typeof(GetAlertResidentListResponse), 200)]
    public async Task<IActionResult> GetAlertResidentList([FromBody] GetAlertResidentListRequest req)

    {
        GetAlertResidentListResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Alert_list", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetAlertResidentListResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]

    [HttpPost("UpdateResidentAlertStatus")]
    public async Task<IActionResult> UpdateResidentAlertStatus([FromBody] UpdateResidentAlertStatusRequest req)
    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_Resident_Alert_Status", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("DeActivateUser/{UserExId}")]
    public async Task<IActionResult> DeActivateUser(string UserExId)

    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Deactivate_User", UserExId, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("ActivateUser/{UserExId}")]
    public async Task<IActionResult> ActivateUser(string UserExId)
    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Activate_User", UserExId, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("DeleteUser/{UserExId}")]
    public async Task<IActionResult> DeleteUser(string UserExId)
    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Delete_User", UserExId, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("CheckDuplicateEmail")]
    public async Task<IActionResult> CheckDuplicateEmail([FromBody] EmailVerificationRequest req)
    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(new baseRequest(), true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Check_Duplicate_Email", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GetOnetoOneChatThreadId")]
    public async Task<IActionResult> GetOnetoOneChatThreadId([FromBody] GetOnetoOneChatThreadIdRequest req)
    {
        GetOnetoOneChatThreadIdResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_OnetoOne_Chat_ThreadId", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetOnetoOneChatThreadIdResponse>(res);
            var loggedInUserAcstoken = string.Empty;

            //Chat Participant list which will be passed to while creating chat thread id and also return to front end.
            List<Azure.Communication.Chat.ChatParticipant> lstParticipant = new();
            for (int i = 0; i < br.lstUsers.Count; i++)
            {

                if (string.IsNullOrEmpty(br.lstUsers[i]?.AcsUserId))
                {
                    br.lstUsers[i].AcsUserId = (await _acsService.GetUserAndStoreAcsId(br.lstUsers[i].UserExId, _dbSvc))?.AcsUserId;
                }

                if (br.lstUsers[i].UserExId == req?.UserExId1)
                {
                    //Create ACSToken for logged in user to connect the chat and if threadId is not available then use this token to create new chat threadId

                    var result = await _acsService.GetUserTokenAsync(br.lstUsers[i]?.AcsUserId);
                    loggedInUserAcstoken = Convert.ToString(result?.Token);
                    br.AcsUserToken = loggedInUserAcstoken;
                }
                var newUser = new CommunicationUserIdentifier(br.lstUsers[i].AcsUserId);
                var person = new Azure.Communication.Chat.ChatParticipant(newUser)
                { DisplayName = br.lstUsers[i].DisplayName };
                lstParticipant.Add(person);
            }

            //Check if threadId already exists, if yes then do not create new, if no create new one.
            if (string.IsNullOrEmpty(br?.AcsChatThreadId))
            {

                string acsChatThreadId = await _acsService.CreateOnetoOneChatThreadId("OnetoOneChat", loggedInUserAcstoken, lstParticipant);
                GetOnetoOneChatThreadIdResponse brn = new();
                string reqr = "{\"userExId1\":\"" + req?.UserExId1 + "\",\"userExId2\":\"" + req?.UserExId2 + "\",\"acsChatThreadId\":\"" + Convert.ToString(acsChatThreadId) + "\",\"role\"" + ":\"" + req?.Role + "\"}";
                var resp = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_OnetoOne_Chat_ThreadId", reqr, jsonSecurity);
                brn = HTJsonSerialiser.Deserialise<GetOnetoOneChatThreadIdResponse>(res);
                br.AcsChatThreadId = Convert.ToString(acsChatThreadId);
            }
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpGet("GetLoggedInUserACSToken/{UserExId}")]
    public async Task<IActionResult> GetLoggedInUserACSToken(string UserExId)
    {
        baseResponse br = new() { Status = "Success" };
        try
        {
            string acsUid = string.Empty;
            acsUid = (await _acsService.GetUserAndStoreAcsId(UserExId, _dbSvc))?.AcsUserId;
            var result = await _acsService.GetUserTokenAsync(acsUid);
            br.Message = Convert.ToString(result?.Token);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GetResidentsLastVitalsUpdateReport")]
    [ProducesResponseType(typeof(GetResidentsLastVitalCaptureReportResponse), 200)]
    public async Task<IActionResult> GetResidentsLastVitalsUpdateReport([FromBody] GetResidentsLastVitalCaptureReportRequest req)
    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("sp_Report_GetResidents_LastVitals_new", json, jsonSecurity);
            br.Message = res;
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("GetFeedBackDetailsList")]
    [ProducesResponseType(typeof(GetHTuserFeedbackDetailsResponse), 200)]
    public async Task<IActionResult> GetFeedBackDetailsList([FromBody] GetHTuserFeedbackDetailsRequest req)

    {
        GetHTuserFeedbackDetailsResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_FeedBack_Details_List", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<GetHTuserFeedbackDetailsResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }

        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("UpsertFeedbackDetails")]
    public async Task<IActionResult> UpsertFeedbackDetails([FromBody] UpsertHTuserFeedbackRequest req)
    {
        baseResponse br = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Feedback_Details", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<baseResponse>(res);
        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (new JsonResult(br));
    }

    [AllowAnonymous]
    [HttpPost("ResetUserPassword")]
    public async Task<InviteUserResponse> ResetUserPassword([FromBody] ChangeUserPasswordRequest req)
    {
        InviteUserResponse br = new();
        var brEmailTemplate = new SharedListItem();

        try
        {
            string Newpassword = await _emailSerive.RandomString(8);
            req.NewPassword = Newpassword;

            if (string.IsNullOrEmpty(req.UserExId))
                throw new DllNotFoundException("UserExId not found in request");

            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_User_Reset_Password", json, jsonSecurity);
            br = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<InviteUserResponse>(res) : new InviteUserResponse());

            brEmailTemplate = await _emailSerive.GetEmailTemplate("ResetPassword");
            string emailTemplate = string.Format(brEmailTemplate.Description, br.FirstName, br.Email, Newpassword, _portalUrl);
            var result = await _emailSerive.SendEmail(emailTemplate, br.Email, brEmailTemplate.Category, _portalUrl);

        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (br);
    }

    [AllowAnonymous]
    [HttpPost("ResetPassword")]
    public async Task<ResetUserPasswordResponse> ResetPassword([FromBody] ResetUserPasswordRequest req)
    {
        ResetUserPasswordResponse br = new();
        var brEmailTemplate = new SharedListItem();

        try
        {
            string Newpassword = await _emailSerive.RandomString(8);
            req.NewPassword = Newpassword;

            if (string.IsNullOrEmpty(req.Email))
                throw new DllNotFoundException("Email not found in request");

            // var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Reset_user_Password", json, string.Empty);
            br = (!string.IsNullOrEmpty(res) ? HTJsonSerialiser.Deserialise<ResetUserPasswordResponse>(res) : new ResetUserPasswordResponse());
            if (br.Status == "Valid")
            {
                brEmailTemplate = await _emailSerive.GetEmailTemplate("ResetUserPassword");
                string emailTemplate = string.Format(brEmailTemplate.Description, br.Details, br.Email, Newpassword, _portalUrl);
                var result = await _emailSerive.SendEmail(emailTemplate, br.Email, brEmailTemplate.Category, _portalUrl);
            }
            else
            {
                br.Status = "Error";
                br.Message = "User email not found.";
            }

        }
        catch (Exception ex)
        {
            br.FromException(ex);
        }
        return (br);
    }

}
