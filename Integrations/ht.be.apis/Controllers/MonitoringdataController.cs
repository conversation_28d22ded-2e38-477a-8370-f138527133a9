﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Reflection.Metadata;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using GemBox.Document;
using GemBox.Document.Tables;
using ht.be.apis.Extensions;
using ht.be.apis.Services;
using ht.be.apis.Utils;
using ht.common.backend.shared;
using ht.common.backend.shared.helpers;
using ht.data.common;
using ht.data.common.Telehealth;
using ht.data.common.Users;
using ht.data.common.Wounds;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Devices.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using static System.Net.WebRequestMethods;
using static PeterPiper.Hl7.V2.Schema.XmlParser.HL7v2Xsd;
using Color = GemBox.Document.Color;
using Environment = System.Environment;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MonitoringDataController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        AcsService _acsService;
        BlobHelper _blobHelper;
        ParallelOptions _parallelOptions;
        PartnerService _partnerService;
        ILogger<MonitoringDataController> _logger;
        public static string iotConnString = string.Empty;
        public static TransportType _tt = TransportType.Mqtt;
        public static string _accountName;
        public static string _accountKey;
        public static string _usersUrl;
        public static string _userSasToken;

        public static string StorePath;// @"https://htappuat.blob.core.windows.net/pub/docs";
        public static string DocReport = "HealthTeamsDiagnosticReport.docx";
        public static string DocProgressNote = "HealthTeamsProgressNote.docx";
        public static string DocHl7Report = "HealthTeamsHl7Report.docx";
        public static string Lic = "DN-2022Apr25-vBukBWLH2kUR0fYZpMmHgyRgz/bOjbSxacxUUxcg/yigwwASdxVI8QWZsxysiTL4MCmi9ww9yroVHEn3awQX6Xivs2w==A";

        public static string UsersUrl = Environment.GetEnvironmentVariable("BlobUsersUrl");
        public static string UserSasToken = Environment.GetEnvironmentVariable("BlobUsersSasToken");

        private ILogger log;


        private string _authAudienceId = null;
        public MonitoringDataController(

            DBService dbSvc,
            TokenService tkSvc,
            AcsService acsSvc,
            BlobHelper blobHelper,
            PartnerService partnerService,
            ILogger<MonitoringDataController> logger)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
            _acsService = acsSvc;
            _authAudienceId = common.backend.shared.Globals.Properties["AuthAudience"];
            _parallelOptions = new() { MaxDegreeOfParallelism = 3 };
            _blobHelper = blobHelper;
            _partnerService = partnerService;
            _logger = logger;
            _dbSvc._logger = logger;
            _userSasToken = UserSasToken;
            _usersUrl = UsersUrl;
        }

        [AllowAnonymous]

        [HttpPost("UpsertMonitoringData")]
        public async Task<IActionResult> UpsertMonitoringData([FromBody] UpsertMonitoringDataRequest req)

        {
            UpsertMonitoringDataResponse br = new();

            //  ht.monitoring.HTClient.iotConnString = IoTDeviceService.IoTConnStr;// "HostName=read-hub.azure-devices.net;DeviceId=testDevice2;SharedAccessKey=nrfqdTYBsfpgk6Nf/1wvkVgEdQp0k19y888oqXyMez4=";
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var json = HTJsonSerialiser.Serialise(req);
            if (string.IsNullOrEmpty(req.Data.ExTaskId))
            {
                req.Data.ExTaskId = Guid.Empty.ToString();
            }
            if (string.IsNullOrEmpty(req.Data.ExTransactionId))
            {
                req.Data.ExTransactionId = Guid.NewGuid().ToString();
            }
            dynamic jsonSecuData = JsonConvert.DeserializeObject(jsonSecurity);
            req.Data.ExNurseId = jsonSecuData.userExId;

            ht.monitoring.HTClient.iotConnString = IoTDeviceService.IoTConnStr;// "HostName=read-hub.azure-devices.net;DeviceId=testDevice2;SharedAccessKey=nrfqdTYBsfpgk6Nf/1wvkVgEdQp0k19y888oqXyMez4=";
            var client = new ht.monitoring.HTClient();
            //  var lst = client.SendResultsAsync(req.Data).GetAwaiter().GetResult();
            var lst = await SendResultsAsync(req.Data);
            client = null;
            if (lst.Count == 0)
            {
                br.Status = "Success";
                br.Message = "Vitals data uploaded successfully.";
            }
            else
            {
                br.Status = "Error";
                br.Message = "Error while uploading the data. " + Convert.ToString(lst?[0].Message);
            }
            return (new JsonResult(br));
        }

        public byte[] Compress(byte[] data)
        {
            MemoryStream output = new MemoryStream();
            using (DeflateStream dstream = new DeflateStream(output, CompressionLevel.Optimal))
            {
                dstream.Write(data, 0, data.Length);
                dstream.Flush();

            }
            return output.ToArray();
        }
        //This is for sending the data to iotHub.

        public async Task<List<Exception>> SendResultsAsync(TelehealthMonitorData data)
        {
            var lst = new List<Exception>();
            var iotConnString = IoTDeviceService.IoTConnStr;//"HostName=read-hub-uat.azure-devices.net;DeviceId=UatDevice;SharedAccessKey=eNgOHpvhOTbAxFYZ0Zb8CD9bOszpBplOHWbT9pyJEus=";
            try
            {
                if (string.IsNullOrEmpty(iotConnString))
                    throw new ArgumentNullException("HT Iot Hub Connection string cannot be blank");
                if (data == null)
                    throw new NullReferenceException("Data cannot be null");
                if (string.IsNullOrEmpty(data.ExTaskId) || string.IsNullOrEmpty(data.UserExId))
                    throw new NullReferenceException("Task and User Ids cannot be null");

                //To cater for the Null exTaskId
                data.ExTaskId ??= Guid.Empty.ToString();

                // we need to upload the files first then the results second
                using (var clnt = DeviceClient.CreateFromConnectionString(
                        IotHubConnectionStringBuilder.Create(iotConnString).ToString(), _tt)
                      )
                {
                    byte[] compressBytes = Compress(Encoding.ASCII.GetBytes(HTJsonSerialiser.Serialise(data)));
                    // compressBytes = Encoding.ASCII.GetBytes(HTJsonSerialiser.Serialise(data));
                    // var encoding = "deflate";// "utf-8";//"deflate";//
                    using (var msg = new Message(compressBytes)
                    {
                        ContentType = "application/json",
                        ContentEncoding = "deflate",
                        To = "all-monitoring-data"
                    })
                    {
                        msg.Properties.Add("MessageType", "MonitorResults");
                        msg.Properties.Add("TaskId", data.ExTaskId);
                        msg.Properties.Add("UserId", data.UserExId);
                        msg.Properties.Add("TransId", data.ExTransactionId);
                        await clnt.SendEventAsync(msg);

                    }
                    clnt.CloseAsync();


                }

                GC.Collect();
            }
            catch (Exception ex)
            {
                lst.Add(ex);
            }
            return lst;
        }



        //Generate monitoring result pdf
        [AllowAnonymous]
        [HttpGet("GetMonitoringData/{ExTransactionId}")]
        public async Task<IActionResult> GetMonitoringData(string ExTransactionId)
        {
            GetTeleHealthDetailsResponse br = new GetTeleHealthDetailsResponse { Status = "Success" };
            try
            {
                baseRequest req = new();
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Monitoring_Data_by_ExTransactionId", ExTransactionId, jsonSecurity);
                br = HTJsonSerialiser.Deserialise<GetTeleHealthDetailsResponse>(res);

                #region for Monitoring data get accessible urls by using AskForAccess method
                if (br.Resident?.Monitoring?.Count > 0)
                {
                    var token = CancellationToken.None;
                    var md = br.Resident.Monitoring;

                    if (md?.Count > 0)
                    {
                        await Parallel.ForEachAsync(md, _parallelOptions, async (item, token) =>
                        {
                            await Parallel.ForEachAsync(item.Readings, _parallelOptions, async (reading, token) =>
                            {
                                if (reading.Files?.Count > 0)
                                {
                                    foreach (var f in reading.Files)
                                    {
                                        var res = await AskForAccessAsync(new AskForAccessRequest { Files = f.FileUrls });
                                        f.FileUrls = res?.Files;
                                    }
                                }

                            });

                        });
                    };
                };





                #region get report HTML
                baseResponse brs = new();
                var jsonreq = HTJsonSerialiser.Serialise(br);
                var reshtml = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Vital_Report_Html", jsonreq, jsonSecurity);
                brs = HTJsonSerialiser.Deserialise<baseResponse>(reshtml);
                #endregion
                ComponentInfo.SetLicense(Lic);

                // Save to MemoryStream as PDF
                var document = DocumentModel.Load(new MemoryStream(System.Text.Encoding.UTF8.GetBytes(brs.Message)), LoadOptions.HtmlDefault);
                using var memoryStream = new MemoryStream();
                document.Save(memoryStream, new PdfSaveOptions());
                memoryStream.Position = 0; // Reset stream position to the beginning



                #endregion

                // Upload to Azure Blob Storage
                //string accountName = accountName;

                string blobName = "ResidentVitalsReport.pdf";

                var dt = br.Resident.Monitoring[0]?.DateCreatedUtc?.ToString("yyyyMMddHHmm");
                var fname = $"{br.Resident.Monitoring[0]?.UserExId.ToUpper()}/reports/{br.Resident.Facility}_{br.Resident.LastName}_{br.Resident.FirstName}_{dt}.pdf";

                // Get the container client
                string filepath = Globals.Properties["blobusersurl"];
                string storageConnectionString = Globals.Properties["blobusersConns"];
                BlobContainerClient containerClient = new BlobContainerClient(storageConnectionString, "users");
                memoryStream.Position = 0;

                var blobClient = containerClient.GetBlobClient(fname);


                await blobClient.UploadAsync(memoryStream, overwrite: true);
                var resp = await AskForYearAccessAsync(filepath + "/" + fname);
                br.Resident.Monitoring[0].ReportUrl = resp;

                //Update the report URL
                baseResponse brupdate = new();
                //var jsonupreq = HTJsonSerialiser.Serialise("{\"reportUrl:\""+ resp +",\"exTransactionId:\""+ ExTransactionId + "}");
                //HTJsonSerialiser.Serialise
                var jsonupreq = ($"{{\"reportUrl\":\"{resp}\",\"exTransactionId\":\"{ExTransactionId}\"}}");
                var resuphtml = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_Vital_Report_URL", jsonupreq, jsonSecurity);
                brupdate.Message = resp;

                return (new JsonResult(brupdate));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }


        [AllowAnonymous]
        [HttpPost("GetMonitoringDataList")]

        public async Task<IActionResult> GetMonitoringDataList([FromBody] GetMonitoringDataListRequest req)
        {
            GetMonitoringDataListResponse br = new GetMonitoringDataListResponse();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Monitoring_Data_List_by_UserId", json, jsonSecurity);
                br.Items = HTJsonSerialiser.Deserialise<List<TelehealthMonitorData>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpGet("GetUserLastVitals/{userexId}")]
        public async Task<IActionResult> GetUserLastVitals(string userexId)
        {
            TelehealthMonitorData br = null;
            baseRequest req = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_Monitoring_Data_Last_Brief_by_UserId", userexId, jsonSecurity);
                var resp = HTJsonSerialiser.Deserialise<TelehealthMonitorData>(res);


                return (new JsonResult(resp));
            }
            catch (Exception ex)
            {

                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]

        [HttpGet("GetResidentThresholds/{exResidnetId}")]

        public async Task<IActionResult> GetResidentThresholds(string exResidnetId)
        {
            GetResidentThresholdsResponse br = new();
            baseRequest req = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_MonitorData_Get_Threshholds_ByResidentId", exResidnetId, jsonSecurity);
                var resp = new GetResidentThresholdsResponse
                {
                    MonitoringThresholds = HTJsonSerialiser.Deserialise<MonitoringThresholds>(res)
                };


                return (new JsonResult(resp));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpPost("GetResidentWounds")]

        public async Task<IActionResult> GetResidentWounds([FromBody] GetWoundRequest req)
        {
            GetWoundsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Resident_Wound_List", json, jsonSecurity);
                br.Wounds = HTJsonSerialiser.Deserialise<List<Wound>>(res);
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpPost("GetWoundDetails")]
        public async Task<IActionResult> GetWoundDetails([FromBody] GetWoundRequest req)
        {
            GetWoundsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Wound_Details", json, jsonSecurity);
                br.Wounds = HTJsonSerialiser.Deserialise<List<Wound>>(res);

                var md = br.Wounds;
                var token = CancellationToken.None;
                if (md?.Count > 0)
                {
                    await Parallel.ForEachAsync(md, _parallelOptions, async (item, token) =>
                    {
                        await Parallel.ForEachAsync(item.WoundDetails, _parallelOptions, async (woundDetails, token) =>
                        {
                            if (woundDetails.Files?.Count > 0)
                            {
                                foreach (var f in woundDetails.Files)
                                {
                                    var res = await AskForAccessAsync(new AskForAccessRequest { Files = f.FileUrls });
                                    f.FileUrls = res?.Files;
                                }
                            }

                        });

                    });
                };

                if (md?.Count > 0)
                {
                    await Parallel.ForEachAsync(md, _parallelOptions, async (item, token) =>
                    {
                        await Parallel.ForEachAsync(item.WoundDetails, _parallelOptions, async (woundDetails, token) =>
                        {
                            if (woundDetails.WoundImages?.Count > 0)
                            {
                                foreach (var f in woundDetails.WoundImages)
                                {
                                    var res = await AskForAccessAsync(f.FileUrl);
                                    f.FileUrl = res;
                                }
                            }

                        });

                    });
                };
                if (md?.Count > 0)
                {
                    await Parallel.ForEachAsync(md, _parallelOptions, async (item, token) =>
                    {
                        await Parallel.ForEachAsync(item.WoundDetails, _parallelOptions, async (woundDetails, token) =>
                        {
                            if (!string.IsNullOrEmpty(woundDetails.AreaCalculationImage))
                            {
                                var res = await AskForAccessAsync(woundDetails.AreaCalculationImage);
                                woundDetails.AreaCalculationImage = res;
                            }
                        });

                    });
                };
                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        [AllowAnonymous]
        [HttpPost("UpsertWoundDetails")]
        public async Task<IActionResult> UpsertWoundDetails([FromBody] UpsertWoundDetailsRequest req)
        {
            UpsertWoundDetailsResponse br = new();
            try
            {
                var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Upsert_Wound", json, jsonSecurity);
                br.Wounds = HTJsonSerialiser.Deserialise<List<Wound>>(res);

                #region Partner integration 
                try
                {
                    if (br.Wounds.Count > 0)
                    {
                        var partnerMappingDetails = await _partnerService.GetFacilityPartnerMapping(HttpContext.Request.Headers["x-ExFacilityId"].FirstOrDefault(), req.Wound.ExResidentId);
                        if (_partnerService.IsPartnerTheLookOut(partnerMappingDetails) || _partnerService.IsPartnerMedtech(partnerMappingDetails))
                        {
                            //get wound from db so we have both the header record and the new detail record
                            var getWoundRequest = new GetWoundRequest { ExWoundId = br.Wounds[0].ExWoundId };
                            var getWoundJson = HTJsonSerialiser.Serialise(getWoundRequest);
                            var getWoundRes = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Wound_Details", getWoundJson, jsonSecurity);
                            var woundList = HTJsonSerialiser.Deserialise<List<Wound>>(getWoundRes);
                            if (woundList.Count > 0)
                            {
                                var wound = woundList[0];
                                if (wound.WoundDetails is not null)
                                {
                                    //get access tokens for all images we are sending links to
                                    var woundDetails = wound.WoundDetails[0];
                                    if (woundDetails.WoundImages?.Count > 0)
                                    {
                                        int expiry = 1051200; // 2 years
                                        foreach (var f in woundDetails.WoundImages)
                                        {
                                            var urlWithAccess = await AskForAccessAsync(f.FileUrl, expiry);
                                            f.FileUrl = urlWithAccess;
                                        }
                                    }
                                }
                                dynamic user = JsonConvert.DeserializeObject(jsonSecurity);

                                if (_partnerService.IsPartnerTheLookOut(partnerMappingDetails))
                                    //send wound details to TLW
                                    await _partnerService.SendWoundAssessment(wound, (string)user.userExId, req.Wound.ExResidentId, partnerMappingDetails);

                                if (_partnerService.IsPartnerMedtech(partnerMappingDetails))
                                {
                                    //send wound details to Medtech
                                    var medtech = new MedtechIntegrationService(_dbSvc, _logger, _partnerService);
                                    await medtech.SendWoundAssessment(wound, (string)user.userExId, req.Wound.ExResidentId, partnerMappingDetails);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //we don't want to fail normal processing if the integration fails, so log the error and continue
                    _logger.LogError($"Error while sending wound details to partner. ExWoundId: {br.Wounds[0].ExWoundId}. Error: {ex.ToString()}");
                }
                #endregion

                return (new JsonResult(br));
            }
            catch (Exception ex)
            {
                br.FromException(ex);
                return (new JsonResult(br));
            }
        }

        private async Task<AskForAccessResponse> AskForAccessAsync(AskForAccessRequest req)
        {
            AskForAccessResponse br = new AskForAccessResponse { Files = new List<HealthTeamsFile>() };
            if (req.Files?.Count == 0)
                return br;
            try
            {
                br.Files = req.Files;
                foreach (var f in br.Files)
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(f.FileUrl) && f.FileUrl.StartsWith("https://"))
                        {
                            var key = await _blobHelper.GetSasTokenfor2Years(f.FileUrl, 2);
                            if (string.IsNullOrEmpty(key))
                                throw new NullReferenceException("Token cannot be null");
                            f.FileUrl = f.FileUrl + "?" + key;
                        }
                        if (!string.IsNullOrEmpty(f.FileThumbUrl) && f.FileThumbUrl.StartsWith("https://"))
                        {
                            var key = await _blobHelper.GetSasTokenfor2Years(f.FileThumbUrl, 2);
                            if (string.IsNullOrEmpty(key))
                                throw new NullReferenceException("Thumb Token cannot be null");
                            f.FileThumbUrl = f.FileThumbUrl + "?" + await _blobHelper.GetSasTokenfor2Years(f.FileThumbUrl, 2);
                        }
                        else
                        {
                            f.FileThumbUrl = f.FileUrl;
                        }
                    }
                    catch (Exception fex)
                    {
                        if (!_blobHelper.CheckIfBlobExists(f.FileUrl))
                            f.Status = $"ERROR: File does not exist {f.FileUrl}\r\n" + fex.Message;
                    }
                }
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }
            return (br);
        }

        private async Task<string> AskForAccessAsync(string fileUrl, int minsValidFor = 30)
        {
            baseResponse br = new();
            try
            {
                if (!string.IsNullOrEmpty(fileUrl) && fileUrl.StartsWith("https://"))
                {
                    var key = await _blobHelper.GetSasToken(fileUrl, minsValidFor);
                    if (string.IsNullOrEmpty(key))
                        throw new NullReferenceException("Token cannot be null");
                    fileUrl = fileUrl + "?" + key;
                }
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return fileUrl;
        }

        private async Task<string> AskForYearAccessAsync(string fileUrl, int minsValidFor = 30)
        {
            baseResponse br = new();
            try
            {
                if (!string.IsNullOrEmpty(fileUrl) && fileUrl.StartsWith("https://"))
                {
                    var key = await _blobHelper.GetSasTokenfor2Years(fileUrl, minsValidFor);
                    if (string.IsNullOrEmpty(key))
                        throw new NullReferenceException("Token cannot be null");
                    fileUrl = fileUrl + "?" + key;
                }
            }
            catch (Exception ex)
            {
                br.FromException(ex);
            }

            return fileUrl;
        }
    }
}
