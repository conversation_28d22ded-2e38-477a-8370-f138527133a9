﻿using ht.be.apis.models;
using ht.be.apis.Services;
using ht.be.apis.Utils;
using ht.common.backend.shared;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models;
using ht.common.backend.shared.models.sys;
using ht.data.common.B2C;
using ht.data.common.Users;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using ht.be.apis.Extensions;
using ht.data.common;
using Microsoft.Extensions.Logging;
using ht.data.common.Events;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class EventController : ControllerBase
    {

        DBService _dbSvc;
        TokenService _tokenService;
        ILogger _log;

        private string _authAudienceId = null;
        public EventController(DBService dbSvc, TokenService tkSvc,ILogger log)
        {
            _dbSvc = dbSvc;
            _tokenService = tkSvc;
        }

        
        [Route("UpsertEvent")]

        [HttpPost]
        public async Task<IActionResult> UpsertEvent([FromBody] HTEventRequest req)

        {
            HTEventResponse br = new() { Status = "Success",htEvent = new() };
            try
            {
                var json = HTJsonSerialiser.Serialise(req.htEvent);
                var res = await _dbSvc.ExecSprocWithJsonAsync("sp_Events_Upsert", json);
                br.htEvent.ExEventId = res;
            }
            catch (Exception ex)
            {
                _log.LogError(ex, "ERROR: BE-API UpsertEvent");
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }

        
        [HttpGet("GetEvent/{exEventId}")]
        public async Task<IActionResult> GetEvent(string exTaskId)

        {
            HTEventResponse br = new() { Status = "Success", htEvent = new() };
            try
            {
                var res = await _dbSvc.ExecSprocWithIdAsync("sp_Event_Get", exTaskId);
                br.htEvent = HTJsonSerialiser.Deserialise<HTEvent>(res);
            }
            catch (Exception ex)
            {
                _log.LogError(ex, "ERROR: BE-API GetEvent");
                br.FromException(ex);
            }
            return (new JsonResult(br));
        }


    }

}
