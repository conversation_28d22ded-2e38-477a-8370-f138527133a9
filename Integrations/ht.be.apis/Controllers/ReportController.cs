﻿using ht.be.apis.Services;
using ht.common.backend.shared;
using ht.common.backend.shared.models;
using ht.common.backend.shared.models.sys;
using rep=ht.data.common.Reports;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ht.be.apis.Utils;
using ht.data.common.Reports;
using Microsoft.Graph;
using ht.data.common.Shared;
using ht.data.common;

namespace ht.be.apis.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReportController : ControllerBase
    {
        
        private TelemetryClient telemetry;
        private PBiEmbedService _pbiSvc;
        private DBService _dbSvc;

        public ReportController(TelemetryClient telemetry, PBiEmbedService pbiSvc, DBService dbSvc)
        {
            this.telemetry = telemetry;
            this._pbiSvc = pbiSvc;
            this._dbSvc =  dbSvc;
        }

        [AllowAnonymous]
        [Route("GetReport")]
        [HttpPost]
        [ProducesResponseType(200, Type = typeof(rep.GetReportResponse))]
        
        public async Task<IActionResult> GetReport([FromBody] rep.GetReportRequest req)
        {
            var br = new rep.GetReportResponse { Status = "Success" };
            try
            {
                var json = await _dbSvc.ExecSprocWithIdAsync("sp_Get_Report_ByName", req.ReportName);
                var dbReport = HTJsonSerialiser.Deserialise<rep.DBReport>(json);
                
                var reportParams = _pbiSvc.GetEmbedParams(new Guid(dbReport.PbiWorkspaceId), new Guid(dbReport.PbiReportId));
                br.Report = reportParams;

            }
            catch (Exception ex)
            {
                this.telemetry.TrackException(ex);
                br.FromException(ex);

            }
            return new JsonResult(br);
        }

        [AllowAnonymous]
        [Route("GetReportList")]
        [HttpPost]
        [ProducesResponseType(200, Type = typeof(SharedListResponse))]

        public async Task<IActionResult> GetReportList([FromBody] baseRequest req)
        {
            var br = new SharedListResponse { Status = "Success" };
            try
            {
                var json = await _dbSvc.ExecSprocWithIdAsync("sp_Get_Report_List", req.ExFacilityId ??"");
                br = HTJsonSerialiser.Deserialise<SharedListResponse>(json);
            }
            catch (Exception ex)
            {
                this.telemetry.TrackException(ex);
                br.FromException(ex);

            }
            return new JsonResult(br);
        }
    }

}
