﻿using Hl7.Fhir.Support;
using ht.be.apis.Extensions;
using ht.be.apis.models;
using ht.be.apis.Services;
using ht.be.apis.Utils;
using ht.be.apis.Utils.Mhr;
using ht.common.backend.shared;
using ht.common.backend.shared.helpers;
using ht.common.backend.shared.models.security;
using ht.data.common;
using ht.data.common.B2C;
using ht.data.common.Shared;
using ht.data.common.Users;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using static ht.be.apis.models.LoginResponse;
using baseResponse = ht.data.common.baseResponse;

namespace ht.be.apis.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class ClientController : ControllerBase
{

    DBService _dbSvc;
    TokenService _tokenService;
    AcsService _acsService;
    EmailService _emailSerive;
    private readonly SmsService _smsService;
    GraphService _graphService;
    PartnerService _partnerService;
    private readonly IConfiguration _configuration;
    private string _authAudienceId = null;
    private string _portalUrl = null;
    private string _apimKey = null;
    private string _meetingUrl = string.Empty;
    private string _residentUrl = string.Empty;

    public ClientController(DBService dbSvc, TokenService tkSvc, AcsService acsSvc, GraphService svc, EmailService emailSvc, SmsService smsService, PartnerService partnerService, IConfiguration configuration)
    {
        _configuration = configuration;
        _dbSvc = dbSvc;
        _tokenService = tkSvc;
        _acsService = acsSvc;
        _authAudienceId = Globals.Properties["AuthAudience"];
        _portalUrl = common.backend.shared.Globals.Properties["PortalUrl"];
        _meetingUrl = common.backend.shared.Globals.Properties["MeetingUrl"];
        _apimKey = common.backend.shared.Globals.Properties["APIMKey"];
        _emailSerive = emailSvc;
        _smsService = smsService;
        _graphService = svc;
        _partnerService = partnerService;
        _residentUrl = common.backend.shared.Globals.Properties["ResidentUrl"];
    }

    [AllowAnonymous]
    [Route("Discovery")]

    [HttpPost]
    public async Task<IActionResult> Discovery([FromBody] DiscoveryRequest req)
    {
        //var userInfo = HttpContext.Request.ExtractClientUserRoles();

        //need to call AcsId + GetVitals for the User.
        var verWound = await _dbSvc.ExecSprocAsync("sp_Version_Update", new Dictionary<string, string> { { "VersionName", "Wound" } });
        //Drop downs

        var resp = new DiscoveryResponse
        {
            BaseUrl = "https://ht.bff.api.uat.azurewindows.net",
            Properties = new Dictionary<string, string> { { "WoundVersion", verWound }, { "pk", TokenService._rsaPublicKey } }
        };

        return (new JsonResult(resp));
    }

#if DEBUG
    [AllowAnonymous] //this is coming out shortly - testing.
#endif
    [HttpPost("B2CGetAppToken")]
    public async Task<IActionResult> B2CGetAppToken([FromBody] object obj)
    {
        B2CGetAppTokenRequest req = HTJsonSerialiser.Deserialise<B2CGetAppTokenRequest>(obj.ToString());
        string audience = "https://bffapis.healthteams.com.au";

        string aadIssuer = Globals.Properties["AuthAADIssuer"];
        string aadAudience = Globals.Properties["AuthAudience"];

        B2CGetAppTokenResponse br = new();
        try
        {
            #region TOken Work
            var ireq = new LoginRequest
            {
                ExternalId = req.Oid,
                Idtype = "AAD",
                UserEmail = req.Email,
                FirstName = req.FirstName,
                LastName = req.LastName,
                UserMobile = req.Mobile,
                UserMobileCC = req.MobileCC
            };
            /*
            if (ireq.UserEmail == null || ireq.FirstName == null || ireq.LastName == null)
               throw new ArgumentNullException("Authenticate failed - inbound values not correct");
            */
            #endregion



            if (!string.IsNullOrEmpty(ireq?.ExternalId))
            {
                var json = HTJsonSerialiser.Serialise(ireq);
                var res = await _dbSvc.ExecSprocWithJsonAsync("sp_User_B2CAuthenticate", json);

                br.UserRoles = res; //Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(res));


            }
            else
            {
                br.UserMessage = "Authentication failed - please supply login details";
            }
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.UserMessage = ex.Message;

            return (new ConflictObjectResult(br));
        }
    }


#if DEBUG
    [AllowAnonymous] //this is coming out shortly - testing.
#endif
    [HttpPost("Authenticate")]
    public async Task<IActionResult> Authenticate([FromHeader(Name = "Authentication")] string tok, [FromBody] LoginRequest req)
    {
        string audience = "https://bffapis.healthteams.com.au";

        string aadIssuer = Globals.Properties["AuthAADIssuer"];
        string aadAudience = Globals.Properties["AuthAudience"];

        LoginResponse br = new();
        try
        {
            #region TOken Work
            //get the system token
            tok = tok.Replace("Bearer ", "", StringComparison.InvariantCultureIgnoreCase);
            //var result = _tokenService.ValidateAADToken(tok,aadIssuer,aadAudience);
            var result = _tokenService.DeserialiseToken(tok);

            req = req ?? new LoginRequest();
            req.ExternalId = result.Claims?.Where(x => x.Type == "oid").Select(x => x.Value)?.FirstOrDefault();
            req.Idtype = "AAD";
            req.UserEmail = req.UserEmail ?? result.Claims.Where(x => x.Type == "email").Select(x => x.Value)?.FirstOrDefault();
            req.FirstName = req.FirstName ?? result.Claims.Where(x => x.Type == "givenName").Select(x => x.Value)?.FirstOrDefault();
            req.LastName = req.LastName ?? result.Claims.Where(x => x.Type == "family_name").Select(x => x.Value)?.FirstOrDefault();
            req.InviteCode = req.InviteCode ?? result.Claims.Where(x => x.Type == "usercode").Select(x => x.Value)?.FirstOrDefault();
            req.InviteFacility = req.InviteFacility ?? result.Claims.Where(x => x.Type == "facname").Select(x => x.Value)?.FirstOrDefault();
            req.UserMobile = req.UserMobile ?? result.Claims.Where(x => x.Type == "mobile").Select(x => x.Value)?.FirstOrDefault();
            req.UserMobileCC = req.UserMobileCC ?? result.Claims.Where(x => x.Type == "mobilecountry").Select(x => x.Value)?.FirstOrDefault();


            if (req.UserEmail == null || req.FirstName == null || req.LastName == null)
                throw new ArgumentNullException("Authenticate failed - inbound values not correct");
            #endregion



            if (!string.IsNullOrEmpty(req?.ExternalId))
            {
                var json = HTJsonSerialiser.Serialise(req);
                var res = await _dbSvc.ExecSprocWithJsonAsync("sp_User_Authenticate", json);
                br = HTJsonSerialiser.Deserialise<LoginResponse>(res);

                if (br != null && !string.IsNullOrEmpty(br.UserExId))
                {
                    var lst = br.Roles?.Select(x => x.Role.ToString()).ToList<string>();
                    //generate token
                    var customClaims = new List<Claim>
                    {
                        new Claim("FirstName",br.FirstName??""),
                        new Claim("LastName",br.LastName??""),
                        new Claim("Email",br.Email??"")
                    };

                    br.TokenDetails = await _tokenService.ReturnNewSecurityToken(br.UserExId, audience, "App", lst, customClaims);
                }
                else
                    throw new MissingMemberException("BEAPIs - user not found exception.");

            }
            else
            {
                br.Errors = new List<string> { { "Login details are empty" } };
                br.Message = "Authentication failed - please supply login details";
            }
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br = await LoginResponse.FromException(br, ex) as LoginResponse;

            return (new JsonResult(br));
        }
    }

#if DEBUG
    [AllowAnonymous] //this is coming out shortly - testing.
#endif
    [HttpGet("ValidateToken")]
    public async Task<IActionResult> ValidateToken([FromHeader(Name = "x-client-tok")] string tok)
    {
        return new JsonResult(_tokenService.ValidateAppToken(tok));
    }

#if DEBUG
    [AllowAnonymous] //this is coming out shortly - testing.
#endif
    [HttpPost("UpsertUsers")]
    public async Task<IActionResult> UpsertUsers([FromBody] UpsertUsersRequest req)
    {
        UpsertUsersResponse br = new UpsertUsersResponse { UpsertUserResponses = new() };
        try
        {
            //get the system token
            //var tok = HttpContext.Request.Headers["Authentication"].First().Replace("Bearer ","",StringComparison.InvariantCultureIgnoreCase);

            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Users_Upsert_ForMeeting", json);
            br = HTJsonSerialiser.Deserialise<UpsertUsersResponse>(res);
            var bChanged = false;
            for (int i = 0; i < req.UpsertUserRequests?.Count; i++)
            {
                var user = br.UpsertUserResponses[i];
                var userReq = req.UpsertUserRequests[i];
                if ((string.IsNullOrEmpty(user.AcsToken)) || (user.AcsTokenExpiresUtc.HasValue && user.AcsTokenExpiresUtc < System.DateTime.UtcNow))
                {
                    bChanged = true;
                    userReq.UserExId = user.UserExId;
                    if (!string.IsNullOrEmpty(user.UserAcsId))
                    {
                        var r = await _acsService.GetUserTokenAsync(user.UserAcsId);
                        userReq.UserAcsId = user.UserAcsId;
                        userReq.AcsToken = r.Token;
                        userReq.AcsTokenExpiresUtc = r.TokenExpiresUtc;
                    }
                    else
                    {
                        var r = await _acsService.GetUserTokenAsync();
                        userReq.UserAcsId = r.UserId;
                        userReq.AcsToken = r.Token;
                        userReq.AcsTokenExpiresUtc = r.TokenExpiresUtc;
                    }
                }
            }
            if (bChanged)
            {
                res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Users_Upsert_ForMeeting", HTJsonSerialiser.Serialise(req));
                br = HTJsonSerialiser.Deserialise<UpsertUsersResponse>(res);
            }
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br = await UpsertUsersResponse.FromException(br, ex) as UpsertUsersResponse;

            return (new JsonResult(br));
        }
    }

#if DEBUG
    [AllowAnonymous] //this is coming out shortly - testing.
#endif
    [HttpGet("GetMeetingDetails/{meetingId}")]

    //TODO: validate token for meeting access
    public async Task<IActionResult> GetMeetingDetails(string meetingId)
    {
        GetMeetingDetailsResponse br = null;
        try
        {

            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Meeting_GetMeetingDetails", meetingId);
            br = HTJsonSerialiser.Deserialise<GetMeetingDetailsResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br = await GetMeetingDetailsResponse.FromException(br, ex) as GetMeetingDetailsResponse;

            return (new JsonResult(br));
        }
    }

    [Route("SecureCall")]
    [HttpGet]
    public async Task<IActionResult> SecureCall()

    {
        ht.common.backend.shared.models.baseResponse br = new()
        {
            Message = "Greetings from the Server, your security is working"
        };

        return (new JsonResult(br));
    }

    [AllowAnonymous] //this is coming out shortly - testing.
    [HttpPost("UpsertUserProfile")]
    public async Task<IActionResult> UpsertUserProfile([FromBody] HealthTeamsUser req)
    {
        UpsertUserProfileResponse br = new UpsertUserProfileResponse();
        try
        {
            //get the system token
            //var tok = HttpContext.Request.Headers["Authentication"].First().Replace("Bearer ","",StringComparison.InvariantCultureIgnoreCase);
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

            // If a health identifier is provided, we need to check if it is valid
            if (!String.IsNullOrWhiteSpace(req.IHI))
            {
                // Determine the role of the user of the profile being updated
                var exFacilityId = JsonDocument.Parse(jsonSecurity).RootElement.GetProperty("exFacilityId").GetString();
                var userExId = req.UserExId;
                try
                {
                    var parmsTest = new Dictionary<string, string> { { "exFacilityId", exFacilityId }, { "userExId", userExId } };
                    var userRole = await _dbSvc.ExecSprocAsync("sp_Get_User_Role", parmsTest);
                    if (userRole != null && (userRole == "facility" || userRole == "doctor" || userRole == "nurse"))
                    {
                        // We are updating the profile of a health provider, so we need to validate their HPII
                        if (!HealthIdentifierHelper.IsValid(req.IHI, IdentifierType.HPII))
                        {
                            br.Status = "Error";
                            br.Message = "The HPI-I Identifier is not in a valid format";
                            return (new JsonResult(br));
                        }
                    }
                    else
                    {
                        // If we were validating patient IHIs, we would do that here
                    }
                }
                catch (Exception ex)
                {
                    string test = ex.Message;
                }
            }

            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_User_Upsert_UserProfile", json, jsonSecurity);
            br.user = HTJsonSerialiser.Deserialise<HealthTeamsUser>(res);
            br.Status = "Success";
            return (new JsonResult(br));
        }
        catch (SqlException sqlex)
        {
            br.Message = sqlex.Message;
            br.Status = "Error";
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }


    //MICK: More can be added to return from this routine with additional parameters - e.g. 
    [AllowAnonymous]
    [HttpGet("GetUserProfileByExternalId/{externalId}")]
    public async Task<IActionResult> GetUserProfileByExternalId(string externalId)
    {
        GetUserProfileResponse br = new GetUserProfileResponse();
        var json = "{ \"userExternalId\":\"" + externalId + "\"}";
        try
        {
            baseRequest req = new();

            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Users_Get_UserProfile_ByExternalId", json, jsonSecurity);
            br.User = HTJsonSerialiser.Deserialise<HealthTeamsUser>(res);

            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]
    [HttpPost("InviteUser")]
    public async Task<IActionResult> InviteUser([FromBody] InviteUserRequest req)
    {
        var br = new InviteUserResponse();
        var brEmailTemplate = new SharedListItem();
        baseResponse bsr = new() { Status = "Success" };

        //BUG - no password hash was being stored for invited users. 2023-08-16
        string password = await _emailSerive.RandomString(8);
        req.PasswordHash = password;

        var json = HTJsonSerialiser.Serialise(req);

        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);

            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_UserInvite_Create", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<InviteUserResponse>(res);
            br.Status = "Success";
            //call to send the code
            if ((bool)!br.IsNewUser)
            {
                brEmailTemplate = await _emailSerive.GetEmailTemplate("InviteExisting");

                string emailTemplate = string.Format(brEmailTemplate.Description, req.FirstName, br.FacName, br.Role, _portalUrl);
                var result = await _emailSerive.SendEmail(emailTemplate, br.Email, brEmailTemplate.Category, _portalUrl);

            }
            else if ((bool)br.IsNewUser)
            {
                brEmailTemplate = await _emailSerive.GetEmailTemplate("InviteNew");
                // We will uncomment the following 
                //bsr.Message = await _graphService.CreateInvitedUser(br, true, password);
                // var reqjson = "{\"userExId\":\"" + br.UserExId + "\",\"exUserId\":\"" + bsr.Message + "\"}";
                //var resp = _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_resident_ADD", reqjson, jsonSecurity);
                string emailTemplate = string.Format(brEmailTemplate.Description, req.FirstName, br.FacName, br.Role, br.Email, password, _portalUrl);
                var result = await _emailSerive.SendEmail(emailTemplate, br.Email, brEmailTemplate.Category, _portalUrl);

                #region TLW_Staffer_LookUp
                // check partner exist on current HTC facility

                var parnter = await _partnerService.GetPartnerByNameFacility(req.ExFacilityId, Partner.TheLookOut.ToString());
                if (parnter != null)
                {
                    //check created user exist TLW
                    var staffer = await _partnerService.SearchTlwStafferByEmail(req.Email, parnter);
                    if (staffer != null)
                    {
                        await _partnerService.InserStaffToHTC(staffer.profile.id, br.UserId, parnter.PartnerId);
                    }
                }



                #endregion

            }


            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new NotFoundObjectResult(br));
        }
    }

    [AllowAnonymous]
    [HttpGet("GenerateLink/{mode}")]
    public async Task<IActionResult> GenerateLink(string mode, [FromQuery] string code, [FromQuery] string email, [FromQuery] string facName, [FromQuery] string approle, [FromQuery] string lastname)
    {
        var br = new ht.common.backend.shared.models.baseResponse();
        try
        {
            var url = $"b2c/v1/api/Identity/{(mode == "signup" ? "BuildSignupLink/" : "BuildSigninLink/")}{email}?code={code}&facname={facName}&approle={approle}&lastname={lastname}";
            using (var client = APIMManager.GetClient())
            {
                //call and get the link
                var resp = await client.GetAsync(url);
                resp.EnsureSuccessStatusCode();
                br.Message = await resp.Content.ReadAsStringAsync(); //should have the link right there.
                br.Status = "Success";
            };
            return (new JsonResult(br));

        }
        catch (Exception ex)
        {
            br = await ht.common.backend.shared.models.baseResponse.FromException(br, ex);
            return (new NotFoundObjectResult(br));
        }
    }

    [AllowAnonymous]
    [HttpGet("GenerateAndSendEmail/{mode}")]
    public async Task<IActionResult> GenerateAndSendEmail(string mode, [FromQuery] string code, [FromQuery] string email, [FromQuery] string facName, [FromQuery] string approle, [FromQuery] string lastname)
    {
        var br = new ht.common.backend.shared.models.baseResponse();
        try
        {
            var url = $"b2c/v1/api/Identity/{(mode == "signup" ? "SendSignupEmail/" : "SendSigninEmail/")}{email}?code={code}&facname={facName}&approle={approle}&lastname={lastname}";
            using (var client = APIMManager.GetClient())
            {
                //call and get the link
                var resp = await client.GetAsync(url);
                resp.EnsureSuccessStatusCode();
                br.Message = await resp.Content.ReadAsStringAsync(); //should have the link right there.
                br.Status = "Success";
            };
            return (new JsonResult(br));

        }
        catch (Exception ex)
        {
            br = await ht.common.backend.shared.models.baseResponse.FromException(br, ex);
            return (new NotFoundObjectResult(br));
        }
    }


    //#endregion



    //This routine is call for when users are part way through the sign up process and they need to enter their association code.
#if DEBUG
    [AllowAnonymous] //this is coming out shortly - testing.
#endif
    [ApiExplorerSettings(IgnoreApi = false)]
    [HttpPost("B2CRegisterCheck")]
    public async Task<IActionResult> B2cRegisterCheck([FromBody] B2CRegistrationCheckRequest req)
    {
        B2CRegistrationCheckResponse br = new();
        try
        {
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("sp_UserInvite_Validate", json);
            br = HTJsonSerialiser.Deserialise<B2CRegistrationCheckResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.Status = ex.Message;

            return (new JsonResult(br));
        }
    }

    //TODO: Madhav - why is this anonymous??
    [AllowAnonymous]
    [HttpGet("GetInvitedUsers/{facilityextId}")]

    public async Task<IActionResult> GetInvitedUsers(string facilityextId)
    {
        InvitedUsersResponse br = new();
        baseRequest req = new();
        try
        {
            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithIdAsync("dbo.sp_Get_InvitedUsers_ByfacilityExtId", facilityextId, jsonSecurity);
            var resp = new InvitedUsersResponse
            {
                InvitedUsers = HTJsonSerialiser.Deserialise<List<InviteUserResponse>>(res)
            };

            return (new JsonResult(resp));

        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]
    [HttpPost("InviteUserOnCall")]
    public async Task<IActionResult> InviteUserOnCall([FromBody] InviteUserOnCallRequest req)
    {
        var brEmailTemplate = new SharedListItem();
        var brGuestTokenRequest = new GuestTokenRequest();
        var br = new InviteUserOnCallResponse();
        var json = HTJsonSerialiser.Serialise(req);
        string meetingUrl = string.Empty;
        try
        {
            string audience = "https://bffapis.healthteams.com.au";


            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Invite_User_On_Call", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<InviteUserOnCallResponse>(res);

            if (br?.Status != "Error")
            {

                var logingResponse = new LoginResponse();
                var rs = new List<LoginResponse.UserFacilityRole>();

                var roles = new UserFacilityRole();
                roles.Role = br.Role + ":" + req.ExFacilityId;
                rs.Add(roles);

                var lst = rs?.Select(x => x.Role.ToString()).ToList<string>(); // br.Roles?.Select(x => x.Role.ToString()).ToList<string>();
                string userRoles = br.InviteLink.ToString();
                //generate token
                var customClaims = new List<Claim>
                    {
                        new Claim("FirstName",br.FirstName??""),
                        new Claim("LastName",br.LastName??""),
                        new Claim("Email",br.Email??"")
                    };


                //string idName, string tokenType, List<string> roles, string userRoles, DateTime taskDateTimeUtc, List<Claim> customClaims
                brGuestTokenRequest.ExGuestUserId = br.ExGuestUserId;
                brGuestTokenRequest.IdName = "App";
                brGuestTokenRequest.UserRoles = userRoles;
                brGuestTokenRequest.TaskDateTimeUtc = br.TaskDateTimeUTC;
                brGuestTokenRequest.Roles = lst;
                brGuestTokenRequest.CustomClaims = customClaims;
                var token =  ReturnGuestUserSecurityToken(brGuestTokenRequest);
                string MeetingCode = await _emailSerive.RandomString(6);
                br.InviteLink = _portalUrl + "telehealth?taskid=" + br.ExTaskId + "&id_token=" + token + "&type=external";
                _meetingUrl = _meetingUrl + "/" + MeetingCode;
                br.MeetingCode = MeetingCode;

                //Update actual meeting link in the database.
                var jsonr = HTJsonSerialiser.Serialise(br);
                var resp = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_Meeting_Link", jsonr, jsonSecurity);

                brEmailTemplate = await _emailSerive.GetEmailTemplate("InviteOnTeleHealth");
                string emailTemplate = string.Format(brEmailTemplate.Description, br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt"), req.FirstName, br.InvitedBy, br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt"), br.ResidentName, br.FacName, _meetingUrl);
               
                var result = await _emailSerive.SendEmail(emailTemplate, br.Email, brEmailTemplate.Category, _meetingUrl);

                // Send short meeting link to front end 
                br.InviteLink = _meetingUrl;
            }
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new NotFoundObjectResult(br));
        }
    }


    public string ReturnGuestUserSecurityToken(GuestTokenRequest brGuestTokenRequest)
    {

        customJWTToken res=null;
        try
        {

            var dt = DateTime.UtcNow;
            var expDt = dt.AddHours(12);

            if (brGuestTokenRequest.TaskDateTimeUtc > DateTime.UtcNow)
            {
                dt = brGuestTokenRequest.TaskDateTimeUtc.AddMinutes(-10);
                expDt = brGuestTokenRequest.TaskDateTimeUtc.AddHours(12);
            }

            var unixTimeSeconds = new DateTimeOffset(dt).ToUnixTimeSeconds();
            var issuer = _configuration["JwtIssuer"];
            var audience = _configuration["JwtAudience"];
            var key = Encoding.UTF8.GetBytes(_configuration["JwtKey"]);
            var signingCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha512Signature);

            var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Iat, unixTimeSeconds.ToString(), ClaimValueTypes.Integer64),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(ClaimTypes.NameIdentifier, brGuestTokenRequest.IdName),
            new Claim("ht_roles", Convert.ToBase64String(Encoding.UTF8.GetBytes(brGuestTokenRequest.UserRoles)))
        };

            if (brGuestTokenRequest.CustomClaims != null)
                claims.AddRange(brGuestTokenRequest.CustomClaims);

            if (brGuestTokenRequest.Roles != null)
                brGuestTokenRequest.Roles.ForEach(r => claims.Add(new Claim(ClaimTypes.Role, r)));

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = expDt,
                Issuer = issuer,
                Audience = audience,
                SigningCredentials = signingCredentials
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            var jwtToken = tokenHandler.WriteToken(token);
            return jwtToken;
        }
        catch
        {
            throw;
        }
    }

    [AllowAnonymous]
    [HttpPost("InviteUserOnCallSMS")]
    public async Task<IActionResult> InviteUserOnCallSMS([FromBody] InviteUserOnCallRequest req)
    {
        var brGuestTokenRequest = new GuestTokenRequest();
        var brEmailTemplate = new SharedListItem();
        var br = new InviteUserOnCallResponse();
        var json = HTJsonSerialiser.Serialise(req);
        string meetingUrl = string.Empty;
        try
        {
            string audience = "https://bffapis.healthteams.com.au";


            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Invite_User_On_Call", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<InviteUserOnCallResponse>(res);

            if (br?.Status != "Error")
            {

                var logingResponse = new LoginResponse();
                var rs = new List<LoginResponse.UserFacilityRole>();

                var roles = new UserFacilityRole();
                roles.Role = br.Role + ":" + req.ExFacilityId;
                rs.Add(roles);

                var lst = rs?.Select(x => x.Role.ToString()).ToList<string>(); // br.Roles?.Select(x => x.Role.ToString()).ToList<string>();
                string userRoles = br.InviteLink.ToString();
                //generate token
                var customClaims = new List<Claim>
                    {
                        new Claim("FirstName",br.FirstName??""),
                        new Claim("LastName",br.LastName??""),
                        new Claim("Email",br.Email??"")
                    };

                brGuestTokenRequest.ExGuestUserId = br.ExGuestUserId;
                brGuestTokenRequest.IdName = "App";
                brGuestTokenRequest.UserRoles = userRoles;
                brGuestTokenRequest.TaskDateTimeUtc = br.TaskDateTimeUTC;
                brGuestTokenRequest.Roles = lst;
                brGuestTokenRequest.CustomClaims = customClaims;
                var token = ReturnGuestUserSecurityToken(brGuestTokenRequest);

                //var token = await _tokenService.ReturnGuestUserSecurityToken(br.ExGuestUserId, audience, "App", lst, userRoles, (DateTime)br.TaskDateTimeUTC, customClaims);
                string MeetingCode = await _emailSerive.RandomString(6);
                br.InviteLink = _portalUrl + "telehealth?taskid=" + br.ExTaskId + "&id_token=" + token + "&type=external";
                _meetingUrl = _meetingUrl + "/" + MeetingCode;
                br.MeetingCode = MeetingCode;

                //Update actual meeting link in the database.
                var jsonr = HTJsonSerialiser.Serialise(br);
                var resp = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_Meeting_Link", jsonr, jsonSecurity);


                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"HealthTeams: Virtual Consult Invitation on {br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt")}");
                sb.AppendLine($"");
                sb.AppendLine($"Hello *{req.FirstName}*");
                sb.AppendLine($"");
                sb.AppendLine($"*{br.InvitedBy}* has invited you to join the virtual consult scheduled on {br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt")} for the resident _{br.ResidentName}_ from facility *{br.FacName}*.");
                sb.AppendLine($"");
                sb.AppendLine("Please click the following link to join the consult.\r\n");
                sb.AppendLine($"{_meetingUrl}");

                _smsService.SendToSms(br.Mobile, sb.ToString());



                br.InviteLink = _meetingUrl;
            }
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new NotFoundObjectResult(br));
        }
    }


    
    [AllowAnonymous]
    [HttpPost("SendVirtualConsultLinkToUser")]
    public async Task<IActionResult> SendVirtualConsultLinkToUser([FromBody] InviteUserOnCallRequest req)
    {
        var brEmailTemplate = new SharedListItem();
        var br = new InviteUserOnCallResponse();
        var json = HTJsonSerialiser.Serialise(req);
        string meetingUrl = string.Empty;
        try
        {
            string audience = "https://bffapis.healthteams.com.au";


            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Get_Virtual_Consult_Link_Details", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<InviteUserOnCallResponse>(res);

            if (br?.Status != "Error")
            {

                string MeetingCode = br.MeetingCode;
                _meetingUrl = _meetingUrl + "/" + MeetingCode;
                br.MeetingCode = MeetingCode;

                //Sen Email//

                brEmailTemplate = await _emailSerive.GetEmailTemplate("InviteOnTeleHealth");
                string emailTemplate = string.Format(brEmailTemplate.Description, br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt"), req.FirstName, br.InvitedBy, br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt"), br.ResidentName, br.FacName, _meetingUrl);
                var result = await _emailSerive.SendEmail(emailTemplate, br.Email, brEmailTemplate.Category, _meetingUrl);

                // Send SMS

                if (!string.IsNullOrEmpty(br.Mobile))
                {
                    if (!br.Mobile.StartsWith("+"))
                        br.Mobile = "+" + br.Mobile;
                    var smsNum = new PhoneNumbers.PhoneNumber();
                    var phUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
                    try
                    {
                        smsNum = phUtil.Parse(br.Mobile, null);
                    }
                    catch (Exception ex)
                    {
                        br.Message = Convert.ToString(ex.InnerException);
                    }

                    if (smsNum.HasCountryCode && smsNum.HasNationalNumber)
                    {
                        StringBuilder sb = new StringBuilder();
                        // sb.AppendLine($"HealthTeams: Virtual Consult Invitation on {br.UserTaskDateTimeUTC}");
                        // sb.AppendLine($"");
                        sb.AppendLine($"Hello {req.FirstName}");
                        sb.AppendLine($"");
                        sb.AppendLine($"{br.InvitedBy} has invited you to join the virtual consult scheduled on {br.UserTaskDateTimeUTC.ToString("dd/MM/yyyy hh:mm tt")} for the resident {br.ResidentName} from facility {br.FacName}.");
                        sb.AppendLine($"");
                        sb.AppendLine("Please click the following link to join the consult.\r\n");
                        sb.AppendLine($"{_meetingUrl}");

                        _smsService.SendToSms(br.Mobile, sb.ToString());
                    }
                }

                br.InviteLink = _meetingUrl;
            }
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new NotFoundObjectResult(br));
        }
    }

    [AllowAnonymous]
    [HttpPost("Authentication")]
    public async Task<IActionResult> Authentication([FromBody] AuthUserRequest req)
    {
        AuthUserResponse br = new();
        try
        {
            var json = HTJsonSerialiser.Serialise(req);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_User_ValidateUser", json, string.Empty);
            br = HTJsonSerialiser.Deserialise<AuthUserResponse>(res);
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new JsonResult(br));
        }
    }

    [AllowAnonymous]
    [HttpPost("SendResidentDirectLink")]
    public async Task<IActionResult> SendResidentDirectLink([FromBody] InviteUserOnCallRequest req)
    {
        var brGuestTokenRequest = new GuestTokenRequest();
        var brEmailTemplate = new SharedListItem();
        var br = new InviteUserOnCallResponse();
        var json = HTJsonSerialiser.Serialise(req);
        string meetingUrl = string.Empty;
        try
        {
            string audience = "https://bffapis.healthteams.com.au";

            // The withholdNotifications header can be used to stop the sending to email and sms
            var withholdNotification = HttpContext.Request.Headers["withholdNotifications"].FirstOrDefault();
            bool sendNotifications = true;
            if (!string.IsNullOrEmpty(withholdNotification) && withholdNotification.ToLower() == "true")
                sendNotifications = false;

            var jsonSecurity = HttpContext.Request.GetUserSecurityJson(req, true);
            var res = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Send_Resident_Direct_Link", json, jsonSecurity);
            br = HTJsonSerialiser.Deserialise<InviteUserOnCallResponse>(res);
            if (br?.Status != "Error")
            {

                var logingResponse = new LoginResponse();
                var rs = new List<LoginResponse.UserFacilityRole>();

                var roles = new UserFacilityRole();
                roles.Role = br.Role + ":" + req.ExFacilityId;
                rs.Add(roles);

                var lst = rs?.Select(x => x.Role.ToString()).ToList<string>(); // br.Roles?.Select(x => x.Role.ToString()).ToList<string>();
                string userRoles = br.InviteLink.ToString();
                //generate token
                var customClaims = new List<Claim>
                {
                    new Claim("FirstName",br.FirstName??""),
                    new Claim("LastName",br.LastName??""),
                    new Claim("Email",br.Email??"")
                };

                brGuestTokenRequest.ExGuestUserId = br.ExGuestUserId;
                brGuestTokenRequest.IdName = "App";
                brGuestTokenRequest.UserRoles = userRoles;
                brGuestTokenRequest.TaskDateTimeUtc = br.TaskDateTimeUTC;
                brGuestTokenRequest.Roles = lst;
                brGuestTokenRequest.CustomClaims = customClaims;
                var token = ReturnGuestUserSecurityToken(brGuestTokenRequest);
                //var token = await _tokenService.ReturnResidentDirectLinkGuestUserSecurityToken(br.ExGuestUserId, audience, "App", lst, userRoles, 24, customClaims);
                string MeetingCode = await _emailSerive.RandomString(6);
                br.InviteLink = _portalUrl + "resident/" + br.ExResidentId + "/res-dashboard" + "?id_token=" + token + "&type=external";
                _residentUrl = _residentUrl + "/" + MeetingCode;
                br.MeetingCode = MeetingCode;

                //Update actual meeting link in the database.
                var jsonr = HTJsonSerialiser.Serialise(br);
                var resp = await _dbSvc.ExecSprocWithJsonAsync("dbo.sp_Update_Resident_Direct_Link", jsonr, jsonSecurity);

                if (sendNotifications)
                {
                    //Send Email
                    brEmailTemplate = await _emailSerive.GetEmailTemplate("ResidentDirectLink");
                    string emailTemplate = string.Format(brEmailTemplate.Description, req.FirstName, br.InvitedBy, br.ResidentName, br.FacName, _residentUrl);
                    var result = await _emailSerive.SendEmail(emailTemplate, br.Email, brEmailTemplate.Category, _residentUrl);

                    //Send SMS if Mobile number is available
                    if (!string.IsNullOrEmpty(br.Mobile))
                    {
                        StringBuilder sb = new StringBuilder();
                        sb.AppendLine($"HealthTeams:  Resident Details");
                        sb.AppendLine($"");
                        sb.AppendLine($"Hello {req.FirstName}");
                        sb.AppendLine($"");
                        sb.AppendLine($"{br.InvitedBy} has sent you details of {br.ResidentName} resident from facility {br.FacName}.");
                        sb.AppendLine($"");
                        sb.AppendLine("Please click the following link to view the details.\r\n");
                        sb.AppendLine($"{_residentUrl}");
                        _smsService.SendToSms(br.Mobile, sb.ToString());
                    }
                }
                // Send short meeting link to front end 
                br.InviteLink = _residentUrl;
            }
            return (new JsonResult(br));
        }
        catch (Exception ex)
        {
            br.FromException(ex);
            return (new NotFoundObjectResult(br));
        }
    }
}

