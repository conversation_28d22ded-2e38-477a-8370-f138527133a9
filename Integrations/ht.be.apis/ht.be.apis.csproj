﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ApplicationInsightsResourceId>/subscriptions/e186ad57-92e6-41b0-92d9-a891425405fb/resourceGroups/rg-ht-uat/providers/microsoft.insights/components/ht-apis-appinsights-uat</ApplicationInsightsResourceId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DefineConstants>DEBUG;TRACE</DefineConstants>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Utils\BlobHelper.cs" />
	</ItemGroup>

	<!--<ItemGroup>
    <OpenApiReference Include="..\..\..\..\..\Users\mick\Downloads\microsoft-rs-SSRS-2.0-swagger.json" CodeGenerator="NSwagCSharp" Namespace="ht.services.ssrs" ClassName="SsrsManager" Link="OpenAPIs\microsoft-rs-SSRS-2.0-swagger.json" />
  </ItemGroup>-->

	<ItemGroup>
		<PackageReference Include="Azure.Communication.CallingServer" Version="1.0.0-beta.3" />
		<PackageReference Include="Azure.Communication.Chat" Version="1.1.2" />
		<PackageReference Include="Azure.Communication.Identity" Version="1.2.0" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.2.2" />
		<PackageReference Include="Azure.Identity" Version="1.13.2" />
		<PackageReference Include="GemBox.Document" Version="35.0.1488-hotfix" />
		<PackageReference Include="Hl7.Fhir.R4" Version="5.5.1" />
		
		<PackageReference Include="ht.monitoring" Version="1.0.2-230125044117" />
    <PackageReference Include="HttpMultipartParser" Version="9.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.DependencyCollector" Version="2.21.0" />
    <PackageReference Include="Microsoft.Azure.Devices" Version="1.39.0" />
    <PackageReference Include="Microsoft.Extensions.ApiDescription.Client" Version="9.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.24" />
    <PackageReference Include="Microsoft.Graph" Version="5.26.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="2.13.4" />
    <PackageReference Include="Microsoft.PowerBI.Api" Version="4.16.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
    <PackageReference Include="Nehta.VendorLibrary.Common" Version="4.3.0" />
    <PackageReference Include="Nehta.VendorLibrary.HI" Version="1.8.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NSwag.ApiDescription.Client" Version="14.2.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="QuickChart" Version="2.3.0" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.ServiceModel.Http" Version="6.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.3" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
    <PackageReference Include="HarfBuzzSharp" Version="8.3.1-preview.2.3" />
    <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" Version="8.3.1-preview.2.3" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="6.1.0" />
    <PackageReference Include="ht.data.common" Version="1.0.2-250617135657" />
  </ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Common\ht.common.backend.models\ht.common.backend.shared.csproj" />
	</ItemGroup>

</Project>
