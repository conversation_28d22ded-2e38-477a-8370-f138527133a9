using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Telehealth;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestClientController : baseTest
    {
        public TestClientController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestDiscovery()
        {
            var content = JsonContent.Create<DiscoveryRequest>(new DiscoveryRequest
            {
                AppName = "HealthTeams",
                Platform = "Web"
            });

            var resp = await _client.PostAsync("/api/client/discovery", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestAuthenticateWithDoc2()
        {
            //currently in the authenticate routine we don't check the expiry of the cert.
            //so this token below is valid.
            var tok = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjQ5MkMxRDdDMzg2QjZCNzg5MEE4QThDNjFGRUIxNEYyRDhGOTVFQUEiLCJ4NXQiOiJTU3dkZkRocmEzaVFxS2pHSC1zVTh0ajVYcW8ifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PdoFLyJp25RmZ7RCwwcvNLXyldzw_E7s2t8JB6tQrmAFt9IhvvOieCAyCvqfWe0cgAsn8oZAQcCStFFU3zA195evgKhuUtXp6og_iTSl5tOevAWzKVNL_qEyT5anRg3jDwSpQZICg-fcqS1ZeFUijVqZZkcfZPs-mdGSlopBvjXvhRY8CBnOcsdOzXLSdocUNTI1retIEcVxnTWyH6lUKe_x3OYNlJMKVcxW1NGUWXDbzn-cXzHjF7xHCm7erp-90q-7bYvZXLd1PS2D7rdFemaVrbIXMvbBf6goUcharaLtbN0Ifk8JRVegTxroDvc6WiYG7FVMzYfcgm_efE8-jg";

            var content = JsonContent.Create<LoginRequest>(new LoginRequest());

            _client.DefaultRequestHeaders.Add("Authentication", $"Bearer {tok}");
            var resp = await _client.PostAsync("/api/client/authenticate", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }


        [Fact]
        public async Task TestUpsertMonitoringData()
        {
            TelehealthMonitorData t = new TelehealthMonitorData();
            var content = JsonContent.Create<UpsertMonitoringDataRequest>(new UpsertMonitoringDataRequest
            {
                //  TelehealthMonitorData = null

            });

            var resp = await _client.PostAsync("/api/TeleHealth/UpsertMonitoringData", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}