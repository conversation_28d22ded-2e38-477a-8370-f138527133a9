using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestNotesController : baseTest
    {
        public TestNotesController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetNotes()
        {
            string UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Notes/GetNotes?UserExId=" + UserExId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetNotesDetails()
        {
            string exnoteId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Notes/GetNotesDetails?exnoteId=" + exnoteId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertNotes()
        {
            var content = JsonContent.Create<TelehealthProgressNote>(new TelehealthProgressNote
            {                
                  ExNoteId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                  
                  ApptDateUtc=DateTime.UtcNow,
                  NoteDateUtc=DateTime.UtcNow,
                  Title="",
                  NoteDetails=""       
            });

            var resp = await _client.PostAsync("/api/Monitoringdata/UpsertMonitoringData", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}