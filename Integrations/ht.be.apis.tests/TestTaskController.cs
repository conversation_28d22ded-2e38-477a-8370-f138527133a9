using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Tasks;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestTaskController : baseTest
    {
        public TestTaskController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetTask()
        {
            string taskId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Task/GetTask?taskId=" + taskId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetScriptDetails()
        {
            string scriptId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Scripts/GetScriptDetails?scriptId=" + scriptId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
        

        [Fact]
        public async Task TestGetTasksList()
        {
            var content = JsonContent.Create<GetTaskListRequest>(new GetTaskListRequest
            {
                DateFromUtc = System.DateTime.UtcNow,
                DateToUtc = System.DateTime.UtcNow,                
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExCarerId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExDoctorId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                TaskType="Appointment"
            });

            var resp = await _client.PostAsync("/api/Task/GetTasksList", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertTask()
        {
            var content = JsonContent.Create<UpsertTaskRequest>(new UpsertTaskRequest
            {
                Task = new HealthTeamsTask
                    {
                        AssignedToId = "",
                        TaskType  = TaskTypes.CollectVitals,
                        TaskStatus = "Upcoming",
                        TaskDateTimeUtc = DateTime.UtcNow.AddDays(3),
                        TaskLocalLocale = "AUS Eastern Standard Time",
                        TaskName = "Collect vitals from Mr. Baskings",
                        ExTaskId = "",
                        AssignedToName = "Tristan Burgers",
                        AssignedToUser = new data.common.Users.HealthTeamsUser
                        {
                                Email="<EMAIL>",
                                Role = "Carer",
                                FullName ="Tristan Burgers",
                                Mobile ="**********",
                                PostCode = "2026",
                                UserCreatedUtc = DateTime.UtcNow
                        },
                        ExDoctorId = Guid.NewGuid().ToString(),
                        ExFacilityId = "",
                        ExResidentId = Guid.NewGuid().ToString(),
                        TaskDescription = "Tristan is to Collect Vitals from Mr. Baskings",
                        Resident = new data.common.Users.HealthTeamsUser
                        {
                            Email= "<EMAIL>",
                            Role = "Resident",
                            FullName = "Janice Bauer",
                            Mobile="0411 222 333",
                            PostCode ="2026",
                            ContactMethod="Mobile",
                            VitalsLastCapturedUtc = DateTime.UtcNow.AddDays(-10),
                            Address = new Address
                            {
                                ExAddressId = "01",
                                LastModifiedUtc = DateTime.UtcNow,
                                BuildingName = "Tower byte",
                                Street1 = "56 Arthur Street",
                                City = "Randwick",
                                State = "nsw",
                                PostCode = "2000",
                                Country = "aust",
                                Long = "Unit 06",
                                Lat = "Unit 06",
                                MeshblockId = "01"
                            }
                        },
                        VitalsToCapture = new List<string>
                        {
                            "Blood Pressure","SpO2","Temperature","Glucose","Weight","Heartrate","UTI"
                        }
                    }
            });

            var resp = await _client.PostAsync("/api/Task/UpsertTask", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}