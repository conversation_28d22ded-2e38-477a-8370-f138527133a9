using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestUserController : baseTest
    {
        public TestUserController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetUserDetails()
        {
            string UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";


            var resp = await _client.GetAsync("/api/User/GetUserDetails?UserExId=" + UserExId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetResidents()
        {
            var content = JsonContent.Create<GetResidentRequest>(new GetResidentRequest
            {
                UserExId= "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/User/GetResidents", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}