using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Telehealth;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestTeleHealthController : baseTest
    {
        public TestTeleHealthController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetTeleHealthDetails()
        {
            var content = JsonContent.Create<GetTeleHealthDetailsRequest>(new GetTeleHealthDetailsRequest
            {
               // exMeetingId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Role = "Resident"
            });

            var resp = await _client.PostAsync("/api/TeleHealth/GetTeleHealthDetails", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetReferrals()
        {
            var content = JsonContent.Create<GetReferralRequest>(new GetReferralRequest
            {
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/TeleHealth/GetReferrals", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertNotes()
        {
            var content = JsonContent.Create<TelehealthProgressNote>(new TelehealthProgressNote
            {
                ExNoteId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Title = "Testing note",
                NoteDetails = "this is test note. Please ignore",
                ApptDateUtc = System.DateTime.UtcNow,
                NoteDateUtc = System.DateTime.UtcNow,


            });

            var resp = await _client.PostAsync("/api/TeleHealth/UpsertNotes", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetScripts()
        {
            var content = JsonContent.Create<GetScriptsRequest>(new GetScriptsRequest
            {
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",

            });

            var resp = await _client.PostAsync("/api/TeleHealth/GetScripts", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertBillSession()
        {
            var content = JsonContent.Create<UpsertBillSessionRequest>(new UpsertBillSessionRequest
            {
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                FullName="",
                DateofSessionUTC=System.DateTime.UtcNow,
                TimeofSessionUTC=System.DateTime.UtcNow,
                BillableItems=null,
                Total=100,
                Gap=100,
                ProviderName="Testing",
                ProviderNumber="1000",
                Signature="",
                IsBilled=true,
                SentTo="family"

            });

            var resp = await _client.PostAsync("/api/TeleHealth/UpsertBillSession", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertMonitoringData()
        {
            TelehealthMonitorData t = new TelehealthMonitorData();
            var content = JsonContent.Create<UpsertMonitoringDataRequest>(new UpsertMonitoringDataRequest
            {
              //  TelehealthMonitorData = null

            });

            var resp = await _client.PostAsync("/api/TeleHealth/UpsertMonitoringData", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}