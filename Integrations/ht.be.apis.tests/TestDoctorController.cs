using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestDoctorController : baseTest
    {
        public TestDoctorController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetDashboard()
        {
            var content = JsonContent.Create<GetDoctorDashboardRequest>(new GetDoctorDashboardRequest
            {
                CurrentDate=System.DateTime.UtcNow,
                CurrentEndDate= System.DateTime.UtcNow,
                CurrentMonth= System.DateTime.UtcNow.Month.ToString(),
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                GetTaskDetails = true
            });

            var resp = await _client.PostAsync("/api/Doctor/GetDashboard", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}