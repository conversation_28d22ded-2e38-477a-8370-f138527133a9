using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestBillsessionController : baseTest
    {
        public TestBillsessionController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetBillsession()
        {
            string UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";


            var resp = await _client.GetAsync("/api/Billsession/GetBillsession?UserExId=" + UserExId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetBillsessionDetails()
        {
            string exbillId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";


            var resp = await _client.GetAsync("/api/Billsession/GetBillsessionDetails?exbillId=" + exbillId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
                
        [Fact]
        public async Task TestUpsertBillsession()
        {
            var content = JsonContent.Create<UpsertBillSessionRequest>(new UpsertBillSessionRequest
            {
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                FullName="",
                DateofSessionUTC = DateTime.UtcNow,
                TimeofSessionUTC=DateTime.UtcNow,       
                BillableItems=new List<BillableItem> { },
                Total=123,        
                Gap = 123,
                ProviderName="",
                ProviderNumber = "",
                Signature = "",
                IsBilled =true,
                SentTo = ""
            });

            var resp = await _client.PostAsync("/api/Billsession/UpsertBillsession", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}