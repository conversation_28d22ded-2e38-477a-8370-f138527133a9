using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestReferralController : baseTest
    {
        public TestReferralController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetReferrals()
        {
            string UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Referral/GetReferrals?UserExId=" + UserExId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetReferralDetails()
        {
            string exreferralId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Referral/GetReferralDetails?exreferralId=" + exreferralId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertReferral()
        {
            var content = JsonContent.Create<TelehealthReferral>(new TelehealthReferral
            {
                ExReferralId="",
                Date=DateTime.UtcNow,
                Type="Type of Referral",
                ReferredTo= "Referred To Person"
            });

            var resp = await _client.PostAsync("/api/Referral/UpsertReferral", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}