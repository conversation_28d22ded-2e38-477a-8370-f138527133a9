﻿using ht.be.apis.Services;
using ht.common.backend.shared.helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class baseTest : IClassFixture<WebApplicationFactory<ht.be.apis.Startup>>
    {
        private readonly WebApplicationFactory<ht.be.apis.Startup> _factory;
        public DBService _dbcSvc;
        public TokenService _tokenService;
        public AcsService _acsService;

        public HttpClient _client;
        public baseTest(WebApplicationFactory<ht.be.apis.Startup> factory)
        {
            _factory = factory;
            DBService.DBConstr = "Server=tcp:htnp01.database.windows.net,1433;Initial Catalog=ht_db;Persist Security Info=False;User ID=adminuser;Password=!HealthTeams.123!;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30";
            _client = _factory.CreateClient();
        }

        
        
        
    }
}
