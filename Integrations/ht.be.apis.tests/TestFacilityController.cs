using FluentAssertions;

using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

using Xunit;

namespace ht.be.apis.tests
{
    public class TestFacilityController : baseTest
    {
        public TestFacilityController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetDashboard()
        {
            var content = JsonContent.Create<GetFacilityDashboardRequest>(new GetFacilityDashboardRequest
            {
                CurrentDate = System.DateTime.UtcNow,
                CurrentEndDate = System.DateTime.UtcNow,
                CurrentMonth = System.DateTime.UtcNow.Month.ToString(),
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                ExResidentId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                GetTaskDetails = true
            });

            var resp = await _client.PostAsync("/api/Facility/GetDashboard", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertFacility()
        {
            var content = JsonContent.Create<UpsertFacilityRequest>(new UpsertFacilityRequest
            {
                Facility = new HealthTeamsFacility
                {
                    LastModifiedUtc = System.DateTime.UtcNow.ToString(),
                    FacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    FacilityName = "Facility Name",
                    Description = "Facility Description",
                    Beds = 3,
                    DefaultPharmacy = "Default Pharmacy",
                    DefaultExPharmacyId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    FacilityManager = "Facility Manager",
                    FacilityExManagerId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    ManagerEmail = "<EMAIL>",
                    CountryCode = "62",
                    MobileNo = "**********",
                    ExAddressId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    Address = new Address
                    {
                        ExAddressId = "01",
                        LastModifiedUtc = DateTime.UtcNow,
                        BuildingName = "Tower byte",
                        Street1 = "56 Arthur Street",
                        City = "Randwick",
                        State = "nsw",
                        PostCode = "2000",
                        Country = "aust",
                        Long = "Unit 06",
                        Lat = "Unit 06",
                        MeshblockId = "01"
                    },
                    IsParentFacility = false,
                    ParentFacilityName = "Parent Facility Name",
                    ParentExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    MainPhone = "**********",
                    WebsiteUrl = "https://"
                }
            });

            var resp = await _client.PostAsync("/api/Facility/UpsertFacility", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetFacilities()
        {
            var content = JsonContent.Create<GetFacilitiesRequest>(new GetFacilitiesRequest
            {
                UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Facility/GetFacilities", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetFacility()
        {
            var content = JsonContent.Create<GetFacilityRequest>(new GetFacilityRequest
            {
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Facility/GetFacility", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetFacilityDevices()
        {
            var content = JsonContent.Create<GetFacilityDevicesRequest>(new GetFacilityDevicesRequest
            {
                // FacilityId = 12345,
                ExFacilityId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c"
            });

            var resp = await _client.PostAsync("/api/Facility/GetFacilityDevices", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }
    }
}