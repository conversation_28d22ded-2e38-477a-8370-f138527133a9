using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestMonitoringdataController : baseTest
    {
        public TestMonitoringdataController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetMonitoringData()
        {
            string userId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Monitoringdata/GetMonitoringData?userId=" + userId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertMonitoringData()
        {
            var content = JsonContent.Create<UpsertMonitoringDataRequest>(new UpsertMonitoringDataRequest
            {
                Data = new TelehealthMonitorData
                {
                    UserExId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                    DateCreatedUtc =DateTime.UtcNow,
                    DateModifiedUtc=DateTime.UtcNow,
                    Readings=new List<TelehealthDataItem>{
                        new TelehealthDataItem
                        {
                            UserExId="98125",
                            ReadDateUtc=DateTime.UtcNow.AddHours(2),
                            ItemType  =MonitorType.BPDiastolic,
                            ReadValue ="71",
                            ReadUnits="",
                            ReadDesc="",
                            AssessColour="Red",
                            ReadDeviceId="",
                            ReadLat="",
                            ReadLon =""
                        },
                        new TelehealthDataItem
                        {
                            UserExId="98125",
                            ReadDateUtc=DateTime.UtcNow.AddHours(2),
                            ItemType  =MonitorType.SpO2,
                            ReadValue ="90",
                            ReadUnits="",
                            ReadDesc="",
                            AssessColour="",
                            ReadDeviceId="",
                            ReadLat="",
                            ReadLon =""
                        }
                    },
                    Thresholds=new MonitoringThresholds{
                    
                    }
                }
            });

            var resp = await _client.PostAsync("/api/Monitoringdata/UpsertMonitoringData", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}