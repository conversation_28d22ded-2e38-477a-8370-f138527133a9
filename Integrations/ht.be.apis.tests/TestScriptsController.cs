using FluentAssertions;
using ht.be.apis.models;
using ht.data.common.Dashboards;
using ht.data.common.FacilitySetup;
using ht.data.common.Shared;
using ht.data.common.Telehealth;
using ht.data.common.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace ht.be.apis.tests
{
    public class TestScriptsController : baseTest
    {
        public TestScriptsController(WebApplicationFactory<Startup> factory) : base(factory)
        {
        }

        [Fact]
        public async Task TestGetScripts()
        {
            string userId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Scripts/GetScripts?userId=" + userId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestGetScriptDetails()
        {
            string scriptId = "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c";

            var resp = await _client.GetAsync("/api/Scripts/GetScriptDetails?scriptId=" + scriptId);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

        [Fact]
        public async Task TestUpsertScripts()
        {
            var content = JsonContent.Create<TelehealthScript>(new TelehealthScript
            {
                ExScriptId= "abcf2d0e-7a69-4cc3-9c58-5849fbe1769c",
                Prescriptions= new List<PrescriptionItem>
                {
                    new PrescriptionItem{
                        Item ="Roximosfan",
                        Strength="50ug",
                        Frequency="Daily",
                        Duration="1 week",
                        Repeats="1",
                        Instructions="With metals"                    
                    },
                    new PrescriptionItem
                    {
                        Item ="Dexoradison",
                        Strength="100ug",
                        Frequency="Twice Daily",
                        Duration="1 week",
                        Repeats="0",
                        Instructions=""
                    }
                },
                ExpiryUtc=DateTime.UtcNow
       
            });

            var resp = await _client.PostAsync("/api/Scripts/UpsertScripts", content);
            resp.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            Trace.Write(await resp.Content.ReadAsStringAsync());
        }

    }
}